import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';

class QuillTextEditingAdapter extends TextEditingController {
  QuillTextEditingAdapter(this.quill, this.onRemoteChange) {
    _listener = () {
      if (_selfChange) return;
      _remote = true;

      text = quill.document.toPlainText();
      final docLength = text.length;
      final safeOffset = quill.selection.baseOffset.clamp(0, docLength);

      value = value.copyWith(
        text: text,
        selection: TextSelection.collapsed(offset: safeOffset),
      );
      _remote = false;
      onRemoteChange();
    };

    quill.addListener(_listener);

    // Initial sync
    text = quill.document.toPlainText();
    final docLength = text.length;
    final safeOffset = quill.selection.baseOffset.clamp(0, docLength);
    selection = TextSelection.collapsed(offset: safeOffset);
  }

  final QuillController quill;
  final VoidCallback onRemoteChange;
  late final VoidCallback _listener;
  bool _selfChange = false;
  bool _remote = false;

  @override
  void dispose() {
    quill.removeListener(_listener);
    super.dispose();
  }

  @override
  set value(TextEditingValue newValue) {
    if (_remote) {
      super.value = newValue;
      return;
    }

    final old = quill.document.toPlainText();
    final diff = _firstDiff(old, newValue.text);
    if (diff == null) return;

    final safeOffset = newValue.selection.baseOffset.clamp(0, newValue.text.length);
    final safeSelection = TextSelection.collapsed(offset: safeOffset);

    _selfChange = true;
    // quill.replaceText(
    //   diff.offset,
    //   diff.deleted,
    //   diff.inserted,
    //   safeSelection,
    // );
    _selfChange = false;

    super.value = newValue.copyWith(selection: safeSelection);
  }

  static _Diff? _firstDiff(String a, String b) {
    if (a == b) return null;
    int start = 0;
    while (start < a.length && start < b.length && a[start] == b[start]) {
      start++;
    }
    int endA = a.length - 1;
    int endB = b.length - 1;
    while (endA >= start && endB >= start && a[endA] == b[endB]) {
      endA--;
      endB--;
    }
    final deleted = endA - start + 1;
    final inserted = b.substring(start, endB + 1);
    return _Diff(start, deleted, inserted);
  }
}

class _Diff {
  _Diff(this.offset, this.deleted, this.inserted);
  final int offset;
  final int deleted;
  final String inserted;
}
