include: package:very_good_analysis/analysis_options.yaml

linter:
  rules:
    public_member_api_docs: false
    lines_longer_than_80_chars: false
    sort_pub_dependencies: false

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.config.dart"
    - "lib/l10n/generated/intl/*.dart"
    - "lib/l10n/generated/l10n.dart"
  strong-mode:
    implicit-dynamic: false
  errors:
    invalid_annotation_target: ignore