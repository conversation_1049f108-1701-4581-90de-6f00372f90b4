<!DOCTYPE html>
<html>

<head>

  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="TYNT">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Google Sign-in -->
  <meta name="google-signin-client_id"
    content="635713110257-9tm6e2n2pm9rknkvj0h5fqd0mdamo79k.apps.googleusercontent.com">

  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="favicon.ico">

  <title>TYNT</title>
  <link rel="manifest" href="manifest.json">

  <script src="firebase-auth.js"></script>

  <script>
    // Your Firebase configuration
    const firebaseConfig = {
      apiKey: "AIzaSyC2rrw3DkUUCI9ktvi6bCDo9nzQx6uGq58",
      authDomain: "taletalk-f154f-c5e32.firebaseapp.com",
      projectId: "taletalk-f154f",
      storageBucket: "taletalk-f154f.firebasestorage.app",
      messagingSenderId: "635713110257",
      appId: "1:635713110257:web:1d6ff0ddf7f3fb84df6950",
      measurementId: "G-10P03SZV6K"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);
  </script>
</head>

<body>
  <script src="flutter_bootstrap.js" async></script>
</body>

</html>