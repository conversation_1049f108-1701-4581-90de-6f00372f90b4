name: tynt_web
description: "A new Flutter project."

publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: ">=3.3.4 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter

  cupertino_icons: ^1.0.6
  flutter_bloc: ^9.1.1
  get_it: ^8.0.3
  injectable: ^2.5.0
  cloud_firestore: ^5.6.11
  shared_preferences: ^2.5.3
  connectivity_plus: ^6.1.4
  go_router: ^16.0.0
  intl: ^0.20.2
  gap: ^3.0.1
  google_fonts: ^6.2.1
  skeletonizer: ^2.1.0+1
  flutter_svg: ^2.2.0
  responsive_builder: ^0.7.1
  fpdart: ^1.1.1
  flutter_image_compress: ^2.4.0
  talker_logger: ^4.9.2
  flutter_gen: ^5.11.0
  bot_toast: ^4.1.3
  cached_network_image: ^3.4.1
  shimmer: ^3.0.0
  flutter_quill: ^11.4.2
  image_picker: ^1.1.2
  flutter_widget_from_html: ^0.17.0
  flutter_tts: ^4.2.3
  vsc_quill_delta_to_html: ^1.0.5
  timeago: ^3.7.1
  dotted_line: ^3.2.3
  lottie: ^3.3.1
  equatable: ^2.0.7
  bloc: ^9.0.0
  animate_do: ^4.2.0
  dotted_border: ^3.1.0
  fluttertoast: ^8.2.12
  rxdart: ^0.28.0
  multi_trigger_autocomplete: 
    path: ./multi_trigger_autocomplete-1.0.0
  dropdown_search: ^6.0.2
  qr_flutter: ^4.1.0
  share_plus: ^11.0.0
  firebase_messaging: ^15.2.10
  flutter_local_notifications: ^19.4.0
  pretty_qr_code: ^3.5.0
  google_sign_in: ^7.1.1
  

dev_dependencies:
  build_runner: ^2.5.0
  injectable_generator: ^2.7.0
  very_good_analysis: ^9.0.0

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/svgs/
    - assets/lotties/
    - assets/svgs/bottom_bar_icons/
    - assets/svgs/contact_us_icons/

  fonts:
    - family: SFProDisplay Semibold
      fonts:
        - asset: assets/fonts/SFProDisplay-Semibold.ttf

    - family: SFProDisplay Regular
      fonts:
        - asset: assets/fonts/SFProDisplay-Regular.ttf

    - family: SFProDisplay Medium
      fonts:
        - asset: assets/fonts/SFProDisplay-Medium.ttf

    - family: SFProDisplay Bold
      fonts:
        - asset: assets/fonts/SFProDisplay-Bold.ttf

    - family: SFProDisplay Heavy
      fonts:
        - asset: assets/fonts/SFProDisplay-Heavy.ttf

flutter_intl:
  enabled: true
  class_name: I10n
  main_locale: en
  arb_dir: lib/l10n/arb
  output_dir: lib/l10n/generated
