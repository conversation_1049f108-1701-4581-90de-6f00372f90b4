import 'package:equatable/equatable.dart';
import 'package:tynt_web/auth/models/country_model.dart';

class CountryListResponse extends Equatable {
  const CountryListResponse({
    this.countries = const [],
    this.message = '',
    this.status = '0',
  });

  factory CountryListResponse.fromJson(Map<String, dynamic> json) {
    return CountryListResponse(
      countries:
          (json['data'] as List<dynamic>?)?.map((e) => CountryModel.fromJson(e as Map<String, dynamic>)).toList() ?? [],
      message: json['message'] as String? ?? '',
      status: json['status'] as String? ?? '0',
    );
  }
  final List<CountryModel> countries;
  final String message;
  final String status;

  Map<String, dynamic> toJson() {
    return {
      'data': countries.map((e) => e.toJson()).toList(),
      'message': message,
      'status': status,
    };
  }

  @override
  List<Object?> get props => [countries, message, status];

  @override
  String toString() {
    return 'UsersListResponse{countries: $countries, message: $message, status: $status}';
  }
}
