import 'package:equatable/equatable.dart';
import 'package:tynt_web/auth/models/user_model.dart';

class UsersListResponse extends Equatable {
  const UsersListResponse({
    this.users = const [],
    this.message = '',
    this.status = '0',
  });

  factory UsersListResponse.fromJson(Map<String, dynamic> json) {
    return UsersListResponse(
      users: (json['data'] as List<dynamic>?)?.map((e) => UserModel.fromJson(e as Map<String, dynamic>)).toList() ?? [],
      message: json['message'] as String? ?? '',
      status: json['status'] as String? ?? '0',
    );
  }
  final List<UserModel> users;
  final String message;
  final String status;

  Map<String, dynamic> toJson() {
    return {
      'data': users.map((e) => e.toJson()).toList(),
      'message': message,
      'status': status,
    };
  }

  @override
  List<Object?> get props => [users, message, status];

  @override
  String toString() {
    return 'UsersListResponse{users: $users, message: $message, status: $status}';
  }
}
