import 'package:equatable/equatable.dart';
import 'package:tynt_web/auth/models/avatar_model.dart';

class AvatarListResponse extends Equatable {
  const AvatarListResponse({
    this.avatarList = const [],
    this.message = '',
    this.status = '0',
  });

  factory AvatarListResponse.fromJson(Map<String, dynamic> json) {
    return AvatarListResponse(
      avatarList:
          (json['data'] as List<dynamic>?)?.map((e) => AvatarModel.fromJson(e as Map<String, dynamic>)).toList() ?? [],
      message: json['message'] as String? ?? '',
      status: json['status'] as String? ?? '0',
    );
  }
  final List<AvatarModel> avatarList;
  final String message;
  final String status;

  Map<String, dynamic> toJson() {
    return {
      'data': avatarList.map((e) => e.toJson()).toList(),
      'message': message,
      'status': status,
    };
  }

  @override
  List<Object?> get props => [avatarList, message, status];

  @override
  String toString() {
    return 'AvatarListResponse{data: $avatarList, message: $message, status: $status}';
  }
}
