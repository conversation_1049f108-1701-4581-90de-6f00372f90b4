import 'package:equatable/equatable.dart';
import 'package:tynt_web/auth/models/user_model.dart';

class UserResponse extends Equatable {
  const UserResponse({
    required this.id,
    this.publicUserId,
    this.pseudoUserId,
    this.publicUser,
    this.pseudoUser,
    this.isTermsAccepted,
  });

  factory UserResponse.fromJson(Map<String, dynamic> json) {
    return UserResponse(
      id: json['id'] as int,
      publicUserId: json['public_user_id'] as int?,
      pseudoUserId: json['pseudo_user_id'] as int?,
      publicUser: json['public_user'] == null ? null : UserModel.fromJson(json['public_user'] as Map<String, dynamic>),
      pseudoUser: json['pseudo_user'] == null ? null : UserModel.fromJson(json['pseudo_user'] as Map<String, dynamic>),
      isTermsAccepted: json['is_terms_accepted'] as int?,
    );
  }
  final int? id;
  final int? publicUserId;
  final int? pseudoUserId;
  final UserModel? publicUser;
  final UserModel? pseudoUser;
  final int? isTermsAccepted;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'public_user_id': publicUserId,
      'pseudo_user_id': pseudoUserId,
      'public_user': publicUser?.toJson(),
      'pseudo_user': pseudoUser?.toJson(),
      'is_terms_accepted': isTermsAccepted,
    };
  }

  @override
  List<Object?> get props => [
        id,
        publicUserId,
        pseudoUserId,
        publicUser,
        pseudoUser,
        isTermsAccepted,
      ];

  @override
  String toString() {
    return 'UserResponse{id: $id, publicUserId: $publicUserId, pseudoUserId: $pseudoUserId, publicUser: $publicUser, pseudoUser: $pseudoUser, isTermsAccepted: $isTermsAccepted}';
  }

  static const empty = UserResponse(id: 0);

  bool isOtherUser(int userId) {
    return userId != publicUserId && userId != pseudoUserId;
  }

  bool isOtherUserByUserName(String userName) {
    return publicUser?.userName != userName && pseudoUser?.userName != userName;
  }
}
