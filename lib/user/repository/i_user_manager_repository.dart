import 'dart:typed_data';

import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:tynt_web/auth/response/login_response.dart';
import 'package:tynt_web/auth/response/string_response.dart';
import 'package:tynt_web/constants/app_strings.dart';
import 'package:tynt_web/user/response/avatar_list_response.dart';
import 'package:tynt_web/user/response/country_list_response.dart';
import 'package:tynt_web/user/response/user_list_response.dart';
import 'package:tynt_web/utlity/app_typedefs/http_typedef.dart';
import 'package:tynt_web/utlity/extentions/string_extentions.dart';
import 'package:tynt_web/utlity/failure/http_failure.dart';
import 'package:tynt_web/utlity/network/client.dart';

part 'user_manager_repository.dart';

abstract class IUserManagerRepository {
  IUserManagerRepository(this.client);
  final Client client;

  ApiResult<LoginResponse> getUserResponse();

  ApiResult<UsersListResponse> getUsersList({
    int page = 1,
    int perPage = 10,
    String? search,
  });

  ApiResult<AvatarListResponse> getAvatars({
    int page = 1,
    int perPage = 10,
  });

  ApiResult<StringResponse> generatePseudoName({
    String? firstName,
    String? lastName,
  });

  ApiResult<LoginResponse> updateUser({
    String? firstName,
    String? lastName,
    String? phoneCode,
    String? phoneNumber,
    String? email,
    String? bio,
    int? avatarId,
    int? pseudoAvatarId,
    String? tyntName,
    String? passcode,
    String? state,
    List<int> genresList = const <int>[],
    Uint8List? profileImage,
    Uint8List? pseudoProfileImage,
    String? gender,
    String? userName,
    int? countryId,
    int? isBusiness,
    int? syncUserName,
  });

  ApiResult<CountryListResponse> getCountriesList({
    int page = 1,
    int perPage = 10,
    String? search,
  });
}
