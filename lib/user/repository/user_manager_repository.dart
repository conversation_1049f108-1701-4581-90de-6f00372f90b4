part of 'i_user_manager_repository.dart';

@Injectable(as: IUserManagerRepository)
class UserManagerRepository extends IUserManagerRepository {
  UserManagerRepository(super.client);

  @override
  ApiResult<LoginResponse> getUserResponse() async {
    final response = await client.get(url: AppStrings.userResponse);

    return response.fold(
      left,
      (r) => Either.tryCatch(() => LoginResponse.fromJson(r), (o, s) => HttpFailure.parsing(o.toString(), 500, s)),
    );
  }

  @override
  ApiResult<UsersListResponse> getUsersList({int page = 1, int perPage = 10, String? search}) async {
    final response = await client.get(
      url: AppStrings.users,
      params: <String, dynamic>{
        'page': '$page',
        'per_page': '$perPage',
        if (search != null && search.trim().isNotEmpty) 'search': search.trim(),
      },
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(() => UsersListResponse.fromJson(r), (o, s) => HttpFailure.parsing(o.toString(), 500, s)),
    );
  }

  @override
  ApiResult<AvatarListResponse> getAvatars({int page = 1, int perPage = 10}) async {
    final response = await client.get(
      url: AppStrings.avatars,
      params: <String, dynamic>{
        'page': '$page',
        'per_page': '$perPage',
      },
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(() => AvatarListResponse.fromJson(r), (o, s) => HttpFailure.parsing(o.toString(), 500, s)),
    );
  }

  @override
  ApiResult<StringResponse> generatePseudoName({
    String? firstName,
    String? lastName,
  }) async {
    final response = await client.post(
      url: AppStrings.generatePseudoName,
      requests: <String, dynamic>{
        if (firstName.isPureValid) 'first_name': firstName!.trim(),
        if (lastName.isPureValid) 'last_name': lastName!.trim(),
      },
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(() => StringResponse.fromJson(r), (o, s) => HttpFailure.parsing(o.toString(), 500, s)),
    );
  }

  @override
  ApiResult<LoginResponse> updateUser({
    String? firstName,
    String? lastName,
    String? phoneCode,
    String? phoneNumber,
    int? avatarId,
    String? email,
    String? bio,
    int? pseudoAvatarId,
    String? tyntName,
    String? passcode,
    List<int> genresList = const <int>[],
    Uint8List? profileImage,
    Uint8List? pseudoProfileImage,
    String? state,
    String? gender,
    int? countryId,
    int? isBusiness,
    String? userName,
    int? syncUserName,
  }) async {
    final response = await client.multipart(
      url: AppStrings.userUpdate,
      webFiles: [
        if (profileImage != null) MapEntry('profile_image', profileImage),
        if (pseudoProfileImage != null) MapEntry('pseudo_profile_image', pseudoProfileImage),
      ],
      requests: <String, String>{
        if (firstName.isPureValid) 'first_name': firstName!.trim(),
        if (email.isPureValid) 'email': email!.trim(),
        'bio': bio.isPureValid ? bio!.trim() : '',
        if (lastName.isPureValid) 'last_name': lastName!.trim(),
        if (phoneCode.isPureValid) 'phone_code': phoneCode!.trim(),
        if (phoneNumber.isPureValid) 'phone_number': phoneNumber!.trim(),
        if (tyntName.isPureValid) 'tynt_name': tyntName!.trim(),
        if (passcode.isPureValid) 'passcode': passcode!.trim(),
        if (state.isPureValid) 'state': state!.trim(),
        if (gender.isPureValid) 'gender': gender!,
        if (userName.isPureValid) 'user_name': userName!,
        if (isBusiness != null) 'is_business': isBusiness.toString(),
        if (syncUserName != null) 'sync_user_name': syncUserName.toString(),
        if (avatarId != null && avatarId != 0) 'avatar_id': avatarId.toString(),
        if (countryId != null && countryId != 0) 'country_id': countryId.toString(),
        if (pseudoAvatarId != null && pseudoAvatarId != 0) 'pseudo_avatar_id': pseudoAvatarId.toString(),
        if (genresList.isNotEmpty)
          for (var i = 0; i < genresList.length; i++) 'genre_ids[$i]': '${genresList[i]}',
      },
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(() => LoginResponse.fromJson(r), (o, s) => HttpFailure.parsing(o.toString(), 500, s)),
    );
  }

  @override
  ApiResult<CountryListResponse> getCountriesList({
    int page = 1,
    int perPage = 10,
    String? search,
  }) async {
    final response = await client.get(
      url: AppStrings.countriesList,
      params: <String, dynamic>{
        'page': '$page',
        'per_page': '$perPage',
        if (search != null && search.trim().isNotEmpty) 'search': search.trim(),
      },
    );

    return response.fold(
      left,
      (r) =>
          Either.tryCatch(() => CountryListResponse.fromJson(r), (o, s) => HttpFailure.parsing(o.toString(), 500, s)),
    );
  }
}
