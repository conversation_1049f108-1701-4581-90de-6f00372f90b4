import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:lottie/lottie.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/no_internet/cubit/internet_connectivity_cubit.dart';
import 'package:tynt_web/primary_button/utility/primary_button_utlity.dart';
import 'package:tynt_web/primary_button/views/primary_button_view.dart';

class NoInternetPage extends StatefulWidget {
  const NoInternetPage({super.key});

  @override
  State<NoInternetPage> createState() => _NoInternetPageState();
}

class _NoInternetPageState extends State<NoInternetPage> {
  final buttonController = PrimaryLoadingButtonController();

  @override
  void dispose() {
    buttonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<InternetConnectivityCubit, NetworkStatus>(
      listenWhen: (previous, current) => previous != current && current.isConnected,
      listener: (context, state) {
        // Utility.toast(message: context.l10n.connected);
        context.pop();
      },
      child: PopScope(
        canPop: false,
        child: Scaffold(
          body: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 15),
            child: Center(
              child: Lottie.asset(
                AppAssets.noInternetLottieAnimation,
                height: MediaQuery.sizeOf(context).shortestSide * 1.6,
              ),
            ),
          ),
          bottomNavigationBar: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: Platform.isIOS ? 30 : 12),
                child: PrimaryButton(
                  onPressed: checkInternet,
                  primaryLoadingButtonController: buttonController,
                  text: context.l10n.tryAgain,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> checkInternet() async {
    buttonController.start();

    final isConnected = await context.read<InternetConnectivityCubit>().checkInternet();

    if (isConnected) {
      buttonController.success();
      return;
    }
    buttonController.error();
  }
}
