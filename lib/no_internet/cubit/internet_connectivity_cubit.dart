import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:tynt_web/utlity/logger_config.dart';

enum NetworkStatus {
  connected,
  noInternet,
  inActive,
}

@lazySingleton
class InternetConnectivityCubit extends Cubit<NetworkStatus> with WidgetsBindingObserver {
  InternetConnectivityCubit() : super(NetworkStatus.connected) {
    subscription = Connectivity().onConnectivityChanged.listen((result) {
      if (!state.isNoInternet && result.isNoInternet) {
        debugError('Internet Disconnected');
        emit(NetworkStatus.noInternet);
        return;
      }
      if (!state.isConnected && result.isConnected) {
        debugLog('Internet Connected');
        emit(NetworkStatus.connected);
      }
    });
  }

  Future<bool> checkInternet() async {
    final result = await Connectivity().checkConnectivity();

    if (!state.isNoInternet && result.isNoInternet) {
      debugError('Internet Disconnected');
      emit(NetworkStatus.noInternet);
      return false;
    }
    if (!state.isConnected && result.isConnected) {
      debugLog('Internet Connected');
      emit(NetworkStatus.connected);
      return true;
    }
    return false;
  }

  late final StreamSubscription<List<ConnectivityResult>> subscription;

  // Be sure to cancel subscription after you are done
  @override
  Future<void> close() async {
    await subscription.cancel();

    await super.close();
  }
}

extension NoInternetExtentions on NetworkStatus {
  bool get isNoInternet => this == NetworkStatus.noInternet;
  bool get isConnected => this == NetworkStatus.connected;
}

extension ListConnectivityResultExtentions on List<ConnectivityResult> {
  bool get isNoInternet =>
      contains(ConnectivityResult.none) ||
      (contains(ConnectivityResult.bluetooth) && !anyConnection) ||
      (contains(ConnectivityResult.vpn) && !anyConnection);

  bool get anyConnection => any(
        (element) =>
            element == ConnectivityResult.mobile ||
            element == ConnectivityResult.wifi ||
            element == ConnectivityResult.ethernet,
      );

  bool get isConnected => anyConnection || (contains(ConnectivityResult.vpn) && anyConnection);
}
