import 'package:equatable/equatable.dart';
import 'package:tynt_web/constants/app_strings.dart';

class TomesModel extends Equatable {
  const TomesModel({
    this.id,
    this.userId,
    this.postsCount,
    this.image,
    this.name,
    this.description,
    this.createdAt,
    this.updatedAt,
  });

  factory TomesModel.fromJson(Map<String, dynamic> json) {
    return TomesModel(
      id: json['id'] as int?,
      userId: json['user_id'] as int?,
      postsCount: json['posts_count'] as int?,
      image: json['image'] as String?,
      name: json['name'] as String?,
      description: json['description'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );
  }
  final int? id;
  final int? userId;
  final int? postsCount;
  final String? image;
  final String? name;
  final String? description;
  final String? createdAt;
  final String? updatedAt;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'posts_count': postsCount,
      'image': image,
      'name': name,
      'description': description,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        postsCount,
        image,
        name,
        description,
        createdAt,
        updatedAt,
      ];

  String? get imageUrl => image == null ? null : '${AppStrings.storageUrl}/$image';

  bool get isDescriptionTooLong => (description?.length ?? 0) > 120;

  @override
  String toString() {
    return 'TomesModel{id: $id, userId: $userId, postsCount: $postsCount, image: $image, name: $name, description: $description, createdAt: $createdAt, updatedAt: $updatedAt}';
  }
}
