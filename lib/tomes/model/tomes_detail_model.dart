import 'package:equatable/equatable.dart';
import 'package:tynt_web/post_preview/model/post_model.dart';
import 'package:tynt_web/tomes/model/tomes_model.dart';

class TomesDetailsModel extends Equatable {
  const TomesDetailsModel({
    this.tomes,
    this.posts = const <PostModel>[],
  });

  factory TomesDetailsModel.fromJson(Map<String, dynamic> json) {
    return TomesDetailsModel(
      tomes: json['tome'] != null ? TomesModel.fromJson(json['tome'] as Map<String, dynamic>) : null,
      posts: (json['posts'] as List<dynamic>?)?.map((e) => PostModel.fromJson(e as Map<String, dynamic>)).toList() ??
          const [],
    );
  }

  final TomesModel? tomes;
  final List<PostModel> posts;

  Map<String, dynamic> toJson() {
    return {
      'tome': tomes?.toJson(),
      'posts': posts.map((e) => e.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [tomes, posts];

  @override
  String toString() {
    return 'TomesDetailsModel{tomes: $tomes, posts: $posts}';
  }
}
