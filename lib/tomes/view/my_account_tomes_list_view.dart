// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:share_plus/share_plus.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/constants/app_strings.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/primary_button/utility/primary_button_utlity.dart';
import 'package:tynt_web/routes/app_routes.dart';
import 'package:tynt_web/tomes/model/tomes_model.dart';
import 'package:tynt_web/tomes/repository/i_tomes_repository.dart';
import 'package:tynt_web/tomes/widget/toms_view_widget.dart';
import 'package:tynt_web/user_profile/bloc/my_account_bloc.dart';
import 'package:tynt_web/user_profile/widget/create_post_widget.dart';
import 'package:tynt_web/user_profile/widget/user_profile_max_width_wapper.dart';
import 'package:tynt_web/utlity/utlity.dart';
import 'package:tynt_web/widgets/dailog_boxes.dart';

final List<TomesModel> tomeList = [
  const TomesModel(
    id: 1,
    name: 'Tome 1',
    image: 'https://gratisography.com/wp-content/uploads/2024/11/gratisography-augmented-reality-800x525.jpg',
    description: 'description 1 ',
  ),
  const TomesModel(
    id: 1,
    name: 'Tome 1',
    image: 'https://gratisography.com/wp-content/uploads/2024/11/gratisography-augmented-reality-800x525.jpg',
    description: 'description 1 ',
  ),
  const TomesModel(
    id: 1,
    name: 'Tome 1',
    image: 'https://gratisography.com/wp-content/uploads/2024/11/gratisography-augmented-reality-800x525.jpg',
    description: 'description 1 ',
  ),
];

class MyAccountTomesListView extends StatefulWidget {
  const MyAccountTomesListView({
    required this.tomes,
    super.key,
  });
  final List<TomesModel> tomes;

  @override
  State<MyAccountTomesListView> createState() => _MyAccountTomesListViewState();
}

class _MyAccountTomesListViewState extends State<MyAccountTomesListView> {
  final _isLoading = PrimaryLoadingButtonController();

  @override
  Widget build(BuildContext context) {
    return UserProfileMaxWidthWapper(
      child: Container(
        constraints: BoxConstraints(maxHeight: context.isMobile ? 120 : MediaQuery.of(context).size.width / 2),
        height: 120,
        child: ListView(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.only(left: 16, right: 16),
          clipBehavior: Clip.none,
          children: [
            CreateTomesWidget(
              onTap: () {
                DailogBoxes.openCreateTomDailog(context, onCreateTomTap: (tom) {
                  context.read<MyAccountBloc>().add(MyAccountAddTome(tome: tom));
                });
              },
            ),
            ...List.generate(widget.tomes.length, (index) {
              final tome = widget.tomes[index];
              return Padding(
                padding: const EdgeInsets.only(left: 14),
                child: TomsViewWidget(
                  tomes: tome,
                  onTap: () {
                    context.goNamed(AppRoutes.tomePost.name, pathParameters: {'id': tome.id.toString()});
                  },
                  onShareTap: () {
                    final sharedLink = Uri.parse(AppStrings.tomeShareLink(tome.id!));
                    DailogBoxes.openShareBottomSheet(
                      context,
                      shareLink: sharedLink,
                      onShareTap: () async {
                        await SharePlus.instance.share(ShareParams(uri: sharedLink));
                      },
                    );
                  },
                  onEditTap: () {
                    DailogBoxes.openCreateTomDailog(context, onCreateTomTap: (tom) {
                      context.read<MyAccountBloc>().add(MyAccountEditTome(tome: tom));
                    }, isEdit: true, tomesModel: tome);
                  },
                  onDeleteTap: () {
                    DailogBoxes.deleteTomesDailog(context, isLoading: _isLoading, onDeleteTomesTap: () {
                      deleteTome(context, tome.id ?? 0);
                    });
                  },
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Future<void> deleteTome(BuildContext context, int tomesId) async {
    _isLoading.start();
    final response = await getIt<ITomesRepository>().deleteTomes(tomesId);

    await response.fold(
      (error) {
        if (context.mounted) {
          Utility.toast(message: error.message);
          _isLoading.stop();
        }
      },
      (response) async {
        if (response.status == '1') {
          final deletedTome = TomesModel(id: tomesId);
          context.read<MyAccountBloc>().add(MyAccountDeleteTome(tome: deletedTome));
          _isLoading.stop();
          context.pop();
        }
        if (context.mounted) {
          Utility.toast(message: response.message);
          _isLoading.stop();
        }
      },
    );
  }
}

class MyAccountTomesLoadingListView extends StatelessWidget {
  const MyAccountTomesLoadingListView({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      containersColor: AppColors.softPeach,
      child: UserProfileMaxWidthWapper(
        child: Container(
          height: 120,
          constraints: BoxConstraints(maxHeight: context.isMobile ? 0 : MediaQuery.of(context).size.width / 2),
          child: ListView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.only(left: 16, right: 16),
            clipBehavior: Clip.none,
            children: [
              CreateTomesWidget(
                onTap: () {
                  log('object');
                  DailogBoxes.openCreateTomDailog(context, onCreateTomTap: (tom) {});
                },
              ),
              ...List.generate(tomeList.length, (index) {
                final tome = tomeList[index];
                return Padding(
                  padding: const EdgeInsets.only(left: 14),
                  child: TomsViewWidget(
                    tomes: tome,
                    onTap: () {
                      context.goNamed(AppRoutes.tomePost.name);
                    },
                    onShareTap: () {},
                    onEditTap: () {
                      DailogBoxes.openCreateTomDailog(context,
                          onCreateTomTap: (tom) {}, isEdit: true, tomesModel: tome);
                    },
                    onDeleteTap: () {
                      log('object delete');
                      // DailogBoxes.deleteTomesDailog(context, onDeleteTomesTap: () {
                      //   context.read<MyAccountBloc>().add(MyAccountDeleteTome(tome: tome));
                      // });
                    },
                  ),
                );
              }),
            ],
          ),
        ),
      ),
    );
  }
}
