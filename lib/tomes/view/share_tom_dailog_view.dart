import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'package:share_plus/share_plus.dart';
import 'package:tynt_web/auth/cubit/theme_cubit.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/constants/app_theme.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/primary_button/views/primary_button_view.dart';
import 'package:tynt_web/utlity/enum/user_type_enum.dart';
import 'package:tynt_web/utlity/utlity.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/popup_wrapper.dart';

class ShareTomBottomView extends StatefulWidget {
  const ShareTomBottomView({
    super.key,
    this.onShareTap,
    this.shareLink,
  });

  final void Function()? onShareTap;
  final Uri? shareLink;

  @override
  State<ShareTomBottomView> createState() => _ShareTomBottomViewState();
}

class _ShareTomBottomViewState extends State<ShareTomBottomView> {
  late final String _shareUrl;

  @override
  void initState() {
    super.initState();
    _shareUrl = widget.shareLink?.toString() ?? '';
    log('$_shareUrl Share URL');
  }

  void _copyToClipboard() {
    Clipboard.setData(ClipboardData(text: _shareUrl));
    Utility.toast(message: 'Link copied to clipboard');
    Navigator.pop(context);
  }

  void _shareLink() {
    SharePlus.instance.share(ShareParams(uri: Uri.parse(_shareUrl)));
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return PopUpWrapper(
      childrenPadding: const EdgeInsets.fromLTRB(20, 20, 20, 30),
      showCancelButton: true,
      children: [
        BlocBuilder<ThemeCubit, ThemeState>(
          builder: (context, state) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Share this tome',
                  style: context.textTheme.bodyLarge?.copyWith(
                    fontSize: context.isMobile ? 24 : 28,
                    fontWeight: FontWeight.w700,
                    fontFamily: AppTheme.spaceGroteskFontFamily,
                    color: state.themeMode == ThemeMode.dark && state.currentUserType == UserType.normal
                        ? AppColors.headingTextDark
                        : AppColors.text,
                  ),
                ),
                const Gap(10),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.black,
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // White QR area
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: SizedBox(
                          width: 180, // QR size
                          height: 180,
                          child: PrettyQrView.data(
                            data: _shareUrl,
                            errorCorrectLevel: QrErrorCorrectLevel.H,
                            decoration: const PrettyQrDecoration(
                              shape: PrettyQrSmoothSymbol(
                                roundFactor: 0,
                                color: AppColors.primary,
                              ),
                              image: PrettyQrDecorationImage(
                                image: AssetImage(
                                  AppAssets.appLogo1,
                                ),
                              ),
                              // quietZone: PrettyQrQuietZone.modules(3),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'TYNT',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 20,
                          letterSpacing: 1.2,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Expanded(
                      child: PrimaryButton(
                        onPressed: _copyToClipboard,
                        text: 'Copy',
                        icon: const Icon(
                          Icons.copy,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                    const Gap(10),
                    Expanded(
                      child: PrimaryButton(
                        onPressed: _shareLink,
                        text: 'Share',
                        icon: const AppImageAsset(
                          AppAssets.shareIcon,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        ),
      ],
    );
  }
}
