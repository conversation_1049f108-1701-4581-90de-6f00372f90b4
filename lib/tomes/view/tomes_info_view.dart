import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/tomes/model/tomes_model.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/app_image_network.dart';
import 'package:tynt_web/widgets/dailog_boxes.dart';

class TomesInfoView extends StatelessWidget {
  const TomesInfoView({super.key, this.tomes});
  final TomesModel? tomes;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: const BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.all(Radius.circular(12)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 77,
            child: Column(
              children: [
                Skeleton.shade(
                  child: AppImageNetwork(
                    url: tomes?.imageUrl ?? '',
                    loading: AppImageLoading(
                      boxShape: BoxShape.rectangle,
                      height: 77,
                      width: 77,
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    imageBuilder: (context, imageProvider) => Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        image: DecorationImage(image: imageProvider, fit: BoxFit.cover),
                      ),
                    ),
                    customErrorWidget: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: AppColors.transparent,
                      ),
                      clipBehavior: Clip.hardEdge,
                      height: 77,
                      width: 77,
                      child: const AppImageAsset(AppAssets.noImagePlaceholder),
                    ),
                    height: 77,
                    width: 77,
                  ),
                ),
                const Gap(10),
                Text(
                  tomes?.name ?? 'Tomes name',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w500,
                        fontSize: context.isMobile ? 14 : 16,
                      ),
                ),
              ],
            ),
          ),
          const Gap(11),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  tomes?.description ?? 'Tomes description',
                  maxLines: 4,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                      ),
                ),
                if (tomes?.isDescriptionTooLong ?? false) ...[
                  const Gap(11),
                  InkWell(
                    onTap: () {
                      DailogBoxes.openTomesDetailsDailog(context, tomes: tomes);
                    },
                    child: Text(
                      context.l10n.showMore,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w400,
                            fontSize: 14,
                            color: AppColors.highlights2,
                          ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
