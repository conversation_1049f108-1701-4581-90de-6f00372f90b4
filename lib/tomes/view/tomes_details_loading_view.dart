import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/auth/cubit/theme_cubit.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/post_preview/model/post_model.dart';
import 'package:tynt_web/user_profile/widget/post_item_view.dart';
import 'package:tynt_web/utlity/utlity.dart';

class TomesDetailsLoadingView extends StatelessWidget {
  const TomesDetailsLoadingView({
    super.key,
    this.isSliverLoading = false,
    this.padding,
  });
  final bool isSliverLoading;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    if (isSliverLoading) {
      return Skeletonizer.sliver(
        child: SliverPadding(
          padding: padding ?? Utility.listCommonpadding(context),
          sliver: SliverList.separated(
            itemBuilder: (_, index) => const PostItemView(postModel: PostModel(id: 0)),
            separatorBuilder: (_, __) => const Gap(16),
            itemCount: 5,
          ),
        ),
      );
    }
    return Skeletonizer(
      ignoreContainers: false,
      containersColor: AppColors.white,
      effect: ShimmerEffect(
        baseColor: AppColors.shimmerLoadingColor(context.read<ThemeCubit>().state.themeMode),
      ),
      child: ListView.separated(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: padding ?? Utility.listCommonpadding(context),
        itemBuilder: (_, index) => Container(
          height: 100,
          width: double.infinity,
          color: AppColors.shimmerLoadingColor(context.read<ThemeCubit>().state.themeMode),
        ),
        separatorBuilder: (_, __) => const Gap(16),
        itemCount: 5,
      ),
    );
  }
}
