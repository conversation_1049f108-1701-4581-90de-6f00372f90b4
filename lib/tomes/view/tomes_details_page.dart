import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/tomes/bloc/tomes_details_bloc.dart';
import 'package:tynt_web/tomes/view/tomes_details_loading_view.dart';
import 'package:tynt_web/tomes/view/tomes_info_view.dart';
import 'package:tynt_web/user_profile/view/my_account_post_list_view.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/no_data.dart';

class TomesDetailsWrapperPage extends StatelessWidget {
  const TomesDetailsWrapperPage({required this.tomesId, this.isShared = false, super.key});
  final String tomesId;
  final bool isShared;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<TomesDetailsBloc>()..add(LoadTomesPost(tomesId: tomesId)),
      child: isShared
          ? Scaffold(
              body: TomesDetailPage(tomesId: tomesId, isShared: isShared),
            )
          : TomesDetailPage(tomesId: tomesId, isShared: isShared),
    );
  }
}

class TomesDetailPage extends StatefulWidget {
  const TomesDetailPage({super.key, this.tomesId, this.isShared = false});
  final String? tomesId;
  final bool isShared;

  @override
  State<TomesDetailPage> createState() => _TomesDetailPageState();
}

class _TomesDetailPageState extends State<TomesDetailPage> {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(
          maxWidth: 748,
        ),
        child: BlocBuilder<TomesDetailsBloc, TomesDetailsState>(
          builder: (context, state) {
            if (state is TomesDetailsInitial) {
              return const TomesDetailsLoadingView();
            } else if (state is TomesDetailsLoading) {
              return const TomesDetailsLoadingView();
            } else if (state is TomesDetailsLoaded) {
              return CustomScrollView(
                slivers: [
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: AppBar(
                        leadingWidth: widget.isShared ? 0 : 50,
                        titleSpacing: 0,
                        leading: widget.isShared
                            ? const SizedBox()
                            : InkWell(
                                onTap: () {
                                  context.pop();
                                },
                                child: const Padding(
                                  padding: EdgeInsets.fromLTRB(0, 20, 24, 20),
                                  child: AppImageAsset(
                                    AppAssets.backArrowIcon,
                                    height: 24,
                                    width: 24,
                                  ),
                                ),
                              ),
                        title: Text(
                          state.tomes?.name ?? '',
                          style: context.textTheme.bodyMedium?.copyWith(
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                    ),
                  ),
                  SliverToBoxAdapter(
                    child: TomesInfoView(
                      tomes: state.tomes,
                    ),
                  ),
                  const SliverToBoxAdapter(
                    child: Gap(20),
                  ),
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        context.l10n.posts.toUpperCase(),
                        style: context.textTheme.bodyMedium?.copyWith(
                          fontSize: 15,
                          letterSpacing: 0,
                          color: Theme.of(context).textTheme.labelLarge?.color,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  if (state.tomesPosts.isEmpty) const SliverFillRemaining(child: NoData()),
                  if (state.tomesPosts.isNotEmpty)
                    MyAccountPostListView(posts: state.tomesPosts, isShared: widget.isShared, tomesId: widget.tomesId),
                ],
              );
            }
            return const Center(
              child: NoData(),
            );
          },
        ),
      ),
    );
  }
}
