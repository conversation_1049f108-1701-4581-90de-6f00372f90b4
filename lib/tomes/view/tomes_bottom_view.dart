import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/constants/app_theme.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/popup_wrapper.dart' show PopUpWrapper;

class TomesBottomView extends StatefulWidget {
  const TomesBottomView({super.key, this.onShareTap, this.onEditTap, this.onDeleteTap});
  final VoidCallback? onShareTap;
  final VoidCallback? onEditTap;
  final VoidCallback? onDeleteTap;

  @override
  State<TomesBottomView> createState() => _TomesBottomViewState();
}

class _TomesBottomViewState extends State<TomesBottomView> {
  @override
  Widget build(BuildContext context) {
    return PopUpWrapper(
      childrenPadding: const EdgeInsets.fromLTRB(20, 20, 20, 30),
      children: [
        Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  context.l10n.tomes,
                  style: context.textTheme.bodyLarge?.copyWith(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    fontFamily: AppTheme.spaceGroteskFontFamily,
                    color: AppColors.text,
                  ),
                ),
                const Spacer(),
                InkWell(
                  onTap: () => Navigator.of(context).pop(),
                  child: const AppImageAsset(
                    AppAssets.closeIcon,
                    height: 24,
                    width: 24,
                    color: AppColors.text,
                  ),
                ),
              ],
            ),
            const Gap(14),
            InkWell(
              onTap: () {
                Navigator.of(context).pop();
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  widget.onShareTap?.call();
                });
              },
              child: Container(
                padding: const EdgeInsets.only(top: 10, bottom: 10),
                width: double.infinity,
                child: Text(
                  context.l10n.share,
                  style: context.textTheme.bodyMedium?.copyWith(
                    fontSize: 15,
                    color: AppColors.text,
                  ),
                ),
              ),
            ),
            InkWell(
              onTap: () {
                Navigator.of(context).pop();
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  widget.onEditTap?.call();
                });
              },
              child: Container(
                padding: const EdgeInsets.only(
                  top: 10,
                  bottom: 10,
                ),
                width: double.infinity,
                child: Text(
                  context.l10n.edit,
                  style: context.textTheme.bodyMedium?.copyWith(
                    fontSize: 15,
                    color: AppColors.text,
                  ),
                ),
              ),
            ),
            InkWell(
              onTap: () {
                Navigator.of(context).pop();
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  widget.onDeleteTap?.call();
                });
              },
              child: Container(
                padding: const EdgeInsets.only(
                  top: 10,
                  bottom: 10,
                ),
                width: double.infinity,
                child: Text(
                  context.l10n.delete,
                  style: context.textTheme.bodyMedium?.copyWith(
                    fontSize: 15,
                    color: AppColors.text,
                  ),
                ),
              ),
            ),
          ],
        )
      ],
    );
  }
}
