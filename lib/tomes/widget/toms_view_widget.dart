import 'package:flutter/material.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/constants/app_theme.dart';
import 'package:tynt_web/tomes/model/tomes_model.dart';
import 'package:tynt_web/tomes/widget/tomes_bottom_widget.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/post_image_view.dart';

class TomsViewWidget extends StatelessWidget {
  const TomsViewWidget({
    this.tomes,
    this.onShareTap,
    this.onEditTap,
    this.onDeleteTap,
    this.onTap,
    this.showActionButton = true,
    this.isSelected = false,
    super.key,
  });
  final TomesModel? tomes;
  final VoidCallback? onShareTap;
  final VoidCallback? onEditTap;
  final VoidCallback? onDeleteTap;
  final VoidCallback? onTap;
  final bool showActionButton;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 120,
        width: 94,
        // padding: EdgeInsets.zero,
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(6),
          // border: Border.all(color: AppColors.red),
          boxShadow: [
            BoxShadow(
              color: AppColors.black.withOpacity(0.15),
              blurRadius: 6.83,
              offset: const Offset(0, 1.95),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              alignment: Alignment.center,
              children: [
                Stack(
                  children: [
                    PostImageView(
                      imageUrl: tomes?.imageUrl ?? '',
                      width: MediaQuery.sizeOf(context).width,
                      height: 80,
                      boxfit: BoxFit.fill,
                      placeholderImage: const Center(child: AppImageAsset(AppAssets.tomesIcon, height: 40, width: 40)),
                      borderRadiusGeometry: const BorderRadius.only(
                        topLeft: Radius.circular(6),
                        topRight: Radius.circular(6),
                      ),
                    ),
                    // if (widget.index == 0)
                    Container(
                      margin: const EdgeInsets.only(top: 68),
                      decoration: BoxDecoration(
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.white.withOpacity(1),
                            blurRadius: 8,
                            spreadRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                          BoxShadow(
                            color: Colors.white.withOpacity(1),
                            blurRadius: 4,
                            spreadRadius: 4,
                            offset: const Offset(0, 11),
                          ),
                        ],
                      ),
                      width: MediaQuery.sizeOf(context).width,
                      height: 2, // keep the line thin for better effect
                      // color: AppColors.white, // actual color of the line
                    ),
                    if (isSelected)
                      Container(
                        color: AppColors.primary.withOpacity(0.55),
                        width: MediaQuery.sizeOf(context).width,
                        height: 76,
                      ),
                  ],
                ),
                if (isSelected)
                  const AppImageAsset(
                    AppAssets.roundCheckIcon,
                    height: 32,
                    width: 32,
                  ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(6, 4, 0, 6),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          tomes?.name ?? '',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                fontWeight: FontWeight.w500,
                                fontFamily: AppTheme.manropeFontFamily,
                              ),
                        ),
                        Text(
                          '${tomes?.postsCount ?? 0} posts',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                fontSize: 10,
                                color: AppColors.subText,
                                fontFamily: AppTheme.manropeFontFamily,
                              ),
                        ),
                      ],
                    ),
                  ),
                  if (showActionButton)
                    TomesBottomWidget(
                      onShareTap: onShareTap,
                      onEditTap: onEditTap,
                      onDeleteTap: onDeleteTap,
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
