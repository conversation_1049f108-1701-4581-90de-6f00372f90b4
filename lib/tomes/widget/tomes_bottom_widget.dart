// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/dailog_boxes.dart';

class TomesBottomWidget extends StatelessWidget {
  const TomesBottomWidget({
    super.key,
    this.onShareTap,
    this.onEditTap,
    this.onDeleteTap,
    this.iconWidget,
    this.isForPostDetail = false,
  });

  final VoidCallback? onShareTap;
  final VoidCallback? onEditTap;
  final VoidCallback? onDeleteTap;
  final bool isForPostDetail;
  final Widget? iconWidget;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          DailogBoxes.openTomesDailog(
            context,
            onShareTap: onShareTap,
            onEditTap: onEditTap,
            onDeleteTap: onDeleteTap,
          );
        },
        child: AppImageAsset(
          AppAssets.menuIcon,
          height: 16,
          width: 16,
          color: context.theme.primaryColorLight,
        ));
  }
}
