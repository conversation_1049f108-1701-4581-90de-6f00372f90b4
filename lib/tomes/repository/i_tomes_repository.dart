import 'dart:typed_data';

import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:tynt_web/constants/app_strings.dart';
import 'package:tynt_web/tomes/response/create_tome_response.dart';
import 'package:tynt_web/tomes/response/tomes_detail_respose.dart';
import 'package:tynt_web/utlity/app_typedefs/http_typedef.dart';
import 'package:tynt_web/utlity/failure/http_failure.dart';
import 'package:tynt_web/utlity/network/client.dart';

part 'tomes_repository.dart';

abstract class ITomesRepository {
  ITomesRepository(this.client);
  final Client client;

  ApiResult<CreateTomesResponse> createTomes({
    required String name,
    required String description,
    Uint8List? image,
  });

  ApiResult<CreateTomesResponse> editTomes({
    required String name,
    required String description,
    required int id,
    Uint8List? image,
  });

  ApiResult<CreateTomesResponse> deleteTomes(
    int id,
  );

  ApiResult<TomesDetailsResponse> getTomesPostsList({
    String? tomesId,
    int page = 1,
    int perPage = 10,
  });
}
