part of 'i_tomes_repository.dart';

@Injectable(as: ITomesRepository)
class TomesRepository extends ITomesRepository {
  TomesRepository(super.client);

  @override
  ApiResult<CreateTomesResponse> createTomes({
    required String name,
    required String description,
    Uint8List? image,
  }) async {
    final response = await client.multipart(
      url: AppStrings.createTomes,
      webFiles: [
        if (image != null) MapEntry('image', image),
      ],
      requests: <String, String>{
        'name': name.trim(),
        'description': description.trim(),
      },
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(
        () => CreateTomesResponse.fromJson(r),
        (o, s) => HttpFailure.parsing(o.toString(), 500, s),
      ),
    );
  }

  @override
  ApiResult<CreateTomesResponse> editTomes({
    required String name,
    required String description,
    required int id,
    Uint8List? image,
  }) async {
    final response = await client.multipart(
      url: AppStrings.editTomes(id),
      webFiles: [
        if (image != null) MapEntry('image', image),
      ],
      requests: <String, String>{
        'name': name.trim(),
        'description': description.trim(),
      },
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(
        () => CreateTomesResponse.fromJson(r),
        (o, s) => HttpFailure.parsing(o.toString(), 500, s),
      ),
    );
  }

  @override
  ApiResult<CreateTomesResponse> deleteTomes(
    int id,
  ) async {
    final response = await client.delete(
      url: AppStrings.deleteTomes(id),
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(
        () => CreateTomesResponse.fromJson(r),
        (o, s) => HttpFailure.parsing(o.toString(), 500, s),
      ),
    );
  }

  @override
  ApiResult<TomesDetailsResponse> getTomesPostsList({
    String? tomesId,
    int page = 1,
    int perPage = 10,
  }) async {
    final response = await client.get(
      url: AppStrings.tomesDetail(tomesId!),
      params: <String, String>{
        'page': '$page',
        'per_page': '$perPage',
      },
    );
    return response.fold(
      left,
      (r) => Either.tryCatch(
        () => TomesDetailsResponse.fromJson(r),
        (o, s) => HttpFailure.parsing(o.toString(), 500, s),
      ),
    );
  }
}
