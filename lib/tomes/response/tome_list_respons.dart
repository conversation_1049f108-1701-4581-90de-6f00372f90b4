import 'package:equatable/equatable.dart';
import 'package:tynt_web/tomes/model/tomes_model.dart';

class TomesListResponse extends Equatable {
  const TomesListResponse({
    this.data = const [],
    this.message = '',
    this.status = '0',
  });

  factory TomesListResponse.fromJson(Map<String, dynamic> json) {
    return TomesListResponse(
      data: (json['data'] as List<dynamic>?)?.map((e) => TomesModel.fromJson(e as Map<String, dynamic>)).toList() ?? [],
      message: json['message'] as String? ?? '',
      status: json['status'] as String? ?? '0',
    );
  }
  final List<TomesModel> data;
  final String message;
  final String status;

  Map<String, dynamic> toJson() {
    return {
      'data': data.map((e) => e.toJson()).toList(),
      'message': message,
      'status': status,
    };
  }

  @override
  List<Object?> get props => [data, message, status];

  @override
  String toString() {
    return 'TomesListResponse{data: $data, message: $message, status: $status}';
  }
}
