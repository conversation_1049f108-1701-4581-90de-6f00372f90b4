import 'package:equatable/equatable.dart';
import 'package:tynt_web/tomes/model/tomes_detail_model.dart';

class TomesDetailsResponse extends Equatable {
  const TomesDetailsResponse({
    this.data,
    this.message = '',
    this.status = '0',
  });

  factory TomesDetailsResponse.fromJson(Map<String, dynamic> json) {
    return TomesDetailsResponse(
      data: json['data'] != null ? TomesDetailsModel.fromJson(json['data'] as Map<String, dynamic>) : null,
      message: json['message'] as String? ?? '',
      status: json['status'] as String? ?? '0',
    );
  }

  final TomesDetailsModel? data;
  final String message;
  final String status;

  Map<String, dynamic> toJson() {
    return {
      'data': data?.toJson(),
      'message': message,
      'status': status,
    };
  }

  @override
  List<Object?> get props => [data, message, status];

  @override
  String toString() {
    return 'TomesDetailsResponse{data: $data, message: $message, status: $status}';
  }
}
