import 'package:equatable/equatable.dart';
import 'package:tynt_web/tomes/model/tomes_model.dart';

class CreateTomesResponse extends Equatable {
  const CreateTomesResponse({
    this.tomes,
    this.message = '',
    this.status = '0',
  });

  factory CreateTomesResponse.fromJson(Map<String, dynamic> json) {
    return CreateTomesResponse(
      tomes: json['data'] != null ? TomesModel.fromJson(json['data'] as Map<String, dynamic>) : null,
      message: json['message'] as String? ?? '',
      status: json['status'] as String? ?? '0',
    );
  }

  final TomesModel? tomes;
  final String message;
  final String status;

  Map<String, dynamic> toJson() {
    return {
      'data': tomes?.toJson(),
      'message': message,
      'status': status,
    };
  }

  @override
  List<Object?> get props => [tomes, message, status];

  @override
  String toString() {
    return 'CreateTomesResponse{tomes: $tomes, message: $message, status: $status}';
  }
}
