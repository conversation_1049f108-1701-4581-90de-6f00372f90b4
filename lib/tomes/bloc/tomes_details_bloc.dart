import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:tynt_web/post_preview/model/post_model.dart';
import 'package:tynt_web/tomes/model/tomes_model.dart';
import 'package:tynt_web/tomes/repository/i_tomes_repository.dart';
import 'package:tynt_web/utlity/bloc_tranformer.dart';
import 'package:tynt_web/utlity/failure/http_failure.dart';

part 'tomes_details_event.dart';
part 'tomes_details_state.dart';

@injectable
class TomesDetailsBloc extends Bloc<TomesDetailsEvent, TomesDetailsState> {
  TomesDetailsBloc({
    required this.tomesRepository,
  }) : super(const TomesDetailsInitial()) {
    on<LoadTomesPost>(_onLoad);
    on<LoadMoreTomesPosts>(_onLoadMoreTomesPosts, transformer: loadMoreTransformer());
    on<Refresh>(_onRefresh);
    on<UpdateTome>(_onUpdateTome);
  }

  final ITomesRepository tomesRepository;

  Future<void> _onLoad(LoadTomesPost event, Emitter<TomesDetailsState> emit) async {
    emit(const TomesDetailsLoading());
    final response = await tomesRepository.getTomesPostsList(tomesId: event.tomesId);
    emit(
      response.fold(
        (l) => TomesDetailsError(tomesloadFailure: l),
        (r) => TomesDetailsLoaded(
          tomesPosts: [...r.data?.posts ?? []],
          tomes: r.data?.tomes,
          hasReachedMaxPosts: (r.data?.posts.length ?? 0) < 10,
        ),
      ),
    );
  }

  Future<void> _onUpdateTome(UpdateTome event, Emitter<TomesDetailsState> emit) async {
    if (state is! TomesDetailsLoaded) return;
    final currentState = state as TomesDetailsLoaded;
    if (currentState.tomes == null) return;

    emit(currentState.copyWith(tomes: event.tomes));
  }

  Future<void> _onLoadMoreTomesPosts(LoadMoreTomesPosts event, Emitter<TomesDetailsState> emit) async {
    if (state is! TomesDetailsLoaded) return;
    final currentState = state as TomesDetailsLoaded;

    emit(currentState.copyWith(isPageLoading: true));

    final response = await tomesRepository.getTomesPostsList(
      page: currentState.currentPostPage + 1,
      tomesId: event.tomesId,
    );

    emit(
      response.fold(
        (l) => currentState.copyWith(isPageLoading: false, failure: l),
        (r) => currentState.copyWith(
          tomesPosts: [...currentState.tomesPosts, ...r.data?.posts ?? []],
          tomes: r.data?.tomes,
          isPageLoading: false,
          currentPostPage: currentState.currentPostPage + 1,
          hasReachedMaxPosts: (r.data?.posts.length ?? 0) < 10,
        ),
      ),
    );
  }

  Future<void> _onRefresh(Refresh event, Emitter<TomesDetailsState> emit) async {
    emit(const TomesDetailsLoading());
    final response = await tomesRepository.getTomesPostsList(tomesId: event.tomesId);
    emit(
      response.fold(
        (l) => TomesDetailsError(tomesloadFailure: l),
        (r) => TomesDetailsLoaded(
          tomesPosts: [...r.data?.posts ?? []],
          tomes: r.data?.tomes,
          hasReachedMaxPosts: (r.data?.posts.length ?? 0) < 10,
        ),
      ),
    );
  }
}
