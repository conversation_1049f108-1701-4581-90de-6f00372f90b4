part of 'tomes_details_bloc.dart';

class TomesDetailsState {
  const TomesDetailsState();
}

class TomesDetailsInitial extends TomesDetailsState {
  const TomesDetailsInitial();
}

class TomesDetailsLoading extends TomesDetailsState {
  const TomesDetailsLoading();
}

class TomesDetailsLoaded extends TomesDetailsState {
  const TomesDetailsLoaded({
    this.isPageLoading = false,
    this.isLoading = false,
    this.failure,
    this.currentPostPage = 1,
    this.hasReachedMaxPosts = false,
    this.tomes,
    this.tomesPosts = const <PostModel>[],
  });
  final bool isPageLoading;
  final bool isLoading;
  final HttpFailure? failure;
  final int currentPostPage;
  final bool hasReachedMaxPosts;
  final TomesModel? tomes;
  final List<PostModel> tomesPosts;

  TomesDetailsLoaded copyWith({
    bool? isPageLoading,
    bool? isLoading,
    HttpFailure? failure,
    int? currentPostPage,
    bool? hasReachedMaxPosts,
    TomesModel? tomes,
    List<PostModel>? tomesPosts,
  }) {
    return TomesDetailsLoaded(
      isPageLoading: isPageLoading ?? this.isPageLoading,
      isLoading: isLoading ?? this.isLoading,
      failure: failure ?? this.failure,
      currentPostPage: currentPostPage ?? this.currentPostPage,
      hasReachedMaxPosts: hasReachedMaxPosts ?? this.hasReachedMaxPosts,
      tomes: tomes ?? this.tomes,
      tomesPosts: tomesPosts ?? this.tomesPosts,
    );
  }
}

class TomesDetailsError extends TomesDetailsState {
  const TomesDetailsError({
    this.userloadFailure,
    this.postloadFailure,
    this.tomesloadFailure,
  });
  final HttpFailure? userloadFailure;
  final HttpFailure? postloadFailure;
  final HttpFailure? tomesloadFailure;
}

// Extension (manual pattern matching helper)
extension TomesDetailsStateExtension on TomesDetailsState {
  HttpFailure? get httpFailure {
    if (this is TomesDetailsLoaded) {
      return (this as TomesDetailsLoaded).failure;
    } else if (this is TomesDetailsError) {
      final error = this as TomesDetailsError;
      return error.userloadFailure ?? error.postloadFailure;
    }
    return null;
  }
}
