part of 'tomes_details_bloc.dart';

abstract class TomesDetailsEvent {
  const TomesDetailsEvent();
}

class LoadTomesPost extends TomesDetailsEvent {
  const LoadTomesPost({required this.tomesId});
  final String tomesId;
}

class UpdateTome extends TomesDetailsEvent {
  const UpdateTome({required this.tomes});
  final TomesModel tomes;
}

class LoadMoreTomesPosts extends TomesDetailsEvent {
  const LoadMoreTomesPosts({required this.tomesId});
  final String tomesId;
}

class Refresh extends TomesDetailsEvent {
  const Refresh({required this.tomesId});
  final String tomesId;
}
