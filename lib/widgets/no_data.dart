// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';

class NoData extends StatelessWidget {
  const NoData({
    super.key,
    this.text,
    this.useSliver = false,
  });
  final String? text;
  final bool useSliver;

  @override
  Widget build(BuildContext context) {
    if (useSliver) {
      return SliverToBoxAdapter(
        child: Center(
          child: Text(
            text ?? context.l10n.nothingToseeHere,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600, fontSize: 20),
          ),
        ),
      );
    }
    return Center(
      child: Text(
        text ?? context.l10n.nothingToseeHere,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600, fontSize: 20),
      ),
    );
  }
}
