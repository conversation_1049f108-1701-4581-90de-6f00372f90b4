import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:tynt_web/auth/cubit/theme_cubit.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/tomes/model/tomes_model.dart';
import 'package:tynt_web/utlity/enum/user_type_enum.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/popup_wrapper.dart';

class TomesDetailsBottomView extends StatelessWidget {
  const TomesDetailsBottomView({super.key, this.tomes});

  final TomesModel? tomes;

  @override
  Widget build(BuildContext context) {
    return PopUpWrapper(childrenPadding: const EdgeInsets.fromLTRB(20, 20, 20, 30), children: [
      BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, state) {
          return SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        tomes?.name ?? '',
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                              fontSize: 18,
                              fontWeight: FontWeight.w700,
                              color: state.themeMode == ThemeMode.dark && state.currentUserType == UserType.normal
                                  ? AppColors.headingTextDark
                                  : AppColors.text,
                            ),
                      ),
                    ),
                    InkWell(
                      onTap: () => Navigator.of(context).pop(),
                      child: AppImageAsset(
                        AppAssets.closeIcon,
                        height: 24,
                        width: 24,
                        color: state.themeMode == ThemeMode.dark && state.currentUserType == UserType.normal
                            ? AppColors.headingTextDark
                            : AppColors.text,
                      ),
                    ),
                  ],
                ),
                const Gap(16),
                Text(
                  tomes?.description ?? '',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                        color: state.themeMode == ThemeMode.dark && state.currentUserType == UserType.normal
                            ? AppColors.headingTextDark
                            : AppColors.subText,
                      ),
                ),
              ],
            ),
          );
        },
      ),
    ]);
  }
}
