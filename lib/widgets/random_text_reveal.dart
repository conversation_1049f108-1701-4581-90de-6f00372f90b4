// ignore_for_file: public_member_api_docs, sort_constructors_first, cascade_invocations
import 'dart:math';

import 'package:flutter/material.dart';

class Source {
  static const digits = '0123456789';
  static const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  static const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  static const alphabets = '$uppercase$lowercase';
  static const specialCharacters = '`~!@#\$%^&*()_+-=[]{}\\|;:\'".>/?';
  static const all = '$digits$alphabets$specialCharacters';
}

class RandomTextReveal extends StatefulWidget {
  /// Creates an animated text widget.
  ///
  /// If the [style] argument is null, the text will use the style from the
  /// closest enclosing [DefaultTextStyle].
  ///
  /// The [overflow] property's behavior is affected by the [softWrap] argument.
  /// If the [softWrap] is true or null, the glyph causing overflow, and those that follow,
  /// will not be rendered. Otherwise, it will be shown with the given overflow option.
  const RandomTextReveal({
    required this.text,
    super.key,
    this.initialText,
    this.shouldPlayOnStart = false,
    this.randomString = Source.uppercase,
    this.duration = const Duration(seconds: 2),
    this.onFinished,
    this.curve = Curves.easeIn,
    this.style,
    this.initialTextStyle,
    this.textDirection = TextDirection.ltr,
    this.locale,
    this.maxLines,
    this.overflow,
    this.softWrap,
    this.textAlign,
    this.semanticsLabel,
  });

  /// The text to display after animation is finished.
  final String text;

  /// Initial random text that you want to display
  final String? initialText;

  /// Boolean flag to auto play animation on start
  final bool shouldPlayOnStart;

  /// The text from which random characters will be displayed before
  /// the actual character
  final String randomString;

  /// The length of time this animation should last.
  final Duration duration;

  /// A function which will be triggered at the end of animation
  final VoidCallback? onFinished;

  /// A collection of common animation curves.
  final Curve curve;

  /// The style of the text with specified color, fontWeight, fontSize
  final TextStyle? style;

  final TextStyle? initialTextStyle;

  /// Represents directionality of text.
  final TextDirection? textDirection;

  /// The Locale used for multiple language support
  final Locale? locale;

  /// The maximum number of lines of the text to be displayed before cropping
  final int? maxLines;

  /// The type of overflow in text
  final TextOverflow? overflow;

  /// Whether the text should break at soft line breaks.
  final bool? softWrap;

  /// How the text should be aligned horizontally.
  final TextAlign? textAlign;

  /// An alternative semantics label for this text.
  final String? semanticsLabel;

  @override
  State<RandomTextReveal> createState() => RandomTextRevealState();
}

class RandomTextRevealState extends State<RandomTextReveal> with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  bool loading = false;
  late String text = widget.text;

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);

    final curvedAnimation = CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    );

    _animation = Tween<double>(
      begin: 0,
      end: widget.text.length.toDouble(),
    ).animate(curvedAnimation)
      ..addListener(() {
        setState(() {});
      });

    if (widget.shouldPlayOnStart) {
      play();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      _animatedText(
        text: text,
        value: _animation.value.toInt(),
      ),
      style: getTextStyle,
      textDirection: widget.textDirection,
      locale: widget.locale,
      maxLines: widget.maxLines,
      overflow: widget.overflow,
      softWrap: widget.softWrap,
      textAlign: widget.textAlign,
      semanticsLabel: widget.semanticsLabel,
    );
  }

  String _animatedText({required String text, required int value}) {
    if (!_controller.isAnimating && !_controller.isCompleted) {
      return widget.initialText ?? '';
    }
    if (text.trim().isEmpty) {
      return widget.initialText ?? '';
    }

    if (value == text.length) {
      widget.onFinished?.call();
      return text;
    }

    final substring = text.substring(0, value);

    final len = loading ? text.length : text.length - substring.length;

    final random = Random();
    final randomString = StringBuffer();

    for (var i = 0; i < len; i++) {
      final index = random.nextInt(widget.randomString.length);
      randomString.writeCharCode(
        widget.randomString.codeUnitAt(index),
      );
    }

    if (loading) {
      return '$randomString';
    }

    return '$substring$randomString';
  }

  TextStyle? get getTextStyle {
    if (!_controller.isAnimating && !_controller.isCompleted && widget.initialTextStyle != null) {
      return widget.initialTextStyle;
    }
    if (text.trim().isEmpty && widget.initialTextStyle != null) {
      return widget.initialTextStyle;
    }
    return widget.style;
  }

  void play({double from = 0}) {
    _controller.forward(from: from);
    setState(() {
      loading = true;
    });
    _controller.repeat();
  }

  void stop() {
    setState(() {
      loading = false;
    });
    final curvedAnimation = CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    );

    _controller.stop();
    _animation = Tween<double>(
      begin: 0,
      end: text.length.toDouble(),
    ).animate(curvedAnimation)
      ..addListener(() {
        setState(() {});
      });
    _controller.forward();
  }

  void playText({double from = 0, String? newText}) {
    setState(() {
      loading = true;
      text = newText ?? text;
    });
    final curvedAnimation = CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    );
    _animation = Tween<double>(
      begin: 0,
      end: text.length.toDouble(),
    ).animate(curvedAnimation)
      ..addListener(() {
        setState(() {});
      });
    _controller.forward(from: from);
    _controller.repeat();
  }

  void setText(String? newText) {
    setState(() {
      text = newText ?? text;
    });
  }

  void successText(String? newText) {
    setState(() {
      loading = false;
      text = newText ?? text;
    });
    final curvedAnimation = CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    );

    _controller.stop();
    _animation = Tween<double>(
      begin: 0,
      end: text.length.toDouble(),
    ).animate(curvedAnimation)
      ..addListener(() {
        setState(() {});
      });
    _controller.forward();
  }

  void errorText() {
    setState(() {
      loading = false;
      text = '';
    });

    final curvedAnimation = CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    );

    _controller.stop();
    _animation = Tween<double>(
      begin: 0,
      end: text.length.toDouble(),
    ).animate(curvedAnimation)
      ..addListener(() {
        setState(() {});
      });
    _controller.forward();
  }
}
