// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:tynt_web/auth/bloc/authentication_bloc.dart';
import 'package:tynt_web/auth/cubit/theme_cubit.dart';
import 'package:tynt_web/auth/models/app_file_data.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/primary_button/utility/primary_button_utlity.dart';
import 'package:tynt_web/primary_button/views/primary_button_view.dart';
import 'package:tynt_web/user/repository/i_user_manager_repository.dart';
import 'package:tynt_web/user_profile/widget/user_circle_avatar.dart';
import 'package:tynt_web/utlity/app_validation.dart';
import 'package:tynt_web/utlity/enum/user_type_enum.dart';
import 'package:tynt_web/utlity/logger_config.dart';
import 'package:tynt_web/utlity/utlity.dart';
import 'package:tynt_web/widgets/animated_tooltip.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/app_text_form_field.dart';
import 'package:tynt_web/widgets/dailog_boxes.dart';
import 'package:tynt_web/widgets/popup_wrapper.dart';
import 'package:tynt_web/widgets/tynt_name_generator.dart';

class CreatePseudoProfileBottomsheet extends StatefulWidget {
  const CreatePseudoProfileBottomsheet({super.key, this.isFromBottomNavigation = false});
  final bool isFromBottomNavigation;

  @override
  State<CreatePseudoProfileBottomsheet> createState() => CreatePseudoProfileBottomsheetState();
}

class CreatePseudoProfileBottomsheetState extends State<CreatePseudoProfileBottomsheet> {
  final selectedAvatar = ValueNotifier<AppFileData>(const AppFileData());

  final nameController = TextEditingController();

  final _form = GlobalKey<FormState>();

  final buttonController = PrimaryLoadingButtonController();

  final passcodeVisibility = ValueNotifier<bool>(true);

  final pseudoPasscodeController = TextEditingController();

  final showTyntError = ValueNotifier<bool>(false);

  @override
  void dispose() {
    nameController.dispose();
    selectedAvatar.dispose();
    buttonController.dispose();
    pseudoPasscodeController.dispose();
    passcodeVisibility.dispose();
    showTyntError.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopUpWrapper(
      childrenPadding: const EdgeInsets.fromLTRB(20, 20, 20, 30),
      children: [
        Container(
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 24),
          child: Form(
            key: _form,
            child: SingleChildScrollView(
              child: BlocBuilder<ThemeCubit, ThemeState>(
                builder: (context, state) {
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          IconButton(
                            constraints: const BoxConstraints(),
                            padding: EdgeInsets.zero,
                            onPressed: () {
                              context.pop();
                            },
                            icon: Icon(
                              Icons.close_outlined,
                              color: state.themeMode == ThemeMode.dark && state.currentUserType == UserType.normal
                                  ? AppColors.headingTextDark
                                  : AppColors.text,
                              size: context.isMobile ? 24 : 30,
                            ),
                          ),
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              context.l10n.createTyntProfile,
                              style: context.textTheme.headlineSmall?.copyWith(
                                fontSize: context.isMobile ? 22 : 26,
                                letterSpacing: 1,
                                fontWeight: FontWeight.w700,
                                color: state.themeMode == ThemeMode.dark && state.currentUserType == UserType.normal
                                    ? AppColors.headingTextDark
                                    : AppColors.text,
                              ),
                            ),
                            const SizedBox(height: 6),
                            Text(
                              context.l10n.thisDetailsWillMakeYouAnonymousOnThisPlatform,
                              style: context.textTheme.bodyMedium?.copyWith(
                                fontSize: context.isMobile ? 14 : 16,
                                letterSpacing: 1,
                                color: state.themeMode == ThemeMode.dark && state.currentUserType == UserType.normal
                                    ? AppColors.subTextDark
                                    : AppColors.subText,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            const Gap(30),
                            Row(
                              children: [
                                ValueListenableBuilder<AppFileData>(
                                  valueListenable: selectedAvatar,
                                  builder: (_, value, __) {
                                    if (value.imageBytes != null) {
                                      return CircleAvatar(
                                        radius: context.isMobile ? 36 : 50,
                                        backgroundImage: Image.memory(value.imageBytes!).image,
                                      );
                                    }
                                    if (value.selectedAvatar != null) {
                                      return UserCircleAvatar(
                                        radius: context.isMobile ? 36 : 50,
                                        imageUrl: value.selectedAvatar?.imageUrl,
                                      );
                                    }
                                    return CircleAvatar(
                                      radius: context.isMobile ? 36 : 50,
                                      backgroundImage: Image.asset(value.defaultAvatar).image,
                                    );
                                  },
                                ),
                                Gap(context.isMobile ? 16 : 22),
                                TextButton(
                                  style: TextButton.styleFrom(
                                    padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
                                    shape: RoundedRectangleBorder(
                                      side: const BorderSide(color: AppColors.border),
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                  ),
                                  onPressed: () {
                                    DailogBoxes.selectAvatarPopup(
                                      context,
                                      defaultAvatar: selectedAvatar.value.defaultAvatar,
                                      onAvatarTap: () async {
                                        final pickedAvatar = await DailogBoxes.selectAvatarBottomSheet(
                                          context,
                                          selectedAvatar: selectedAvatar.value.selectedAvatar,
                                          isFromBottomNavigation: widget.isFromBottomNavigation,
                                        );

                                        if (pickedAvatar != null) {
                                          selectedAvatar.value = AppFileData(selectedAvatar: pickedAvatar);
                                        }
                                      },
                                      onGalleryTap: () async {
                                        final pickedImage = await ImagePicker().pickImage(source: ImageSource.gallery);
                                        if (pickedImage != null) {
                                          selectedAvatar.value =
                                              AppFileData(imageBytes: await pickedImage.readAsBytes());
                                        }
                                      },
                                    );
                                  },
                                  child: Text(
                                    context.l10n.uploadPic,
                                    style: context.textTheme.bodyMedium?.copyWith(
                                      color:
                                          state.themeMode == ThemeMode.dark && state.currentUserType == UserType.normal
                                              ? AppColors.headingTextDark
                                              : AppColors.primary,
                                      fontSize: context.isMobile ? 15 : 18,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            Gap(context.isMobile ? 22 : 32),
                            ValueListenableBuilder<bool>(
                              valueListenable: showTyntError,
                              builder: (_, showError, __) {
                                return TyntNameGenerator(
                                  showEmptyError: showError,
                                  isRequired: true,
                                  onNameChanged: (value) {
                                    if (value == null || value.isEmpty) {
                                      return;
                                    }
                                    nameController.text = value;
                                  },
                                  children: [
                                    const WidgetSpan(child: SizedBox(width: 5)),
                                    WidgetSpan(
                                      alignment: PlaceholderAlignment.middle,
                                      child: _TyntToolTipWidget(
                                        key: const ValueKey('TYNT_NAME_INFO_DESCRIPTION_TOOLTIP'),
                                        text:
                                            '${context.l10n.tyntNameInfoDescription1}\n\n${context.l10n.tyntNameInfoDescription2}',
                                      ),
                                    ),
                                  ],
                                );
                              },
                            ),
                            Gap(context.isMobile ? 16 : 20),
                            ValueListenableBuilder(
                              valueListenable: passcodeVisibility,
                              builder: (_, isObsureText, __) {
                                return AppTextFormField(
                                  hintText: context.l10n.tyntPasscode,
                                  title: context.l10n.tyntPasscode,
                                  obscureText: isObsureText,
                                  textInputAction: TextInputAction.next,
                                  keyboardType: TextInputType.number,
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                    LengthLimitingTextInputFormatter(4),
                                  ],
                                  suffixIconConstraints: const BoxConstraints(),
                                  suffixIcon: InkWell(
                                    onTap: () {
                                      passcodeVisibility.value = !passcodeVisibility.value;
                                    },
                                    child: SizedBox(
                                      height: 20,
                                      child: Padding(
                                        padding: const EdgeInsets.only(right: 16),
                                        child: AppImageAsset(isObsureText ? AppAssets.eyeOffIcon : AppAssets.eyeOnIcon),
                                      ),
                                    ),
                                  ),
                                  counterText: '',
                                  isRequired: true,
                                  controller: pseudoPasscodeController,
                                  validator: (value) => AppValidation.pseudoPasscodeValidation(
                                    context,
                                    value: value,
                                    pseudoNameValue: nameController.text.trim(),
                                    isRequired: true,
                                  ),
                                  headerWidgets: [
                                    const WidgetSpan(child: SizedBox(width: 5)),
                                    WidgetSpan(
                                      alignment: PlaceholderAlignment.middle,
                                      child: _TyntToolTipWidget(
                                        key: const ValueKey('TYNT_PASSCODE_TOOL_TIP'),
                                        text: context.l10n.tyntPasscodeInfoDescription,
                                      ),
                                    ),
                                  ],
                                );
                              },
                            ),
                            const Gap(16),
                            Center(
                              child: PrimaryButton(
                                onPressed: () {
                                  if (isTyntNameEmpty) {
                                    showTyntError.value = true;
                                  } else {
                                    showTyntError.value = false;
                                  }
                                  if (_form.currentState!.validate()) {
                                    if (showTyntError.value) return;
                                    createPseudoProfile();
                                  }
                                },
                                primaryLoadingButtonController: buttonController,
                                text: context.l10n.save,
                              ),
                            ),
                            if (MediaQuery.viewInsetsOf(context).bottom == 0) const Gap(15),
                          ],
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        ),
      ],
    );
  }

  bool get isTyntNameEmpty => nameController.text.trim().isEmpty;

  Future<void> createPseudoProfile() async {
    buttonController.start();

    final failOrSuccess = await getIt<IUserManagerRepository>().updateUser(
      tyntName: nameController.text.trim(),
      profileImage: selectedAvatar.value.imageBytes,
      pseudoAvatarId: selectedAvatar.value.selectedAvatar?.id,
      passcode: pseudoPasscodeController.text.trim(),
    );

    failOrSuccess.fold((l) {
      buttonController.error();
      debugError(l);
      Utility.toast(message: l.message);
    }, (r) {
      buttonController.success();
      Future.delayed(const Duration(milliseconds: 820), () {
        context.read<AuthenticationBloc>().add(UpdateUserEvent(user: r.data));
        // Utility.toast(message: r.message);
        context.pop(true);
      });
    });
  }
}

class _TyntToolTipWidget extends StatelessWidget {
  const _TyntToolTipWidget({
    required this.text,
    super.key,
  });
  final String text;

  @override
  Widget build(BuildContext context) {
    return AnimatedTooltip(
      tooltipColor: AppColors.primary,
      content: SizedBox(
        width: MediaQuery.sizeOf(context).shortestSide / 1.2,
        child: Text(
          text,
          style: context.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w400,
            fontSize: context.isMobile ? 14 : 15,
            color: AppColors.white,
          ),
        ),
      ),
      child: const Icon(
        Icons.info,
        color: AppColors.primary,
        size: 20,
      ),
    );
  }
}
