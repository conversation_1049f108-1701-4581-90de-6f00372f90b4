// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/auth/cubit/theme_cubit.dart';
import 'package:tynt_web/auth/models/avatar_model.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/constants/app_constants.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/user/repository/i_user_manager_repository.dart';
import 'package:tynt_web/user_profile/widget/user_circle_avatar.dart';
import 'package:tynt_web/utlity/easy_debouncer.dart';
import 'package:tynt_web/utlity/enum/user_type_enum.dart';
import 'package:tynt_web/utlity/pagination/pagination_mixin.dart';

class SelectAvatarBottomSheet extends StatefulWidget {
  const SelectAvatarBottomSheet({
    super.key,
    this.selectedAvatar,
    this.isFromBottomNavigation = false,
  });
  final bool isFromBottomNavigation;
  final AvatarModel? selectedAvatar;

  @override
  State<SelectAvatarBottomSheet> createState() => _SelectAvatarBottomSheetState();
}

class _SelectAvatarBottomSheetState extends State<SelectAvatarBottomSheet> with PaginationMixin {
  late final selectedAvatar = ValueNotifier<AvatarModel?>(widget.selectedAvatar);
  final isLoading = ValueNotifier<bool>(false);
  final isLoadingMore = ValueNotifier<bool>(false);
  final avatarList = ValueNotifier<List<AvatarModel>>(<AvatarModel>[]);

  int currentPage = 1;

  bool hasReachedMax = false;

  @override
  void initState() {
    super.initState();
    initiatePagination();
    load();
  }

  @override
  void dispose() {
    selectedAvatar.dispose();
    avatarList.dispose();
    isLoading.dispose();
    isLoadingMore.dispose();
    disposePagination();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.all(20),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 458),
        child: Column(
          children: [
            const Gap(24),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: BlocBuilder<ThemeCubit, ThemeState>(
                builder: (context, state) {
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        context.l10n.selectAvatar,
                        style: context.textTheme.bodyLarge?.copyWith(
                          fontSize: context.isMobile ? 18 : 22,
                          fontWeight: FontWeight.w700,
                          color: state.themeMode == ThemeMode.dark && state.currentUserType == UserType.normal
                              ? AppColors.headingTextDark
                              : AppColors.text,
                        ),
                      ),
                      IconButton(
                        onPressed: () {
                          context.pop();
                        },
                        icon: Icon(
                          Icons.close_outlined,
                          color: state.themeMode == ThemeMode.dark && state.currentUserType == UserType.normal
                              ? AppColors.headingTextDark
                              : AppColors.text,
                          size: context.isMobile ? 24 : 30,
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
            Gap(context.isMobile ? 20 : 24),
            Flexible(
              child: ValueListenableBuilder<bool>(
                valueListenable: isLoading,
                builder: (_, value, __) {
                  if (value) {
                    return Skeletonizer(
                      child: GridView.builder(
                        itemCount: 20,
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 4,
                          crossAxisSpacing: 20,
                          mainAxisSpacing: 20,
                        ),
                        itemBuilder: (_, index) {
                          return const SelectAvatarWidget(avatar: AppAssets.defaultMan);
                        },
                      ),
                    );
                  }
                  return ValueListenableBuilder<List<AvatarModel>>(
                    valueListenable: avatarList,
                    builder: (_, avatars, __) {
                      return ValueListenableBuilder<AvatarModel?>(
                        valueListenable: selectedAvatar,
                        builder: (_, choosedAvatar, __) {
                          return ValueListenableBuilder<bool>(
                            valueListenable: isLoadingMore,
                            builder: (_, loadingMore, __) {
                              return GridView.builder(
                                controller: scrollPaginationController,
                                cacheExtent: 1000,
                                padding: widget.isFromBottomNavigation
                                    ? EdgeInsets.fromLTRB(
                                        16,
                                        0,
                                        16,
                                        (context.isMobile ? kBottomPadding : kBottomPaddingTablet),
                                      )
                                    : const EdgeInsets.symmetric(horizontal: 24),
                                itemCount: loadingMore ? avatars.length + 8 : avatars.length,
                                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 4,
                                  crossAxisSpacing: 16,
                                  mainAxisSpacing: 16,
                                ),
                                itemBuilder: (_, index) {
                                  final isIndexOutOfAvatars = (avatars.length - 1) < index;
                                  if (isIndexOutOfAvatars && loadingMore) {
                                    return const Skeletonizer(child: SelectAvatarWidget(avatar: AppAssets.defaultMan));
                                  }
                                  final avatar = avatars[index];
                                  return SelectAvatarWidget(
                                    isSelected: choosedAvatar == avatar,
                                    onTap: () {
                                      selectedAvatar.value = avatar;
                                      context.pop(selectedAvatar.value);
                                    },
                                    avaterUrl: avatar.imageUrl ?? '',
                                  );
                                },
                              );
                            },
                          );
                        },
                      );
                    },
                  );
                },
              ),
            ),
            const Gap(24),
          ],
        ),
      ),
    );
  }

  Future<void> load() async {
    isLoading.value = true;

    final failOrSuccess = await getIt<IUserManagerRepository>().getAvatars(perPage: 25);

    failOrSuccess.fold((l) {
      isLoading.value = false;
    }, (r) {
      isLoading.value = false;
      avatarList.value = [...r.avatarList];
      hasReachedMax = r.avatarList.length < 10;
    });
  }

  Future<void> loadMore() async {
    isLoadingMore.value = true;
    final failOrSuccess = await getIt<IUserManagerRepository>().getAvatars(page: currentPage + 1, perPage: 25);

    failOrSuccess.fold((l) {
      isLoadingMore.value = false;
    }, (r) {
      isLoadingMore.value = false;
      avatarList.value = [...avatarList.value, ...r.avatarList];
      currentPage += 1;
      hasReachedMax = r.avatarList.length < 10;
    });
  }

  @override
  void onReachedLast() {
    if (!isLoading.value && !isLoadingMore.value && !hasReachedMax) {
      EasyDebounce.debounce('AvatarList_Debounce', const Duration(milliseconds: 350), loadMore);
    }
  }
}

class SelectAvatarWidget extends StatelessWidget {
  const SelectAvatarWidget({
    super.key,
    this.avatar,
    this.avaterUrl,
    this.isSelected = false,
    this.onTap,
  });
  final String? avatar;
  final String? avaterUrl;
  final bool isSelected;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Skeleton.shade(
        child: BlocBuilder<ThemeCubit, ThemeState>(
          builder: (context, state) {
            return CircleAvatar(
              backgroundColor: isSelected
                  ? AppColors.primary
                  : state.themeMode == ThemeMode.dark && state.currentUserType == UserType.normal
                      ? AppColors.headingTextDark
                      : AppColors.white,
              radius: context.isMobile ? 40 : 53,
              child: UserCircleAvatar(
                imageUrl: avaterUrl,
                radius: context.isMobile ? 32 : 40,
              ),
            );
          },
        ),
      ),
    );
  }
}
