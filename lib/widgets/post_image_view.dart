// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/app_image_network.dart';

class PostImageView extends StatelessWidget {
  const PostImageView({
    super.key,
    this.imageUrl,
    this.height,
    this.width,
    this.localFileBytes,
    this.boxfit = BoxFit.cover,
    this.borderRadius,
    this.borderRadiusGeometry,
    this.placeholderImage,
  });
  final String? imageUrl;
  final double? height;
  final double? width;
  final Uint8List? localFileBytes;
  final BoxFit boxfit;
  final double? borderRadius;
  final BorderRadiusGeometry? borderRadiusGeometry;
  final Widget? placeholderImage;

  @override
  Widget build(BuildContext context) {
    if (localFileBytes != null) {
      return ClipRRect(
        borderRadius: borderRadiusGeometry ?? BorderRadius.circular(borderRadius ?? 12),
        clipBehavior: Clip.hardEdge,
        child: Image.memory(
          localFileBytes!,
          fit: boxfit,
          height: height,
          width: width,
        ),
      );
    }
    return ClipRRect(
      borderRadius: borderRadiusGeometry ?? BorderRadius.circular(borderRadius ?? 12),
      child: AppImageNetwork(
        url: imageUrl ?? '',
        height: height,
        customErrorWidget: Container(
          decoration: BoxDecoration(
            borderRadius: borderRadiusGeometry ?? BorderRadius.circular(12),
          ),
          clipBehavior: Clip.hardEdge,
          height: height ?? 400,
          width: MediaQuery.sizeOf(context).width,
          child: placeholderImage ?? const AppImageAsset(AppAssets.noImagePlaceholder),
        ),
        removeMermoryCache: true,
        loading: AppImageLoading(
          boxShape: BoxShape.rectangle,
          height: height ?? 400,
          width: MediaQuery.sizeOf(context).width,
        ),
        width: width,
        fit: boxfit,
      ),
    );
  }
}
