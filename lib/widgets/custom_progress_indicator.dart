import 'package:flutter/material.dart';
import 'package:tynt_web/constants/app_colors.dart';

class CustomProgressIndicator extends StatelessWidget {
  const CustomProgressIndicator({
    super.key,
    this.color,
    this.value,
  });
  final Color? color;
  final double? value;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: CircularProgressIndicator(
        color: color ?? ((Theme.of(context).brightness == Brightness.dark) ? AppColors.white : AppColors.primary),
        value: value,
      ),
    );
  }
}
