import 'package:animate_do/animate_do.dart';
import 'package:flutter/cupertino.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/widgets/blur_widget.dart';

class FullScreenLoading extends StatelessWidget {
  const FullScreenLoading({
    super.key,
    this.progressColor = AppColors.white,
  });

  factory FullScreenLoading.black() {
    return const FullScreenLoading(progressColor: AppColors.black);
  }

  final Color progressColor;

  @override
  Widget build(BuildContext context) {
    return ElasticIn(
      child: FadeIn(
        child: Pulse(
          child: BlurWidget(
            signmaX: 4.5,
            signmaY: 4.5,
            child: Center(child: CupertinoActivityIndicator(color: progressColor, radius: 30)),
          ),
        ),
      ),
    );
  }
}
