// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';

class AppIconButton extends StatelessWidget {
  const AppIconButton({
    required this.icon,
    required this.onPressed,
    super.key,
    this.size,
    this.iconSize,
    this.color,
  });
  final String icon;
  final VoidCallback onPressed;
  final double? size;
  final double? iconSize;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Skeleton.shade(
      child: IconButton(
        onPressed: onPressed,
        splashRadius: size,
        constraints: size == null ? const BoxConstraints() : BoxConstraints(maxHeight: size!, maxWidth: size!),
        padding: EdgeInsets.zero,
        icon: AppImageAsset(
          icon,
          color: color,
          height: iconSize,
          width: iconSize,
        ),
      ),
    );
  }
}
