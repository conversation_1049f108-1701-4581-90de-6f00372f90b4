import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/constants/app_colors.dart';

class AppImageAsset extends StatelessWidget {
  const AppImageAsset(
    String assetName, {
    super.key,
    this.height,
    this.width,
    this.fit = BoxFit.cover,
    this.color,
  }) : asset = assetName;
  final String asset;
  final double? height;
  final double? width;
  final BoxFit fit;
  final Color? color;
  @override
  Widget build(BuildContext context) {
    return Skeleton.replace(
      replacement: ClipRRect(
        borderRadius: BorderRadius.circular(100),
        child: Container(
          height: height,
          width: width,
          color: AppColors.red,
        ),
      ),
      child: SvgPicture.asset(
        asset,
        height: height,
        width: width,
        fit: fit,
        colorFilter: color == null ? null : ColorFilter.mode(color!, BlendMode.srcIn),
      ),
    );
  }
}
