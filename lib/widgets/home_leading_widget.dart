import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/auth/bloc/authentication_bloc.dart';
import 'package:tynt_web/auth/cubit/theme_cubit.dart';
import 'package:tynt_web/auth/models/user_model.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/local_storage/repository/i_local_storage_repository.dart';
import 'package:tynt_web/master/widget/authenticated_user_avatar.dart';
import 'package:tynt_web/master/widget/user_swicher.dart';
import 'package:tynt_web/routes/app_routes.dart';

class HomeLeadingWidget extends StatelessWidget {
  const HomeLeadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Gap(20),
        CircleAvatar(
          radius: context.isMobile ? 19 : 23,
          backgroundColor: AppColors.border2,
          child: CircleAvatar(
            radius: context.isMobile ? 18 : 22,
            backgroundColor: AppColors.white,
            child: BlocSelector<AuthenticationBloc, AuthenticationState, UserModel?>(
              selector: (state) => state.currentUser,
              builder: (context, user) {
                return Skeletonizer(
                  enabled: user == null || user.id == 0,
                  child: Skeleton.shade(
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      onSelected: (value) async {
                        if (value == 'signOut') {
                          await getIt<ILocalStorageRepository>().removeAuth();
                          await GoogleSignIn().signOut();
                          context.go(AppRoutes.login.route);
                        }
                      },
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      itemBuilder: (context) => [
                        PopupMenuItem<String>(
                          value: 'signOut',
                          child: Row(
                            children: [
                              const Icon(Icons.logout, color: Colors.red),
                              const SizedBox(width: 8),
                              Text(
                                context.l10n.signOut,
                                style: const TextStyle(color: Colors.red),
                              ),
                            ],
                          ),
                        ),
                      ],
                      child: AuthenticatedUserAvatar(
                        newImageUrl: user?.formmtedAvatarProfile,
                        radius: context.isMobile ? 16 : 20,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        const Gap(20),
        BlocListener<ThemeCubit, ThemeState>(
          listenWhen: (previous, current) => previous.currentUserType != current.currentUserType,
          listener: (context, state) {
            context.read<AuthenticationBloc>().add(const ChangeCurrentUserEvent());
          },
          child: UserSwitcher(
            onTap: (details) {
              context.read<ThemeCubit>().changeUserToggle();
            },
            width: 54,
          ),
        ),
      ],
    );
  }
}
