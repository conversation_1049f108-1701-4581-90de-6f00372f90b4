import 'dart:async';

import 'package:flutter/material.dart';
import 'package:tynt_web/auth/models/avatar_model.dart';
import 'package:tynt_web/auth/models/country_model.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/create_post/view/delete_post_bottom_view.dart';
import 'package:tynt_web/create_post/view/discard_post_changes_bottom_view.dart';
import 'package:tynt_web/create_post/view/post_setting_dailog.dart';
import 'package:tynt_web/create_post/widget/delete_post_view.dart';
import 'package:tynt_web/create_post/widget/discard_post_changes_view.dart';
import 'package:tynt_web/create_post/widget/search_genre_bottom_sheet.dart';
import 'package:tynt_web/create_post/widget/search_post_type_bottom_sheet.dart';
import 'package:tynt_web/post_preview/model/post_genres_model.dart';
import 'package:tynt_web/post_preview/model/post_type_model.dart' show PostTypeModel;
import 'package:tynt_web/primary_button/utility/primary_button_utlity.dart';
import 'package:tynt_web/screen_lock/views/screen_lock.dart';
import 'package:tynt_web/tomes/model/tomes_model.dart';
import 'package:tynt_web/tomes/view/share_tom_dailog_view.dart';
import 'package:tynt_web/tomes/view/tomes_bottom_view.dart';
import 'package:tynt_web/user_profile/view/create_tom_bottom_view.dart';
import 'package:tynt_web/user_profile/view/delete_tomes_bottom_view.dart';
import 'package:tynt_web/user_profile/view/select_country_bottom_sheet.dart';
import 'package:tynt_web/user_profile/widget/post_filter_bottom_view.dart';
import 'package:tynt_web/widgets/blur_widget.dart';
import 'package:tynt_web/widgets/create_pseudo_profile_bottomsheet.dart';
import 'package:tynt_web/widgets/select_avatar_bottom_sheet.dart';
import 'package:tynt_web/widgets/select_avatar_popup.dart';
import 'package:tynt_web/widgets/tomes_details_bottom_view.dart';

abstract class DailogBoxes {
  static Future<T?> showBluredBgDailog<T extends Object?>(
    BuildContext context,
    Widget child, {
    double blurValue = 3.5,
    Color barrierColor = Colors.black38,
  }) {
    return showGeneralDialog<T>(
      context: context,
      barrierDismissible: true,
      barrierLabel: '',
      barrierColor: barrierColor,
      transitionBuilder: (ctx, anim1, anim2, animChild) => BlurWidget(
        opacity: anim1,
        signmaX: blurValue * anim1.value,
        signmaY: blurValue * anim2.value,
        child: animChild,
      ),
      pageBuilder: (context, anim1, anim2) => child,
    );
  }

  static Future<void> openShareBottomSheet(
    BuildContext context, {
    void Function()? onShareTap,
    Uri? shareLink,
  }) {
    return showBluredBgDailog<void>(
      context,
      ShareTomBottomView(
        onShareTap: onShareTap,
        shareLink: shareLink,
      ),
      blurValue: 1.5,
    );
  }

  static Future<void> openPostSettingDailog(
    BuildContext context, {
    required int postId,
  }) {
    return showBluredBgDailog<void>(
      context,
      PostSettingDailogView(
        postId: postId,
      ),
      blurValue: 1.5,
    );
  }

  static Future<CountryModel?> selectCountryDailog(
    BuildContext context,
  ) {
    return showBluredBgDailog<CountryModel>(
      context,
      DraggableScrollableSheet(
        initialChildSize: 1,
        builder: (_, controller) {
          return SelectCountryPopUp(scrollController: controller);
        },
      ),
      blurValue: 1.5,
    );
  }

  static Future<void> searchPostTypeDailog(
    BuildContext context, {
    ValueChanged<PostTypeModel>? onPostTypeSelected,
  }) {
    return showBluredBgDailog<void>(
      context,
      SearchPostBottomSheet(onPostTypeSelected: onPostTypeSelected),
      blurValue: 1.5,
    );
  }

  static Future<void> searchGenresDailog(
    BuildContext context, {
    ValueChanged<GenresModel>? onSelected,
  }) {
    return showBluredBgDailog<void>(
      context,
      SearchGenreBottomSheet(onSelected: onSelected),
      blurValue: 1.5,
    );
  }

  static Future<void> openCreateTomDailog(
    BuildContext context, {
    void Function(TomesModel)? onCreateTomTap,
    bool? isEdit = false,
    TomesModel? tomesModel,
  }) {
    return showBluredBgDailog<void>(
      context,
      CreateTomDailogView(
        onCreateTomTap: onCreateTomTap,
        isEdit: isEdit ?? false,
        tomesModel: tomesModel,
      ),
      blurValue: 1.5,
    );
  }

  static Future<void> openTomesDailog(
    BuildContext context, {
    VoidCallback? onShareTap,
    VoidCallback? onEditTap,
    VoidCallback? onDeleteTap,
  }) {
    return showBluredBgDailog<void>(
      context,
      TomesBottomView(
        onShareTap: onShareTap,
        onEditTap: onEditTap,
        onDeleteTap: onDeleteTap,
      ),
      blurValue: 1.5,
    );
  }

  static Future<void> openTomesDetailsDailog(
    BuildContext context, {
    TomesModel? tomes,
  }) {
    return showBluredBgDailog<void>(
      context,
      TomesDetailsBottomView(
        tomes: tomes,
      ),
      blurValue: 1.5,
    );
  }

  static Future<void> openPostFilterBottomSheet(
    BuildContext context, {
    VoidCallback? onAllPostTap,
    VoidCallback? onMyPostTap,
    VoidCallback? onTribePostTap,
    VoidCallback? onDraftPostTap,
  }) {
    return showBluredBgDailog<void>(
      context,
      PostFilterBottomView(
        onAllPostTap: onAllPostTap,
        onMyPostTap: onMyPostTap,
        onTribePostTap: onTribePostTap,
        onDraftPostTap: onDraftPostTap,
      ),
      blurValue: 1.5,
    );
  }

  static Future<void> selectAvatarPopup(
    BuildContext context, {
    required VoidCallback onGalleryTap,
    required VoidCallback onAvatarTap,
    String defaultAvatar = AppAssets.defaultMan,
  }) {
    return showBluredBgDailog<void>(
      context,
      SelectAvatarPopup(onAvatarTap: onAvatarTap, onGalleryTap: onGalleryTap, defaultAvatar: defaultAvatar),
      blurValue: 1.5,
    );
  }

  static Future<void> discardPostChangesDailog(
    BuildContext context, {
    VoidCallback? onDiscardTap,
    VoidCallback? onSaveTap,
  }) {
    return showBluredBgDailog<void>(
      context,
      DiscardPostChangesDailogView(
        onDiscardTap: () {
          onDiscardTap!();
          Navigator.pop(context);
        },
        onSaveTap: () {
          onSaveTap!();
          Navigator.pop(context);
        },
      ),
      blurValue: 1.5,
    );
  }

  static Future<void> deletePostDailog(
    BuildContext context, {
    VoidCallback? onDeletePostTap,
    VoidCallback? onCancelTap,
  }) {
    return showBluredBgDailog<void>(
      context,
      DeletePostDailogView(
        onDeletePostTap: onDeletePostTap,
        onCancelTap: onCancelTap,
      ),
      blurValue: 1.5,
    );
  }

  static Future<void> deleteTomesDailog(
    BuildContext context, {
    VoidCallback? onDeleteTomesTap,
    VoidCallback? onCancelTap,
    PrimaryLoadingButtonController? isLoading,
  }) {
    return showBluredBgDailog<void>(
      context,
      DeleteTomesDailogView(
        onDeleteTomesTap: onDeleteTomesTap,
        onCancelTap: onCancelTap,
        isLoading: isLoading,
      ),
      blurValue: 1.5,
    );
  }

  static Future<bool?> verifyPasscode(BuildContext context) => showBluredBgDailog<bool?>(
        context,
        const ScreenLock(),
        blurValue: 10,
      );

  static Future<void> discardPostChangesDialog(
    BuildContext context, {
    VoidCallback? onDiscardTap,
    VoidCallback? onSaveTap,
  }) {
    return showBluredBgDailog<void>(
      context,
      DiscardPostChangesView(
        onDiscardTap: onDiscardTap,
        onSaveTap: onSaveTap,
      ),
      blurValue: 1.5,
    );
  }

  static Future<void> deletePostDialog(
    BuildContext context, {
    VoidCallback? onDeletePostTap,
    VoidCallback? onSaveDraft,
  }) {
    return showBluredBgDailog<void>(
      context,
      DeletePostView(
        onDeletePostTap: onDeletePostTap,
        onSaveDraft: onSaveDraft,
      ),
      blurValue: 1.5,
    );
  }

  static Future<bool?> createPseudoProfileBottomSheet(
    BuildContext context, {
    bool isFromBottomNavigation = false,
  }) async {
    return showBluredBgDailog<bool?>(
      context,
      CreatePseudoProfileBottomsheet(isFromBottomNavigation: isFromBottomNavigation),
    );
  }

  static Future<AvatarModel?> selectAvatarBottomSheet(
    BuildContext context, {
    AvatarModel? selectedAvatar,
    bool isFromBottomNavigation = false,
  }) async {
    return showBluredBgDailog<AvatarModel?>(
      context,
      SelectAvatarBottomSheet(
        selectedAvatar: selectedAvatar,
        isFromBottomNavigation: isFromBottomNavigation,
      ),
    );
  }
}
