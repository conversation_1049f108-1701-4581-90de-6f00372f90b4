// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/constants/app_theme.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/utlity/utlity.dart';

class AppTextFormField extends StatelessWidget {
  const AppTextFormField({
    super.key,
    this.controller,
    this.label,
    this.hintText,
    this.focusNode,
    this.validator,
    this.placeholder,
    this.enabled = true,
    this.obscureText = false,
    this.keyboardType,
    this.inputFormatters,
    this.suffixIcon,
    this.prefixIcon,
    this.onChanged,
    this.errorText,
    this.titleColor,
    this.fillColor,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.border,
    this.initialValue,
    this.filled = true,
    this.contentPadding,
    this.isReadOnly = false,
    this.textInputAction,
    this.title,
    this.onTap,
    this.onFieldSubmitted,
    this.onEditingComplete,
    this.isRequired = false,
    this.counterText,
    this.margin = const EdgeInsets.only(top: 8, bottom: 8),
    this.style,
    this.hintStyle,
    this.maxWidth,
    this.obscuringCharacter = '•',
    this.autofillHints,
    this.suffixIconConstraints,
    this.preFixIconConstraints,
    this.textCapitalization,
    this.headerWidgets = const [],
    this.onTapOutside,
    this.autoFocus = false,
    this.isDense = false,
    this.cursorColor,
  });

  final TextEditingController? controller;
  final String? label;
  final String? hintText;
  final FocusNode? focusNode;
  final String? Function(String?)? validator;
  final String? placeholder;
  final bool enabled;
  final bool obscureText;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final ValueChanged<String>? onChanged;
  final String? errorText;
  final Color? fillColor;
  final Color? titleColor;
  final int maxLines;
  final int? minLines;
  final int? maxLength;
  final InputBorder? border;
  final String? initialValue;
  final bool filled;
  final EdgeInsets? contentPadding;
  final bool isReadOnly;
  final TextInputAction? textInputAction;
  final String? title;
  final VoidCallback? onTap;
  final void Function(String)? onFieldSubmitted;
  final void Function()? onEditingComplete;
  final bool isRequired;
  final String? counterText;
  final EdgeInsets margin;
  final TextStyle? style;
  final TextStyle? hintStyle;
  final double? maxWidth;
  final String obscuringCharacter;
  final Iterable<String>? autofillHints;
  final BoxConstraints? suffixIconConstraints;
  final BoxConstraints? preFixIconConstraints;
  final TextCapitalization? textCapitalization;
  final List<WidgetSpan> headerWidgets;
  final void Function(PointerDownEvent)? onTapOutside;
  final bool autoFocus;
  final Color? cursorColor;
  final bool isDense;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (title != null)
          Container(
            constraints: BoxConstraints(maxWidth: maxWidth ?? Utility.maxWidth(context).maxWidth),
            padding: const EdgeInsets.only(left: 0.5),
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: title,
                    style: context.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                      fontFamily: AppTheme.spaceGroteskFontFamily,
                      color: titleColor,
                    ),
                  ),
                  if (isRequired) ...[
                    TextSpan(
                      text: '* ',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            // color: AppColors.redColor,
                          ),
                    ),
                    ...headerWidgets,
                  ],
                ],
              ),
            ),
          ),
        Container(
          margin: margin,
          constraints: BoxConstraints(maxWidth: maxWidth ?? Utility.maxWidth(context).maxWidth),
          child: TextFormField(
            onTapOutside: onTapOutside ??
                (event) {
                  FocusScope.of(context).unfocus();
                },
            onEditingComplete: onEditingComplete,
            onFieldSubmitted: onFieldSubmitted,
            textCapitalization: textCapitalization ?? TextCapitalization.none,
            onTap: onTap,
            minLines: minLines,
            controller: controller,
            readOnly: isReadOnly,
            focusNode: focusNode,
            cursorColor: cursorColor ?? AppColors.text,
            autofillHints: autofillHints,
            style: style ??
                context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w400,
                  color: AppColors.text,
                  fontSize: context.isMobile ? 15 : 18,
                ),
            textInputAction: textInputAction,
            validator: validator,
            inputFormatters: inputFormatters,
            obscureText: obscureText,
            obscuringCharacter: obscuringCharacter,
            onChanged: onChanged,
            keyboardType: keyboardType,
            textAlignVertical: TextAlignVertical.center,
            initialValue: initialValue,
            maxLines: maxLines,
            autofocus: autoFocus,
            maxLength: maxLength,
            decoration: InputDecoration(
              isDense: isDense,
              counter: counterText != null
                  ? Text(
                      counterText!,
                      style: Theme.of(context).inputDecorationTheme.counterStyle,
                    )
                  : null,
              errorText: errorText,
              fillColor: fillColor,
              filled: filled,
              hintStyle: hintStyle ??
                  Theme.of(context).inputDecorationTheme.hintStyle?.copyWith(
                        fontSize: context.isMobile ? null : 18,
                      ),
              errorStyle: Theme.of(context).inputDecorationTheme.errorStyle?.copyWith(
                    fontSize: context.isMobile ? null : 18,
                  ),
              contentPadding: contentPadding,
              enabled: enabled,
              suffixIcon: suffixIcon,
              prefixIcon: prefixIcon,
              hintText: hintText,
              enabledBorder: border,
              disabledBorder: border,
              focusedBorder: border,
              focusedErrorBorder: border,
              errorBorder: border,
              border: border,
              suffixIconConstraints: suffixIconConstraints,
              prefixIconConstraints: preFixIconConstraints,
            ),
          ),
        )
      ],
    );
  }
}
