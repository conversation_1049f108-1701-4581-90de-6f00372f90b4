// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:tynt_web/auth/models/avatar_model.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/popup_wrapper.dart';

class SelectAvatarPopup extends StatelessWidget {
  const SelectAvatarPopup({
    required this.onGalleryTap,
    required this.onAvatarTap,
    super.key,
    this.defaultAvatar = AppAssets.defaultMan,
    AvatarModel? selectedAvatar,
  });
  final VoidCallback onGalleryTap;
  final VoidCallback onAvatarTap;
  final String defaultAvatar;

  @override
  Widget build(BuildContext context) {
    return PopUpWrapper(
      childrenPadding: const EdgeInsets.fromLTRB(20, 20, 20, 30),
      children: [
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              alignment: Alignment.centerRight,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      context.l10n.selectFrom,
                      style: context.textTheme.bodyLarge?.copyWith(
                        fontSize: context.isMobile ? 18 : 24,
                        letterSpacing: 1,
                        fontWeight: FontWeight.w700,
                        color: AppColors.text,
                      ),
                    ),
                  ],
                ),
                InkWell(
                  onTap: () => Navigator.of(context).pop(),
                  child: AppImageAsset(
                    AppAssets.closeIcon,
                    height: context.isMobile ? 24 : 29,
                    width: context.isMobile ? 24 : 29,
                    color: AppColors.text,
                  ),
                ),
              ],
            ),
            Gap(context.isMobile ? 20 : 40),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _SelectImageSubWidget(
                  key: const ValueKey('Gallery'),
                  text: context.l10n.gallery,
                  onTap: () {
                    context.pop();
                    onGalleryTap.call();
                  },
                  child: CircleAvatar(
                    radius: context.isMobile ? 36 : 50,
                    backgroundColor: AppColors.primary,
                    child: AppImageAsset(
                      AppAssets.galleryIcon,
                      height: context.isMobile ? 30 : 60,
                      width: context.isMobile ? 30 : 60,
                    ),
                  ),
                ),
                _SelectImageSubWidget(
                  key: const ValueKey('Avatar'),
                  text: context.l10n.avatar,
                  onTap: () {
                    context.pop();
                    onAvatarTap.call();
                  },
                  child: CircleAvatar(
                    radius: context.isMobile ? 36 : 50,
                    backgroundColor: AppColors.primary,
                    backgroundImage: Image.asset(defaultAvatar).image,
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }
}

class _SelectImageSubWidget extends StatelessWidget {
  const _SelectImageSubWidget({
    required this.child,
    required this.text,
    required this.onTap,
    super.key,
  });
  final Widget child;
  final String text;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: Column(
        children: [
          child,
          const Gap(9),
          Text(
            text,
            style: context.textTheme.bodyMedium?.copyWith(
              fontSize: 15,
              fontWeight: FontWeight.w500,
              color: AppColors.text,
            ),
          ),
        ],
      ),
    );
  }
}
