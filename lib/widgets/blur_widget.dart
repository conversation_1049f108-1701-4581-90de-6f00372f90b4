// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:ui';

import 'package:flutter/material.dart';

class BlurWidget extends StatelessWidget {
  const BlurWidget({
    required this.child,
    super.key,
    this.signmaX = 4,
    this.signmaY = 4,
    this.opacity,
  });
  final double signmaX;
  final double signmaY;
  final Widget child;
  final Animation<double>? opacity;

  @override
  Widget build(BuildContext context) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: signmaX, sigmaY: signmaX),
      child: opacity == null
          ? child
          : FadeTransition(
              opacity: opacity!,
              child: child,
            ),
    );
  }
}
