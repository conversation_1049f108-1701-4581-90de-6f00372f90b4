// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/num_extentions.dart';
import 'package:tynt_web/shimmer/shimmer.dart';
import 'package:tynt_web/shimmer/shimmer_loading.dart';

class AppImageNetwork extends StatelessWidget {
  const AppImageNetwork({
    required this.url,
    super.key,
    this.fit = BoxFit.cover,
    this.height,
    this.width,
    this.color,
    this.imageBuilder,
    this.errorAssetIcon,
    this.loading = const AppImageLoading(),
    this.customErrorWidget,
    this.removeMermoryCache = false,
  });
  final String url;
  final BoxFit fit;
  final double? height;
  final double? width;
  final Color? color;
  final ImageWidgetBuilder? imageBuilder;
  final String? errorAssetIcon;
  final Widget? loading;
  final Widget? customErrorWidget;
  final bool removeMermoryCache;

  @override
  Widget build(BuildContext context) {
    final errorWidget = customErrorWidget ??
        ErrorImageWidget(
          height: height,
          width: width,
          errorAssetIcon: errorAssetIcon,
          fit: fit,
        );
    if (url.trim().isEmpty) {
      return errorWidget;
    }
    return CachedNetworkImage(
      key: ValueKey(url),
      imageUrl: url,
      height: height,
      width: width,
      fit: fit,
      color: color,
      imageBuilder: imageBuilder,
      progressIndicatorBuilder: (context, url, progress) => loading ?? const SizedBox.shrink(),
      errorWidget: (context, url, errorObj) => errorWidget,
      maxHeightDiskCache: removeMermoryCache ? null : ((height ?? 60) * 1.8).toInt(),
      maxWidthDiskCache: removeMermoryCache ? null : ((height ?? 60) * 1.8).toInt(),
      memCacheWidth: removeMermoryCache ? null : width?.cacheSize(context),
      memCacheHeight: removeMermoryCache ? null : height?.cacheSize(context),
    );
  }
}

class AppImageLoading extends StatelessWidget {
  const AppImageLoading({
    super.key,
    this.height,
    this.width,
    this.boxShape = BoxShape.circle,
    this.decoration,
  });
  final double? height;
  final double? width;
  final BoxShape boxShape;
  final Decoration? decoration;

  @override
  Widget build(BuildContext context) {
    return Shimmer(
      child: ShimmerLoading(
        isLoading: true,
        child: Container(
          height: height,
          width: width,
          decoration: decoration ??
              BoxDecoration(
                color: AppColors.white,
                shape: boxShape,
              ),
        ),
      ),
    );
  }
}

class ErrorImageWidget extends StatelessWidget {
  const ErrorImageWidget({
    super.key,
    this.height,
    this.width,
    this.errorAssetIcon,
    this.fit = BoxFit.cover,
  });
  final double? height;
  final double? width;
  final String? errorAssetIcon;
  final BoxFit fit;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppColors.white,
        image: DecorationImage(
          image: Image.asset(
            errorAssetIcon ?? AppAssets.defaultMan,
            height: height,
            width: width,
            fit: fit,
          ).image,
        ),
      ),
    );
  }
}
