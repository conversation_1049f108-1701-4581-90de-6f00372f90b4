// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:animate_do/animate_do.dart';
import 'package:flutter/material.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';

class PopUpWrapper extends StatelessWidget {
  const PopUpWrapper({
    required this.children,
    super.key,
    this.childrenPadding = const EdgeInsets.symmetric(horizontal: 30),
    this.showCancelButton = false,
    this.constraints,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.physics,
    this.scrollController,
  });
  final List<Widget> children;
  final EdgeInsets childrenPadding;
  final bool showCancelButton;
  final BoxConstraints? constraints;
  final CrossAxisAlignment crossAxisAlignment;
  final ScrollPhysics? physics;
  final ScrollController? scrollController;

  @override
  Widget build(BuildContext context) {
    return FadeIn(
      child: Dialog(
        insetPadding: const EdgeInsets.all(20),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        child: ConstrainedBox(
          constraints: constraints ?? const BoxConstraints(maxWidth: 458),
          child: SingleChildScrollView(
            physics: physics,
            controller: scrollController,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (showCancelButton)
                  Align(
                    alignment: Alignment.centerRight,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: InkWell(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: const AppImageAsset(
                          AppAssets.closeIcon,
                          height: 24,
                          width: 24,
                          color: AppColors.text,
                        ),
                      ),
                    ),
                  ),
                Padding(
                  padding: childrenPadding,
                  child: Column(
                    crossAxisAlignment: crossAxisAlignment,
                    children: children,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
