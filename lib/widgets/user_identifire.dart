import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tynt_web/auth/cubit/theme_cubit.dart';
import 'package:tynt_web/utlity/enum/user_type_enum.dart';

// ignore: avoid_positional_boolean_parameters
typedef UserIdentiferWidgetBuilder = Widget Function(BuildContext context, bool isTyntUser);

class UserIdentifer extends StatelessWidget {
  const UserIdentifer({
    required this.builder,
    super.key,
  });
  final UserIdentiferWidgetBuilder builder;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      buildWhen: (previous, current) => previous.currentUserType != current.currentUserType,
      builder: (context, state) => builder(context, state.currentUserType == UserType.tynt),
    );
    // return BlocSelector<AuthenticationBloc, AuthenticationState, bool>(
    //   selector: (state) => state.currentUser?.isPseudoUser ?? false,
    //   builder: builder,
    // );
  }
}
