// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/user/repository/i_user_manager_repository.dart';
import 'package:tynt_web/utlity/extentions/string_extentions.dart';
import 'package:tynt_web/utlity/utlity.dart';
import 'package:tynt_web/widgets/random_text_reveal.dart';

class TyntNameGenerator extends StatefulWidget {
  const TyntNameGenerator({
    required this.onNameChanged,
    super.key,
    this.showEmptyError = false,
    this.isRequired = false,
    this.initialText,
    this.children,
  });
  final ValueChanged<String?> onNameChanged;
  final bool showEmptyError;
  final bool isRequired;
  final String? initialText;
  final List<InlineSpan>? children;

  @override
  State<TyntNameGenerator> createState() => TyntNameGeneratorState();
}

class TyntNameGeneratorState extends State<TyntNameGenerator> {
  final textKey = GlobalKey<RandomTextRevealState>();
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          constraints: BoxConstraints(maxWidth: Utility.maxWidth(context).maxWidth),
          padding: const EdgeInsets.only(left: 0.5),
          child: RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: context.l10n.tyntName,
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.copyWith(fontWeight: FontWeight.w500, fontSize: context.isMobile ? 15 : 18),
                ),
                if (widget.isRequired) ...[
                  TextSpan(
                    text: '* ',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          // color: AppColors.redColor,
                        ),
                  ),
                ],
                const WidgetSpan(child: SizedBox(width: 5)),
                WidgetSpan(
                  alignment: PlaceholderAlignment.middle,
                  child: InkWell(
                    onTap: generateTyntName,
                    borderRadius: BorderRadius.circular(5),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.all(Radius.circular(5)),
                        border: Border.all(color: AppColors.border),
                        color: AppColors.white,
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 3),
                      child: Text(
                        context.l10n.generate,
                        style: context.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: AppColors.primary,
                          fontSize: context.isMobile ? 12 : 14,
                        ),
                      ),
                    ),
                  ),
                ),
                if (widget.children != null) ...widget.children!,
              ],
            ),
          ),
        ),
        Container(
          margin: const EdgeInsets.symmetric(vertical: 8),
          constraints: BoxConstraints(maxWidth: Utility.maxWidth(context).maxWidth),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: AppColors.lightGrey),
            // color: AppColors.white,
          ),
          padding: const EdgeInsets.all(11),
          width: MediaQuery.sizeOf(context).width,
          child: RandomTextReveal(
            key: textKey,
            text: widget.initialText ?? 'JhonSmith',
            initialText: widget.initialText ?? context.l10n.tyntName.inCaps,
            initialTextStyle: widget.initialText == null ? context.theme.inputDecorationTheme.hintStyle : null,
            style: context.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w400,
              // color: AppColors.text,
              fontSize: context.isMobile ? 15 : 18,
            ),
          ),
        ),
        if (widget.showEmptyError) ...[
          const Gap(4),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 11),
            child: Text(
              context.l10n.tyntName.inCaps,
              style: context.textTheme.bodyMedium?.copyWith(
                color: AppColors.red2,
                height: 1,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Future<void> generateTyntName() async {
    textKey.currentState?.playText(newText: widget.initialText ?? 'JhonSmith');
    final failOrSuccess = await getIt<IUserManagerRepository>().generatePseudoName();

    failOrSuccess.fold((l) {
      textKey.currentState?.errorText();
      Utility.toast(message: l.message);
    }, (r) {
      textKey.currentState?.successText(r.data);
      widget.onNameChanged(r.data);
    });
  }
}
