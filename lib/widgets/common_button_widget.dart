// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:tynt_web/extentions/context_extentions.dart';

import '../constants/app_colors.dart';

class CommonButton extends StatelessWidget {
  const CommonButton({
    super.key,
    this.maxHeight = 45,
    required this.buttonText,
    this.isShadow = false,
    this.margin,
    this.padding,
    this.buttonColor,
    this.icon,
    this.borderRadius,
    required this.onTap,
    this.buttonTextColor,
    this.isLoading = false,
    this.enableFlexible = false,
    this.border,
    this.width,
  });

  final double maxHeight;
  final String buttonText;
  final bool isShadow;
  final EdgeInsets? margin;
  final EdgeInsets? padding;
  final Color? buttonColor;
  final IconData? icon;
  final BorderRadiusGeometry? borderRadius;
  final void Function() onTap;
  final Color? buttonTextColor;
  final bool isLoading;
  final bool enableFlexible;
  final BoxBorder? border;
  final double? width;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (isLoading) return;
        onTap();
      },
      overlayColor: WidgetStateProperty.all(Colors.transparent),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          ButtonWrapper(
            enableFlexible: enableFlexible,
            child: Container(
              width: width,
              height: 36,
              alignment: Alignment.center,
              margin: margin ?? const EdgeInsets.only(left: 5, right: 5),
              padding: padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                  borderRadius: borderRadius ?? BorderRadius.circular(4),
                  color: buttonColor ?? AppColors.primary,
                  border: border),
              constraints: enableFlexible && context.isMobile ? const BoxConstraints(maxWidth: 300) : null,
              child: Builder(
                builder: (context) {
                  if (isLoading) {
                    return const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        color: AppColors.white,
                        strokeWidth: 2,
                      ),
                    );
                  }
                  return Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (icon != null) ...[
                        Icon(
                          icon,
                          color: AppColors.white,
                        ),
                      ],
                      Flexible(
                        child: Text(
                          buttonText,
                          textAlign: TextAlign.center,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: buttonTextColor ?? AppColors.white,
                            fontWeight: FontWeight.w300,
                            shadows: [
                              isShadow
                                  ? BoxShadow(
                                      offset: const Offset(1, 1),
                                      color: AppColors.black.withOpacity(0.5),
                                    )
                                  : const BoxShadow()
                            ],
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ButtonWrapper extends StatelessWidget {
  const ButtonWrapper({
    super.key,
    required this.child,
    this.enableFlexible = false,
  });
  final Widget child;
  final bool enableFlexible;

  @override
  Widget build(BuildContext context) {
    if (enableFlexible && context.isMobile) {
      return Flexible(
        child: SizedBox(
          width: MediaQuery.of(context).size.width / 2,
          child: child,
        ),
      );
    }
    return child;
  }
}
