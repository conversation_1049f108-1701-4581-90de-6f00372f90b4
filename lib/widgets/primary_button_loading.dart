// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:tynt_web/constants/app_colors.dart';

class PrimaryButtonLoading extends StatelessWidget {
  const PrimaryButtonLoading({
    this.progressColor = Colors.white,
    super.key,
    this.color,
    this.isLoadingState = false,
  });
  final Color? color;
  final Color progressColor;

  final bool isLoadingState;

  @override
  Widget build(BuildContext context) {
    if (isLoadingState) {
      return Container(
        decoration: BoxDecoration(shape: BoxShape.circle, color: color ?? AppColors.text),
        padding: const EdgeInsets.all(10),
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation(progressColor),
          strokeWidth: 3,
        ),
      );
    }
    return CircularProgressIndicator(
      valueColor: AlwaysStoppedAnimation(progressColor),
    );
  }
}
