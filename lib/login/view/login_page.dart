import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:tynt_web/auth/bloc/authentication_bloc.dart';
import 'package:tynt_web/auth/repository/i_auth_repository.dart';
import 'package:tynt_web/auth/response/login_response.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/extentions/string_extentions.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/local_storage/repository/i_local_storage_repository.dart';
import 'package:tynt_web/primary_button/primary_button.dart';
import 'package:tynt_web/routes/app_routes.dart';
import 'package:tynt_web/utlity/app_validation.dart';
import 'package:tynt_web/utlity/utlity.dart';
import 'package:tynt_web/widgets/app_asset_image.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/app_text_form_field.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final emailController = TextEditingController(text: '<EMAIL>'.isDebugging);
  final passwordController = TextEditingController(text: 'Google@007'.isDebugging);
  final passwordVisbility = ValueNotifier<bool>(true);
  final _formKey = GlobalKey<FormState>();

  final _signInButtonController = PrimaryLoadingButtonController();
  final googleButtonController = PrimaryLoadingButtonController();
  final ValueNotifier<bool> passwordVisibility = ValueNotifier(true);

  @override
  void dispose() {
    _signInButtonController.dispose();
    googleButtonController.dispose();
    passwordController.dispose();
    emailController.dispose();
    passwordVisbility.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Form(
        key: _formKey,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (!context.isTablet && !context.isMobile)
              Expanded(
                child: Stack(
                  children: [
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: Image.asset(
                        AppAssets.login,
                        height: MediaQuery.sizeOf(context).height,
                        alignment: Alignment.bottomCenter,
                      ),
                    ),
                    const Positioned(
                      top: 70,
                      left: 0,
                      right: 0,
                      child: Center(
                        child: AppAssetImage(AppAssets.tyntLoginIcon),
                      ),
                    ),
                  ],
                ),
              ),
            Flexible(
              flex: context.isMobile || context.isTablet ? 1 : 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 40),
                constraints: Utility.maxWidth(context),
                color: context.isMobile || context.isTablet ? null : AppColors.white,
                alignment: Alignment.center,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (context.isMobile || context.isTablet) ...[
                        const AppAssetImage(
                          AppAssets.tyntLoginIcon,
                          height: 130,
                        ),
                        const Gap(30),
                      ],
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          'Login',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                fontSize: 24,
                                fontWeight: FontWeight.w900,
                              ),
                        ),
                      ),
                      const Gap(18),
                      AppTextFormField(
                        title: context.l10n.email,
                        isRequired: true,
                        controller: emailController,
                        keyboardType: TextInputType.emailAddress,
                        textInputAction: TextInputAction.next,
                        validator: (value) => AppValidation.emailValidation(context, email: value),
                        hintText: context.l10n.email,
                      ),
                      const Gap(12),
                      ValueListenableBuilder(
                        valueListenable: passwordVisibility,
                        builder: (_, isObsureText, __) {
                          return AppTextFormField(
                            title: context.l10n.password,
                            hintText: context.l10n.password,
                            isRequired: true,
                            textInputAction: TextInputAction.done,
                            controller: passwordController,
                            validator: (value) => AppValidation.passwordValidation(context, value: value),
                            suffixIconConstraints: const BoxConstraints(),
                            suffixIcon: InkWell(
                              onTap: () {
                                passwordVisibility.value = !passwordVisibility.value;
                              },
                              child: SizedBox(
                                height: 20,
                                child: Padding(
                                  padding: const EdgeInsets.only(right: 16),
                                  child: AppImageAsset(isObsureText ? AppAssets.eyeOffIcon : AppAssets.eyeOnIcon),
                                ),
                              ),
                            ),
                            obscureText: isObsureText,
                          );
                        },
                      ),
                      const Gap(2),
                      Align(
                        alignment: Alignment.centerRight,
                        child: InkWell(
                          onTap: () {},
                          child: Text(
                            'Forgot Password?',
                            style: context.textTheme.bodyMedium?.copyWith(
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      ),
                      const Gap(22),
                      PrimaryButton(
                        primaryLoadingButtonController: _signInButtonController,
                        onPressed: () {
                          if (_formKey.currentState!.validate()) {
                            login();
                          }
                        },
                        text: 'Sign In',
                      ),
                      const Gap(22),
                      PrimaryButton.outline(
                        padding: const EdgeInsets.symmetric(vertical: 10),
                        onPressed: () {
                          _formKey.currentState?.reset();
                          googleSignIn();
                        },
                        primaryLoadingButtonController: googleButtonController,
                        fontSize: 14.5,
                        text: context.l10n.continueWithGoogle,
                        icon: const AppImageAsset(AppAssets.googleIcon),
                      ),
                      // const Gap(31),
                      // RichText(
                      //   text: TextSpan(
                      //     children: [
                      //       TextSpan(
                      //         text: 'Don\'t have an account? ',
                      //         style: context.textTheme.labelMedium?.copyWith(
                      //           fontSize: 14,
                      //           fontWeight: FontWeight.w400,
                      //         ),
                      //       ),
                      //       TextSpan(
                      //         text: 'Sign Up',
                      //         style: context.textTheme.bodyMedium?.copyWith(
                      //           fontSize: 14,
                      //           fontWeight: FontWeight.w500,
                      //         ),
                      //         recognizer: TapGestureRecognizer()..onTap = () {},
                      //       ),
                      //     ],
                      //   ),
                      // )
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> login() async {
    _signInButtonController.start();

    final failOrSucess = await getIt<IAuthRepository>().login(
      email: emailController.text.trim(),
      password: passwordController.text.trim(),
    );
    failOrSucess.fold(
      (l) {
        _signInButtonController.error();
      },
      (r) {
        _signInButtonController.success();

        Future.delayed(const Duration(milliseconds: 820), () {
          if (r.token != null && r.data != null && mounted) {
            getIt<ILocalStorageRepository>().setUserToken(r.token);
            context.read<AuthenticationBloc>().add(CheckAuthentication(user: r.data));
            context.go(AppRoutes.profile.route);
          }
        });
      },
    );
  }

  Future<void> googleSignIn() async {
    googleButtonController.start();
    final failOrSuccess = await getIt<IAuthRepository>().googleSignIn();

    failOrSuccess.fold((l) {
      if (l.status == 444) {
        googleButtonController.stop();
        return;
      }

      googleButtonController.error();
      Utility.toast(message: l.message);
    }, (r) {
      googleButtonController.success();

      Future.delayed(const Duration(milliseconds: 820), () {
        Utility.toast(message: r.message);
        if (r.data != null) {
          _navigateSocialUser(r);
        } else {
          googleButtonController.stop();
        }
      });
    });
  }

  void _navigateSocialUser(LoginResponse? response) {
    if (response == null) return;
    getIt<ILocalStorageRepository>().setUserToken(response.token);
    context.read<AuthenticationBloc>().add(CheckAuthentication(user: response.data));
    context.go(AppRoutes.profile.route);
  }
}
