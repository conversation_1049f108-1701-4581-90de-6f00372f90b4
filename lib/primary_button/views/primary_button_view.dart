// ignore_for_file: prefer_int_literals

import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/constants/app_constants.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/primary_button/utility/primary_button_utlity.dart';
import 'package:tynt_web/primary_button/widgets/primary_button_action_icon.dart';
import 'package:tynt_web/widgets/primary_main_button.dart';

class PrimaryButton extends StatefulWidget {
  const PrimaryButton({
    required this.onPressed,
    required this.text,
    super.key,
    this.primaryLoadingButtonController,
    this.padding = const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
    this.backgroundColor = AppColors.primary,
    this.borderRadius = 100,
    this.textColor = AppColors.white,
    this.width,
    this.height,
    this.icon,
    this.textStyle,
    this.fontSize,
    this.borderColor,
    this.customLoadingWidget = const PrimaryButtonSeconderyLoading(),
    this.isCustomLoading = false,
  });

  const PrimaryButton.outline({
    required this.onPressed,
    required this.text,
    super.key,
    this.primaryLoadingButtonController,
    this.padding = const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
    this.backgroundColor = AppColors.white,
    this.borderRadius = 100,
    this.textColor = AppColors.text,
    this.width,
    this.height,
    this.icon,
    this.borderColor = AppColors.subText,
    this.textStyle,
    this.fontSize,
    this.customLoadingWidget = const PrimaryButtonSeconderyLoading(),
    this.isCustomLoading = false,
  });

  final VoidCallback? onPressed;
  final PrimaryLoadingButtonController? primaryLoadingButtonController;
  final EdgeInsetsGeometry padding;
  final String text;
  final Color backgroundColor;
  final double borderRadius;
  final Color textColor;
  final double? width;
  final double? height;
  final Widget? icon;
  final Color? borderColor;
  final TextStyle? textStyle;
  final double? fontSize;
  final bool isCustomLoading;
  final Widget customLoadingWidget;

  @override
  State<PrimaryButton> createState() => _PrimaryButtonState();
}

class _PrimaryButtonState extends State<PrimaryButton> with TickerProviderStateMixin {
  final GlobalKey _globalKey = GlobalKey();

  late bool isNotStatic = widget.primaryLoadingButtonController != null;
  late bool isStatic = widget.primaryLoadingButtonController == null;

  late final _defaultButtonWidth = context.isMobile ? MediaQuery.sizeOf(context).width : kMaxWidthTablet;
  late final _defaultButtonHeight = context.isMobile ? kButtonHeight : kButtonHeightTablet;

  late double _width = widget.width ?? _defaultButtonWidth;

  late double _borderRadius = widget.borderRadius;

  late final double _height = widget.height ?? _defaultButtonHeight;

  final Duration _duration = const Duration(milliseconds: 250);

  final _buttonState = BehaviorSubject<PrimaryButtonState>.seeded(PrimaryButtonState.idle);

  AnimationController? _buttonController;
  Animation<double>? _buttonAnimation;

  @override
  void initState() {
    super.initState();
    _setButtonControllerValues();
  }

  void _setButtonControllerValues() {
    if (isStatic) return;
    _buttonState.stream.listen((event) {
      if (!mounted) return;
      widget.primaryLoadingButtonController!.state.sink.add(event);
    });

    widget.primaryLoadingButtonController!.addListeners(_start, _stop, _onSucess, _onError, _stop, _emptyState);
  }

  @override
  void dispose() {
    _buttonState.close();
    _buttonAnimation = null;
    _buttonController?.dispose();

    super.dispose();
  }

  void _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isStatic) {
      return SizedBox(
        width: _width,
        height: _height,
        child: Builder(
          builder: (context) {
            if (widget.isCustomLoading) {
              return widget.customLoadingWidget;
            }
            return PrimaryMainButton(
              fontSize: widget.fontSize,
              onPressed: widget.onPressed,
              text: widget.text,
              backgroundColor: widget.backgroundColor,
              textColor: widget.textColor,
              borderRadius: _borderRadius,
              padding: widget.padding,
              textStyle: widget.textStyle,
              icon: widget.icon,
              borderColor: widget.borderColor,
            );
          },
        ),
      );
    }
    return PhysicalModel(
      color: AppColors.transparent,
      borderRadius: BorderRadius.circular(_borderRadius),
      child: SizedBox(
        height: _height,
        child: StreamBuilder(
          stream: _buttonState,
          initialData: PrimaryButtonState.idle,
          builder: (context, snapshot) {
            final state = snapshot.data ?? PrimaryButtonState.idle;
            if (state.isSuccess) {
              return PrimaryButtonActionIcon(
                type: PrimaryButtonActionType.success,
                height: _height,
              );
            }
            if (state.isError) {
              return PrimaryButtonActionIcon(
                type: PrimaryButtonActionType.error,
                height: _height,
              );
            }
            return SizedBox(
              key: _globalKey,
              width: _width,
              child: PrimaryMainButton(
                onPressed: state.isIdle ? widget.onPressed : () {},
                text: widget.text,
                icon: widget.icon,
                fontSize: widget.fontSize,
                textStyle: widget.textStyle,
                backgroundColor: widget.backgroundColor,
                textColor: widget.textColor,
                borderRadius: _borderRadius,
                borderColor: widget.borderColor,
                padding: widget.padding,
                isLoading: state.isLoading && _width < 100,
              ),
            );
          },
        ),
      ),
    );
  }

  void _setControllervalues() {
    final initialWidth = _globalKey.currentContext!.size!.width;
    final initialBorderRadius = widget.borderRadius;
    final targetWidth = _height;
    final targetBorderRadius = _height / 2;
    _buttonController = AnimationController(duration: _duration, vsync: this);
    _buttonAnimation = Tween(begin: 0.0, end: 1.0).animate(_buttonController!)
      ..addListener(() {
        _width = initialWidth - ((initialWidth - targetWidth) * _buttonAnimation!.value);
        _borderRadius = initialBorderRadius - ((initialBorderRadius - targetBorderRadius) * _buttonAnimation!.value);
        _notify();
      });
    _buttonState.value = PrimaryButtonState.loading;
    _buttonController?.forward();
  }

  void _start() {
    if (!mounted || isStatic || _buttonState.value.isLoading) return;
    _setControllervalues();
  }

  void _stop() {
    if (!mounted || isStatic) return;
    _buttonController?.reverse();
    _buttonState.value = PrimaryButtonState.idle;
  }

  void _onSucess() {
    _buttonState.value = PrimaryButtonState.success;
  }

  void _onError() {
    _buttonState.value = PrimaryButtonState.error;
    Future.delayed(const Duration(seconds: 2), _stop);
  }

  void _emptyState() {}
}
