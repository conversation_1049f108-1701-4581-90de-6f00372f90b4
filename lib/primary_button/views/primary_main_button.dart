// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/widgets/primary_button_loading.dart';

class PrimaryMainButton extends StatelessWidget {
  const PrimaryMainButton({
    required this.onPressed,
    required this.text,
    super.key,
    this.padding = const EdgeInsets.symmetric(vertical: 14, horizontal: 20),
    this.isLoading = false,
    this.borderRadius = 8,
    this.backgroundColor = AppColors.text,
    this.textColor = AppColors.white,
    this.icon,
    this.borderColor,
    this.textStyle,
    this.fontSize,
  });
  final VoidCallback? onPressed;
  final EdgeInsetsGeometry padding;
  final bool isLoading;
  final String text;
  final double borderRadius;
  final Color backgroundColor;
  final Color textColor;
  final Widget? icon;
  final Color? borderColor;
  final TextStyle? textStyle;
  final double? fontSize;

  @override
  Widget build(BuildContext context) {
    final buttonStyle = ElevatedButton.styleFrom(
      padding: isLoading ? EdgeInsets.zero : padding,
      backgroundColor: backgroundColor,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        side: BorderSide(color: borderColor ?? AppColors.transparent),
      ),
    );
    final child = Builder(
      builder: (context) {
        if (isLoading) {
          return PrimaryButtonLoading(
            color: backgroundColor,
            progressColor: textColor,
            isLoadingState: isLoading,
          );
        }

        return Text(
          text,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: textStyle ??
              context.textTheme.bodyLarge?.copyWith(
                color: textColor,
                fontSize: fontSize ?? (context.isMobile ? 17 : 20),
                fontWeight: FontWeight.w500,
              ),
        );
      },
    );
    if (icon != null) {
      return ElevatedButton(
        style: buttonStyle,
        onPressed: onPressed,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (!isLoading) ...[
              Flexible(child: icon!),
              const Gap(12),
            ],
            Flexible(flex: 2, child: child),
          ],
        ),
      );
    }
    return ElevatedButton(
      style: buttonStyle,
      onPressed: onPressed,
      child: child,
    );
  }
}

class PrimaryButtonSeconderyLoading extends StatelessWidget {
  const PrimaryButtonSeconderyLoading({
    this.color = AppColors.black,
    super.key,
  });
  final Color color;

  @override
  Widget build(BuildContext context) {
    return CupertinoActivityIndicator(color: color);
  }
}
