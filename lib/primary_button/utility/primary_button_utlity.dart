import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';

enum PrimaryButtonState {
  idle,
  loading,
  success,
  error,
}

enum PrimaryButtonActionType {
  success,
  error,
}

class PrimaryLoadingButtonController {
  VoidCallback? _startListener;
  VoidCallback? _stopListener;
  VoidCallback? _successListener;
  VoidCallback? _errorListener;
  VoidCallback? _resetListener;
  VoidCallback? _disposeListner;

  void addListeners(
    VoidCallback startListener,
    VoidCallback stopListener,
    VoidCallback successListener,
    VoidCallback errorListener,
    VoidCallback resetListener,
    VoidCallback disposeListner,
  ) {
    _startListener = startListener;
    _stopListener = stopListener;
    _successListener = successListener;
    _errorListener = errorListener;
    _resetListener = resetListener;
    _disposeListner = disposeListner;
  }

  final BehaviorSubject<PrimaryButtonState> state = BehaviorSubject<PrimaryButtonState>.seeded(PrimaryButtonState.idle);

  /// A read-only stream of the button state
  Stream<PrimaryButtonState> get stateStream => state.stream;

  /// Gets the current state
  PrimaryButtonState get currentState => state.value;

  void start() {
    _startListener?.call();
  }

  /// Notify listeners to start the stop animation
  void stop() {
    _stopListener?.call();
  }

  /// Notify listeners to start the success animation
  void success() {
    _successListener?.call();
  }

  /// Notify listeners to start the error animation
  void error() {
    _errorListener?.call();
  }

  void dispose() {
    state.close();
    _disposeListner?.call();
  }

  /// Notify listeners to start the reset animation
  void reset() {
    _resetListener?.call();
  }
}

extension PrimaryButtonStateExtention on PrimaryButtonState {
  bool get isLoading => this == PrimaryButtonState.loading;
  bool get isError => this == PrimaryButtonState.error;
  bool get isSuccess => this == PrimaryButtonState.success;
  bool get isIdle => this == PrimaryButtonState.idle;
}

extension PrimaryButtonActionTypeExtention on PrimaryButtonActionType {
  bool get isError => this == PrimaryButtonActionType.error;
  bool get isSuccess => this == PrimaryButtonActionType.success;
}
