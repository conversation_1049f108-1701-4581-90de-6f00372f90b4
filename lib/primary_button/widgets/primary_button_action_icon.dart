// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/primary_button/utility/primary_button_utlity.dart';

class PrimaryButtonActionIcon extends StatefulWidget {
  const PrimaryButtonActionIcon({
    required this.type,
    required this.height,
    super.key,
    this.successColor = Colors.green,
    this.errorColor = Colors.red,
    this.successIconColor = AppColors.white,
    this.errorIconColor = AppColors.white,
  });
  final Color successColor;
  final Color errorColor;
  final PrimaryButtonActionType type;
  final Color successIconColor;
  final Color errorIconColor;
  final double height;

  @override
  State<PrimaryButtonActionIcon> createState() => PrimaryButtonActionIconState();
}

class PrimaryButtonActionIconState extends State<PrimaryButtonActionIcon> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _bounceAnimation;

  @override
  void initState() {
    super.initState();
    initValues();
  }

  void initValues() {
    _controller = AnimationController(duration: const Duration(milliseconds: 800), vsync: this);
    _bounceAnimation = Tween<double>(begin: 0, end: widget.height).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.elasticOut,
      )..addListener(_notify),
    );
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: FractionalOffset.center,
      decoration: BoxDecoration(
        color: widget.type.isSuccess ? widget.successColor : widget.errorColor,
        borderRadius: BorderRadius.all(Radius.circular(_bounceAnimation.value / 2)),
      ),
      width: _bounceAnimation.value,
      height: _bounceAnimation.value,
      child: _bounceAnimation.value > 20
          ? Builder(
              builder: (context) {
                if (widget.type.isSuccess) {
                  return Icon(
                    Icons.check,
                    color: widget.successIconColor,
                  );
                }
                return Icon(
                  Icons.close,
                  color: widget.errorIconColor,
                );
              },
            )
          : null,
    );
  }
}
