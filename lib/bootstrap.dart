import 'dart:async';
import 'dart:developer';

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:tynt_web/injector/injector.dart';

Future<void> bootstrap(Widget builder) async {
  Zone.current.fork().runGuarded(() async {
    WidgetsFlutterBinding.ensureInitialized();
    await Firebase.initializeApp(
      options: const FirebaseOptions(
          apiKey: 'AIzaSyC2rrw3DkUUCI9ktvi6bCDo9nzQx6uGq58',
          authDomain: 'taletalk-f154f-c5e32.firebaseapp.com',
          projectId: 'taletalk-f154f',
          storageBucket: 'taletalk-f154f.firebasestorage.app',
          messagingSenderId: '635713110257',
          appId: '1:635713110257:web:1d6ff0ddf7f3fb84df6950',
          measurementId: 'G-10P03SZV6K'),
    );

    await configureDependencies();
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.dark,
      ),
    );
    // Lock orientation to portrait mode
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    GoRouter.optionURLReflectsImperativeAPIs = true;

    runApp(builder);

    FlutterError.onError = (details) {
      log(details.exceptionAsString(), stackTrace: details.stack);
    };
  });
}
