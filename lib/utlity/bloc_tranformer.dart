import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rxdart/rxdart.dart';

EventTransformer<T> searchTransformer<T>({Duration? duration}) {
  return (events, mapper) => events.debounceTime(duration ?? const Duration(milliseconds: 500)).flatMap(mapper);
}

EventTransformer<T> loadMoreTransformer<T>({Duration? duration}) {
  return (events, mapper) => events.debounceTime(duration ?? const Duration(milliseconds: 500)).flatMap(mapper);
}

EventTransformer<T> delayTransformer<T>({int? milliseconds}) {
  return (events, mapper) => events.debounceTime(Duration(milliseconds: milliseconds ?? 600)).flatMap(mapper);
}
