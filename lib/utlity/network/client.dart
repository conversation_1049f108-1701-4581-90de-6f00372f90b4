import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:tynt_web/local_storage/repository/i_local_storage_repository.dart';
import 'package:tynt_web/utlity/app_typedefs/http_typedef.dart';

abstract class Client {
  const Client(this.localStorageRepository);
  final ILocalStorageRepository localStorageRepository;

  ClientResult get({
    required String url,
    DynamicMapResult? params,
    StringMapResult? headers,
  });

  ClientResult getPublic({
    required String url,
    DynamicMapResult params,
    StringMapResult headers,
  });

  ClientResult post({
    required String url,
    DynamicMapResult params,
    StringMapResult headers,
    DynamicMapResult requests,
  });

  ClientResult delete({
    required String url,
    DynamicMapResult params,
    StringMapResult headers,
  });

  ClientResult multipart({
    required String url,
    String method = 'POST',
    DynamicMapResult params,
    StringMapResult headers,
    StringMapResult requests,
    List<MapEntry<String, File>> files,
    List<MapEntry<String, Uint8List>> webFiles,
  });
}
