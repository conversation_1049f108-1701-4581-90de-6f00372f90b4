import 'package:flutter/material.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';

enum Gender {
  male('MALE'),
  female('FEMALE'),
  other('OTHER');

  const Gender(this.name);
  final String name;
}

extension GenderExtentions on Gender {
  String formattedName(BuildContext context) {
    switch (this) {
      case Gender.male:
        return context.l10n.male;
      case Gender.female:
        return context.l10n.female;
      case Gender.other:
        return context.l10n.other;
    }
  }
}
