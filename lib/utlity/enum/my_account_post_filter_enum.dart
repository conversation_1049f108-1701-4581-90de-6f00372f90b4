import 'package:flutter/material.dart';
import 'package:tynt_web/constants/app_strings.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';

enum MyAccountPostFilterType {
  all,
  publicPosts,
  tribe,
  draftPosts;
}

extension MyAccountPostFilterTypeExtention on MyAccountPostFilterType {
  String label(BuildContext context) {
    switch (this) {
      case MyAccountPostFilterType.all:
        return context.l10n.all;
      case MyAccountPostFilterType.publicPosts:
        return context.l10n.public;
      case MyAccountPostFilterType.tribe:
        return context.l10n.tribe;

      case MyAccountPostFilterType.draftPosts:
        return context.l10n.draftPosts;
    }
  }

  String? get postFilter {
    if (isNormalPosts) {
      return AppStrings.normal;
    }
    if (isTribePosts) {
      return AppStrings.community;
    }

    return null;
  }

  bool get isAll => this == MyAccountPostFilterType.all;

  bool get isNormalPosts => this == MyAccountPostFilterType.publicPosts;

  bool get isTribePosts => this == MyAccountPostFilterType.tribe;

  bool get isDraftPosts => this == MyAccountPostFilterType.draftPosts;
}
