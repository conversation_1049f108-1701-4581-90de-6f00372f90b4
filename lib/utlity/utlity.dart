import 'package:flutter/material.dart';
import 'package:flutter_quill/quill_delta.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:responsive_builder/responsive_builder.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/constants/app_constants.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/local_storage/repository/i_local_storage_repository.dart';

class Utility {
  static BoxConstraints maxWidth(BuildContext context) {
    return BoxConstraints(
      maxWidth: context.isMobile ? kMaxWidth : kMaxWidthTablet,
    );
  }

  static void toast({required String? message, Color? color}) {
    if (message == null || message.trim().isEmpty) return;

    final bg = color ?? AppColors.primary;
    final hex = '#${bg.value.toRadixString(16).substring(2)}'; // Convert to hex without alpha

    Fluttertoast.showToast(
      msg: message,
      backgroundColor: bg, // For mobile
      webBgColor: hex, // For web (solid color, no gradient)
      textColor: Colors.white, // Text color for contrast
      gravity: ToastGravity.BOTTOM,
    );
  }

  static EdgeInsetsGeometry listCommonpadding(BuildContext context, {bool removeBottomPadding = false}) {
    return EdgeInsets.fromLTRB(
      16,
      20,
      16,
      removeBottomPadding ? 0 : (context.isMobile ? kBottomPadding : kBottomPaddingTablet),
    );
  }

  static EdgeInsetsGeometry listNewCommonpadding(BuildContext context, {bool removeBottomPadding = false}) {
    return EdgeInsets.fromLTRB(
      0,
      20,
      0,
      removeBottomPadding ? 0 : (context.isMobile ? kBottomPadding : kBottomPaddingTablet),
    );
  }

  static EdgeInsetsGeometry listHomePadding(BuildContext context) {
    return EdgeInsets.fromLTRB(
      16,
      0,
      16,
      context.isMobile ? kBottomPadding : kBottomPaddingTablet,
    );
  }

  static String getTimeAgo(DateTime date) {
    return timeago.format(date, locale: getIt<ILocalStorageRepository>().getLanguage);
  }

  static double otherAccountExpandedBackgoundImageHeight(BuildContext context) {
    final adjustMobileHeight = getValueForRefinedSize(
      context: context,
      normal: kOtherAccountExpandedBackgoundImageHeight,
      large: kOtherAccountExpandedBackgoundImageHeight,
    );
    return context.isMobile ? adjustMobileHeight : kOtherAccountExpandedBackgoundImageHeightTablet;
  }

  static List<String> getAllHashTag(String text) {
    final list = <String>[];
    final exp = RegExp(r'\B#\w\w+');
    exp.allMatches(text).forEach((match) {
      if (match.group(0) != null && match.group(0)!.trim().isNotEmpty) {
        list.add(match.group(0)!.replaceAll('#', ''));
      }
    });
    return list;
  }

  static String formatNumber(int number) {
    final isGreaterthanMillion = number >= 1000000;
    final isGreaterthanThousand = number >= 1000;
    if (isGreaterthanMillion) {
      final result = number / 1000000;
      if (result % 1 != 0) {
        return '${result.toStringAsFixed(1)}M';
      }
      return '${result.round()}M';
    }
    if (isGreaterthanThousand) {
      return '${number ~/ 1000}k';
    }
    return number.toString();
  }

  static String formmtedDate(DateTime date, BuildContext context) {
    final currentDate = DateTime.now();
    final yesterDate = DateTime.now().subtract(const Duration(days: 1));
    if (currentDate.year == date.year && currentDate.month == date.month && currentDate.day == date.day) {
      return 'Today';
    }
    if (yesterDate.year == date.year && yesterDate.month == date.month && (yesterDate.day) == date.day) {
      return 'Yesterday';
    }
    return DateFormat('MMM dd, yyyy').format(date);
  }

  static String formmtedMinute(Duration duration) {
    return '${duration.inMinutes.toString().padLeft(2, '0')}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}';
  }

  static AnimationStyle get popupMenuAniationStyle {
    return const AnimationStyle(
      curve: Curves.easeInQuad,
      duration: Duration(milliseconds: 650),
    );
  }

  static String convertDeltaToHtml(Delta delta) {
    final buffer = StringBuffer();
    final ops = delta.toList();

    const blockquoteStyle = 'border-left: 4px solid #ccc; padding-left: 1em; margin: 0.5em 0; color: #666;';

    final lineBuffer = StringBuffer();
    var lineAttrs = <String, dynamic>{};

    for (var i = 0; i < ops.length; i++) {
      final op = ops[i];
      if (op.data is! String) continue;

      final text = op.data! as String;
      final attrs = op.attributes ?? {};
      final parts = text.split('\n');

      for (var j = 0; j < parts.length; j++) {
        final part = parts[j];
        final styledText = _wrapWithStyle(_escapeHtml(part), attrs);
        lineBuffer.write(styledText);

        final isLineEnd = j < parts.length - 1;
        if (isLineEnd) {
          lineAttrs = attrs;
          final lineHtml = lineBuffer.isEmpty ? '<br>' : lineBuffer.toString();

          if (lineAttrs.containsKey('blockquote')) {
            buffer.writeln('<blockquote style="$blockquoteStyle">$lineHtml</blockquote>');
          } else {
            buffer.writeln('<div>$lineHtml</div>');
          }

          lineBuffer.clear();
        }
      }
    }

    return buffer.toString();
  }

  static String _wrapWithStyle(String text, Map<String, dynamic> attrs) {
    if (attrs.isEmpty) return text;
    if (attrs['link'] != null) text = '<a href="${attrs['link']}">$text</a>';
    if (attrs['code'] == true) text = '<code>$text</code>';
    if (attrs['bold'] == true) text = '<strong>$text</strong>';
    if (attrs['italic'] == true) text = '<em>$text</em>';
    if (attrs['underline'] == true) text = '<u>$text</u>';
    if (attrs['strike'] == true) text = '<s>$text</s>';
    return text;
  }

  static String _escapeHtml(String text) {
    return text
        .replaceAll('&', '&amp;')
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;')
        .replaceAll('"', '&quot;')
        .replaceAll("'", '&#39;');
  }
}
