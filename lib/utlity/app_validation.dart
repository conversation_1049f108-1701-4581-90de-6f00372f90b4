import 'package:flutter/material.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';

class AppValidation {
  static String? emailValidation(
    BuildContext context, {
    required String? email,
  }) {
    if (email == null || email.trim().isEmpty) {
      return context.l10n.emptyEmailMsg;
    }
    if (!RegExp(
      r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$',
    ).hasMatch(email)) {
      return context.l10n.errorEmailMsg;
    }
    return null;
  }

  static String? passwordValidation(
    BuildContext context, {
    required String? value,
  }) {
    if (value == null || value.trim().isEmpty) {
      return context.l10n.emptyPasswordMsg;
    }
    if (value.length < 6) {
      return context.l10n.errorPasswordMsg;
    }
    return null;
  }

  static String? tomDetailsValidation(
    BuildContext context, {
    required String? value,
  }) {
    if (value == null || value.trim().isEmpty) {
      return context.l10n.emptyTomDetailsMsg;
    }
    return null;
  }

  static String? pseudoPasscodeValidation(
    BuildContext context, {
    required String? value,
    required String? pseudoNameValue,
    bool isRequired = false,
  }) {
    if (!isRequired && (pseudoNameValue == null || pseudoNameValue.trim().isEmpty)) {
      return null;
    }
    if (isRequired && (pseudoNameValue == null || pseudoNameValue.trim().isEmpty)) {
      return context.l10n.errorTyntPasscodeMsg;
    }
    if ((value?.length ?? 0) < 4) {
      return context.l10n.errorTyntPasscodeMsg;
    }
    return null;
  }

  static String? firstNameValidation(
    BuildContext context, {
    required String? value,
  }) {
    if (value == null || value.trim().isEmpty) {
      return context.l10n.emptyFirstNameMsg;
    }
    return null;
  }

  static String? lastNameValidation(
    BuildContext context, {
    required String? value,
  }) {
    if (value == null || value.trim().isEmpty) {
      return context.l10n.emptyLastNameMsg;
    }
    return null;
  }

  static String? userNameValidationRequired(
    BuildContext context, {
    required String? value,
  }) {
    if (value == null || value.trim().isEmpty) {
      return context.l10n.userNameEmptyError;
    }
    if (!RegExp(r'^(?!.*\.\.\@)?\w[\w\.]{1,28}\w$').hasMatch(value.trim())) {
      return context.l10n.userNameInvalidError;
    }
    return null;
  }

  // static String? currentPasswordValidation(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyCurrentPasswordMsg;
  //   }
  //   if (value.length < 6) {
  //     return context.l10n.errorPasswordMsg;
  //   }
  //   return null;
  // }

  // static String? newPasswordValidation(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyNewPasswordMsg;
  //   }
  //   if (value.length < 6) {
  //     return context.l10n.errorPasswordMsg;
  //   }
  //   return null;
  // }

  // static String? confirmPasswordValidation(
  //   BuildContext context, {
  //   required String? value,
  //   required String newPassword,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyConfirmPasswordMsg;
  //   }
  //   if (value.length < 6) {
  //     return context.l10n.errorPasswordMsg;
  //   }
  //   if (value != newPassword) {
  //     return context.l10n.errorPasswordNotMatchMsg;
  //   }
  //   return null;
  // }

  // static String? currentPasscodeValidation(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyCurrentPasscodeMsg;
  //   }
  //   if (value.length < 4) {
  //     return context.l10n.errorPasscodeMsg;
  //   }
  //   return null;
  // }

  // static String? newPasscodeValidation(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyNewPasscodeMsg;
  //   }
  //   if (value.length < 4) {
  //     return context.l10n.errorPasscodeMsg;
  //   }
  //   return null;
  // }

  // static String? confirmPasscodeValidation(
  //   BuildContext context, {
  //   required String? value,
  //   required String newPasscode,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyConfirmPasscodeMsg;
  //   }
  //   if (value.length < 4) {
  //     return context.l10n.errorPasscodeMsg;
  //   }
  //   if (value != newPasscode) {
  //     return context.l10n.errorPasscodeNotMatchMsg;
  //   }
  //   return null;
  // }

  // static String? firstNameValidation(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyFirstNameMsg;
  //   }
  //   return null;
  // }

  // static String? lastNameValidation(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyLastNameMsg;
  //   }
  //   return null;
  // }

  // static String? businessNameValidation(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyBusinessNameMsg;
  //   }
  //   if (!RegExp(r'^(?!.*\.\.\@)?\w[\w\.]{1,28}\w$').hasMatch(value.trim())) {
  //     return context.l10n.errorBusinessNameMsg;
  //   }
  //   return null;
  // }

  // static String? pseudoNameValidation(
  //   BuildContext context, {
  //   required String? value,
  //   required String? pseudoPasscodeValue,
  // }) {
  //   if ((pseudoPasscodeValue == null || pseudoPasscodeValue.trim().isEmpty) &&
  //       (value == null || value.trim().isEmpty)) {
  //     return null;
  //   }
  //   if (!RegExp(r'^(?!.*\.\.\@)?\w[\w\.]{1,28}\w$').hasMatch(value?.trim() ?? '')) {
  //     return context.l10n.errorTyntNameMsg;
  //   }
  //   return null;
  // }

  // static String? pseudoPasscodeValidation(
  //   BuildContext context, {
  //   required String? value,
  //   required String? pseudoNameValue,
  //   bool isRequired = false,
  // }) {
  //   if (!isRequired && (pseudoNameValue == null || pseudoNameValue.trim().isEmpty)) {
  //     return null;
  //   }
  //   if (isRequired && (pseudoNameValue == null || pseudoNameValue.trim().isEmpty)) {
  //     return context.l10n.errorTyntPasscodeMsg;
  //   }
  //   if ((value?.length ?? 0) < 4) {
  //     return context.l10n.errorTyntPasscodeMsg;
  //   }
  //   return null;
  // }

  // static String? pseudoNameValidationRequired(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyTyntNameMsg;
  //   }
  //   if (!RegExp(r'^(?!.*\.\.\@)?\w[\w\.]{1,28}\w$').hasMatch(value.trim())) {
  //     return context.l10n.errorTyntNameMsg;
  //   }
  //   return null;
  // }

  // static String? userNameValidationRequired(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.userNameEmptyError;
  //   }
  //   if (!RegExp(r'^(?!.*\.\.\@)?\w[\w\.]{1,28}\w$').hasMatch(value.trim())) {
  //     return context.l10n.userNameInvalidError;
  //   }
  //   return null;
  // }

  // static String? tynsNameValidation(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyTynsNameMsg;
  //   }
  //   return null;
  // }

  // static String? genersValidation(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyGenresMsg;
  //   }
  //   return null;
  // }

  // static String? tynsDescriptionValidation(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyTynsDescriptionMsg;
  //   }
  //   return null;
  // }

  // static String? bioValidation(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyBioMsg;
  //   }
  //   return null;
  // }

  // static String? pinCodeValidation(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyPinCodeMsg;
  //   } else if (value.length < 4) {
  //     return context.l10n.errorPincodeMsg;
  //   }
  //   return null;
  // }

  static String? titleValidation(
    BuildContext context, {
    required String? value,
  }) {
    if (value == null || value.trim().isEmpty) {
      return context.l10n.emptyTitleMsg;
    } else {
      return null;
    }
  }

  // static String? descritionValidation(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyDescriptionMsg;
  //   }
  //   return null;
  // }

  // static String? tagsValidation(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyTagMsg;
  //   } else {
  //     return null;
  //   }
  // }

  // static String? phoneNumberValidation(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyPhoneNumberMsg;
  //   }
  //   return null;
  // }

  // static String? countryValidation(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyCountryMsg;
  //   }
  //   return null;
  // }

  // static String? stateValidation(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyStateMsg;
  //   }
  //   return null;
  // }

  // static String? messageValidation(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyMessage;
  //   }
  //   return null;
  // }

  static String? tomNameValidation(
    BuildContext context, {
    required String? value,
  }) {
    if (value == null || value.trim().isEmpty) {
      return context.l10n.emptyTomNameMsg;
    }
    return null;
  }

  // static String? tomDetailsValidation(
  //   BuildContext context, {
  //   required String? value,
  // }) {
  //   if (value == null || value.trim().isEmpty) {
  //     return context.l10n.emptyTomDetailsMsg;
  //   }
  //   return null;
  // }
}
