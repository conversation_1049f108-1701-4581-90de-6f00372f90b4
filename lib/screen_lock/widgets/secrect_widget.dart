// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:async';

import 'package:animate_do/animate_do.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';

class SecretWithShakingAnimation extends StatefulWidget {
  const SecretWithShakingAnimation({
    required this.length,
    required this.input,
    required this.verifyStream,
    super.key,
  });
  final int length;
  final ValueListenable<String> input;
  final Stream<bool> verifyStream;

  @override
  State<SecretWithShakingAnimation> createState() => _SecretWithShakingAnimationState();
}

class _SecretWithShakingAnimationState extends State<SecretWithShakingAnimation> with SingleTickerProviderStateMixin {
  late Animation<Offset> _animation;
  late AnimationController _animationController;
  late StreamSubscription<bool> _verifySubscription;

  @override
  void initState() {
    super.initState();

    _verifySubscription = widget.verifyStream.listen((valid) {
      if (!valid) {
        _animationController.forward();
      }
    });

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _animation = _animationController
        .drive(CurveTween(curve: Curves.elasticIn))
        .drive(Tween<Offset>(begin: Offset.zero, end: const Offset(0.09, 0)))
      ..addListener(() => setState(() {}))
      ..addStatusListener(
        (status) {
          if (status == AnimationStatus.completed) {
            _animationController.reverse();
          }
        },
      );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _verifySubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _animation,
      child: StreamBuilder<bool>(
        stream: widget.verifyStream,
        initialData: true,
        builder: (context, snapshot) {
          return ScrectsList(
            input: widget.input,
            isError: !(snapshot.data ?? true),
            length: widget.length,
          );
        },
      ),
    );
  }
}

class ScrectsList extends StatelessWidget {
  const ScrectsList({
    required this.input,
    required this.length,
    super.key,
    this.isError = false,
  });

  final ValueListenable<String> input;
  final int length;
  final bool isError;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<String>(
      valueListenable: input,
      builder: (context, value, child) => Padding(
        padding: const EdgeInsets.only(top: 20, bottom: 50),
        child: Wrap(
          spacing: context.isMobile ? 12 : 18,
          children: List.generate(
            length,
            (index) {
              if (value.isEmpty) {
                return Secret(isInvalid: isError);
              }
              return Secret(enabled: index < value.length, isInvalid: isError);
            },
            growable: false,
          ),
        ),
      ),
    );
  }
}

class Secret extends StatelessWidget {
  const Secret({
    super.key,
    this.isInvalid = false,
    this.enabled = false,
  });
  final bool isInvalid;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    final size = context.isMobile ? 20.0 : 32.0;
    if (enabled) {
      return Pulse(
        duration: const Duration(milliseconds: 600),
        child: Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isInvalid ? Colors.red : AppColors.rangoonGreen.withOpacity(0.75),
          ),
          width: size,
          height: size,
        ),
      );
    }
    return ZoomIn(
      duration: const Duration(milliseconds: 600),
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.transparent,
          border: Border.all(color: AppColors.rangoonGreen.withOpacity(0.75)),
        ),
        width: size,
        height: size,
      ),
    );
  }
}
