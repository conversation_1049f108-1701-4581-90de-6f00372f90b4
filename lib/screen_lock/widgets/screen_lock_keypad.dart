// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/screen_lock/controller/screen_lock_input_controller.dart';

const List<String> _numbers = [
  '0',
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '8',
  '9',
];

class ScreenLockKeypad extends StatelessWidget {
  const ScreenLockKeypad({
    required this.inputState,
    super.key,
    this.enabled = true,
  });
  final bool enabled;
  final ScreenLockInputController inputState;

  Widget _generateRow(BuildContext context, int rowNumber) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        final number = (rowNumber - 1) * 3 + index + 1;
        final input = _numbers[number];

        return ScreenLockKeypadButton(
          onPressed: enabled ? () => inputState.addCharacter(input) : null,
          text: input,
        );
      }),
    );
  }

  Widget _generateLastRow(BuildContext context) {
    final input = _numbers[0];

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ScreenLockKeypadButton.transparent(onPressed: null, child: const SizedBox.shrink()),
        ScreenLockKeypadButton(
          onPressed: enabled ? () => inputState.addCharacter(input) : null,
          text: input,
        ),
        ScreenLockKeypadButton.transparent(
          onPressed: enabled ? inputState.removeCharacter : null,
          key: const ValueKey('Cancel_Button'),
          child: Icon(
            Icons.backspace,
            color: AppColors.rangoonGreen.withOpacity(0.5),
            size: 30,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _generateRow(context, 1),
        _generateRow(context, 2),
        _generateRow(context, 3),
        _generateLastRow(context),
      ],
    );
  }
}

class ScreenLockKeypadButton extends StatelessWidget {
  const ScreenLockKeypadButton({
    required this.onPressed,
    super.key,
    this.child,
    this.backgroundColor,
    this.foregroundColor,
    this.text,
  });
  final Widget? child;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final String? text;

  factory ScreenLockKeypadButton.transparent({
    required VoidCallback? onPressed,
    Key? key,
    Widget? child,
  }) =>
      ScreenLockKeypadButton(
        key: key,
        onPressed: onPressed,
        backgroundColor: AppColors.transparent,
        foregroundColor: AppColors.transparent,
        child: child,
      );

  @override
  Widget build(BuildContext context) {
    final size = context.isMobile ? 66.0 : 80.0;
    return Container(
      height: size,
      width: size,
      margin: const EdgeInsets.all(10),
      child: OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          foregroundColor: foregroundColor ?? AppColors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          backgroundColor: backgroundColor ?? AppColors.rangoonGreen.withOpacity(0.5),
        ).copyWith(side: const WidgetStatePropertyAll(BorderSide(width: 0, color: AppColors.transparent))),
        child: child ??
            Text(
              text ?? '',
              style: context.textTheme.headlineMedium?.copyWith(
                fontSize: 36,
                color: AppColors.white,
              ),
            ),
      ),
    );
  }
}
