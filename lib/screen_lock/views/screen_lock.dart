// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:animate_do/animate_do.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:tynt_web/auth/repository/i_auth_repository.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/colors_extnetions.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/local_storage/repository/i_local_storage_repository.dart';
import 'package:tynt_web/screen_lock/controller/screen_lock_input_controller.dart';
import 'package:tynt_web/screen_lock/widgets/screen_lock_keypad.dart';
import 'package:tynt_web/screen_lock/widgets/secrect_widget.dart';
import 'package:tynt_web/utlity/utlity.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/full_screen_loading.dart';

class ScreenLock extends StatefulWidget {
  const ScreenLock({super.key});

  @override
  State<ScreenLock> createState() => ScreenLockState();
}

class ScreenLockState extends State<ScreenLock> {
  late ScreenLockInputController inputController = ScreenLockInputController();

  @override
  void initState() {
    super.initState();
    inputController.initialize(
      digits: 4,
      onValidate: verifyPasscode,
      onRetryLimitReached: () {
        // TODO: Implement this
        // DailogBoxes.forgotPasscodeBottomSheet(context);
      },
    );
  }

  @override
  void dispose() {
    inputController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        FadeIn(
          child: SafeArea(
            child: SizedBox(
              // color: Colors.white.withOpacity2(0.6),
              width: 716,
              height: MediaQuery.sizeOf(context).height,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        context.l10n.enterPasscode,
                        style: context.textTheme.bodyLarge?.copyWith(
                          fontSize: context.isMobile ? 20 : 28,
                          fontWeight: FontWeight.w600,
                          color: AppColors.rangoonGreen.withOpacity2(0.7),
                          letterSpacing: 1.8,
                        ),
                      ),
                      Gap(context.isMobile ? 16 : 22),
                      SecretWithShakingAnimation(
                        length: 4,
                        input: inputController.currentInput,
                        verifyStream: inputController.verifyInput,
                      ),
                      ValueListenableBuilder<bool>(
                        valueListenable: inputController.currentLoading,
                        builder: (_, value, __) {
                          return ScreenLockKeypad(
                            inputState: inputController,
                            enabled: !value,
                          );
                        },
                      ),
                      Gap(context.isMobile ? 30 : 38),
                      TextButton(
                        onPressed: () {
                          // TODO: Implement this
                          // DailogBoxes.forgotPasscodeBottomSheet(context);
                        },
                        child: Text(
                          '${context.l10n.forgotPasscode}?',
                          style: context.textTheme.bodyLarge?.copyWith(
                            fontSize: context.isMobile ? 18 : 24,
                            color: AppColors.rangoonGreen.withOpacity2(0.7),
                          ),
                        ),
                      ),
                    ],
                  ),
                  Positioned(
                    left: 16,
                    top: 15,
                    child: Material(
                      color: AppColors.transparent,
                      child: CircleAvatar(
                        radius: context.isMobile ? 18 : 20,
                        backgroundColor: AppColors.rangoonGreen.withOpacity2(0.5),
                        child: IconButton(
                          onPressed: () => context.pop(),
                          constraints: const BoxConstraints(),
                          padding: EdgeInsets.zero,
                          splashRadius: (context.isMobile ? 24 : 28),
                          icon: AppImageAsset(
                            AppAssets.backArrowIcon,
                            color: AppColors.white,
                            height: context.isMobile ? 24 : 28,
                            width: context.isMobile ? 24 : 28,
                          ),
                        ),
                      ),
                    ),
                  ),
                  ValueListenableBuilder<bool>(
                    valueListenable: inputController.currentLoading,
                    builder: (_, value, __) {
                      if (value) {
                        return const FullScreenLoading();
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> verifyPasscode(String value) async {
    final failOrSuccess = await getIt<IAuthRepository>().verifyPasscode(passcode: value);

    failOrSuccess.fold((l) {
      inputController.stopLoading();
      Utility.toast(message: l.message);
      inputController.onError();
    }, (r) {
      getIt<ILocalStorageRepository>().setTyntUserToken(r.data);
      Future.delayed(const Duration(milliseconds: 550), () {
        // Utility.toast(message: r.message);
        inputController.stopLoading();
        Navigator.pop(context, true);
      });
    });
  }
}
