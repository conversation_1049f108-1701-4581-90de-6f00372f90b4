import 'dart:async';

import 'package:flutter/material.dart';

typedef ValidationCallback = void Function(String input);

class ScreenLockInputController {
  ScreenLockInputController();
  late int _digits;

  List<String> currentInputs = const <String>[];

  late ValueNotifier<String> _inputValueNotifier;
  late ValueNotifier<bool> _loadingNotifier;

  late StreamController<bool> _verifyController;
  late StreamController<bool> _confirmedController;

  late ValidationCallback? _validationCallback;

  late VoidCallback? _onRetryLimitReached;

  /// Get latest input text value.
  ValueNotifier<String> get currentInput => _inputValueNotifier;

  /// Get latest Loading value.
  ValueNotifier<bool> get currentLoading => _loadingNotifier;

  /// Get verify result stream.
  Stream<bool> get verifyInput => _verifyController.stream;

  /// Get confirmed result stream.
  Stream<bool> get confirmed => _confirmedController.stream;

  int totalRetries = 0;

  void addCharacter(String input) {
    if (currentInputs.length >= _digits) {
      return;
    }

    currentInputs = [...currentInputs, input];

    _inputValueNotifier.value = currentInputs.join();

    if (_digits != currentInputs.length) {
      return;
    }

    startLoading();
    _validationCallback?.call(currentInputs.join());
  }

  /// Remove trailing characters and notify.
  Future<void> removeCharacter() async {
    if (currentInputs.isNotEmpty) {
      currentInputs.removeLast();
      _inputValueNotifier.value = currentInputs.join();
    }
  }

  /// Erase all current input.
  void clear() {
    if (currentInputs.isNotEmpty) {
      currentInputs.clear();
      try {
        _inputValueNotifier.value = '';
      } catch (e) {
        // disposed
      }
    }
  }

  void startLoading() {
    _loadingNotifier.value = true;
  }

  void stopLoading() {
    _loadingNotifier.value = false;
  }

  void onError() {
    _verifyController.add(false);
    totalRetries += 1;

    Future.delayed(const Duration(milliseconds: 1000), () {
      clear();
      _verifyController.add(true);

      if (totalRetries % 4 == 0) {
        _onRetryLimitReached?.call();
      }
    });
  }

  void initialize({required int digits, ValidationCallback? onValidate, VoidCallback? onRetryLimitReached}) {
    _inputValueNotifier = ValueNotifier<String>('');
    _verifyController = StreamController.broadcast();
    _confirmedController = StreamController.broadcast();
    _loadingNotifier = ValueNotifier(false);
    _validationCallback = onValidate;
    _onRetryLimitReached = onRetryLimitReached;
    _digits = digits;
  }

  /// Close all streams.
  Future<void> dispose() async {
    _inputValueNotifier.dispose();
    _loadingNotifier.dispose();
    await _verifyController.close();
    await _confirmedController.close();
  }
}
