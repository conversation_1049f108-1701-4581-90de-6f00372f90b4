final class _AppRoute {
  const _AppRoute({
    required this.route,
    required this.name,
  });

  final String route;
  final String name;
}

class AppRoutes {
  static const login = _AppRoute(route: '/login', name: 'login');
  static const profile = _AppRoute(route: '/profile', name: 'profile');
  static const editProfile = _AppRoute(route: '/edit-profile', name: 'edit-profile');
  static const createPost = _AppRoute(route: '/create-post', name: 'create-post');
  static const previewPost = _AppRoute(route: '/preview-post', name: 'preview-post');
  static const tomePost = _AppRoute(route: ':id/tome-post', name: 'tome-post');
  static const postDetail = _AppRoute(route: ':id/post-detail', name: 'post-detail');
  static const editPost = _AppRoute(route: '/edit', name: 'edit-post');
  static const shareTomePost = _AppRoute(route: '/share/:id/tome-post', name: 'share-tome-post');
  static const sharePostDetail = _AppRoute(route: ':postid/post-detail', name: 'share-post-detail');
}
