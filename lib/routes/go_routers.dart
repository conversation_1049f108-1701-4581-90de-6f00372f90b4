import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tynt_web/create_post/view/create_edit_post_wrapper_page.dart';
import 'package:tynt_web/create_post/view/post_detail_page.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/local_storage/repository/i_local_storage_repository.dart';
import 'package:tynt_web/login/view/login_page.dart';
import 'package:tynt_web/master/nested_navigation_page.dart';
import 'package:tynt_web/routes/app_routes.dart';
import 'package:tynt_web/tomes/view/tomes_details_page.dart';
import 'package:tynt_web/user_profile/profile_page.dart';
import 'package:tynt_web/user_profile/view/edit_profile_page.dart';
import 'package:tynt_web/widgets/common_button_widget.dart';

final _rootNavigatorKey = GlobalKey<NavigatorState>();
final _shellNavigatorProfileKey = GlobalKey<NavigatorState>(debugLabel: 'profile');
final _shellNavigatorCreatePostKey = GlobalKey<NavigatorState>(debugLabel: 'post');

final goRouter = GoRouter(
  debugLogDiagnostics: true,
  navigatorKey: _rootNavigatorKey,
  initialLocation: AppRoutes.login.route,
  redirect: (context, state) async {
    final isLoginRoute = [AppRoutes.login.route].contains(state.matchedLocation);
    final isShareTomesRoute = [AppRoutes.shareTomePost.name].contains(state.matchedLocation);
    final userToken = getIt<ILocalStorageRepository>().getUserToken;
    log('${userToken}userToken');
    if (isShareTomesRoute) {
      return null;
    }

    if (!isLoginRoute && userToken == null) {
      log('userToken is null');
      return AppRoutes.login.route;
    }

    if (isLoginRoute && userToken != null) {
      log('userToken is not null');
      return AppRoutes.profile.route;
    }

    if (state.matchedLocation == '/' && userToken != null) {
      log('userToken is not null and state.matchedLocation == /');
      return AppRoutes.profile.route;
    }
    log('userToken is not null and state.matchedLocation != / ${state.matchedLocation}1212');
    return null;
  },
  errorBuilder: (context, state) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Page Not Found',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            const Text('The page you are looking for does not exist.'),
            const SizedBox(height: 30),
            CommonButton(
              buttonText: 'Home',
              onTap: () {
                context.go(AppRoutes.profile.route); // Go to users page or your home page
              },
            ),
          ],
        ),
      ),
    );
  },
  routes: [
    GoRoute(
      path: AppRoutes.login.route,
      name: AppRoutes.login.name,
      builder: (c, s) => const LoginPage(),
    ),
    GoRoute(
      path: AppRoutes.shareTomePost.route,
      name: AppRoutes.shareTomePost.name,
      builder: (c, s) => TomesDetailsWrapperPage(tomesId: s.pathParameters['id'] ?? '', isShared: true),
      routes: [
        GoRoute(
          path: AppRoutes.sharePostDetail.route,
          name: AppRoutes.sharePostDetail.name,
          builder: (c, s) => PostDetailPage(
            postId: int.parse(s.pathParameters['postid'] ?? '0'),
            isShared: true,
          ),
        ),
      ],
    ),
    StatefulShellRoute.indexedStack(
      builder: (context, state, navigationShell) => NestedNavigationPage(navigationShell: navigationShell),
      branches: [
        StatefulShellBranch(
          navigatorKey: _shellNavigatorProfileKey,
          routes: [
            GoRoute(
              path: AppRoutes.profile.route,
              name: AppRoutes.profile.name,
              pageBuilder: (c, s) => const NoTransitionPage(child: ProfilePage()),
              routes: [
                GoRoute(
                  path: AppRoutes.editProfile.route,
                  name: AppRoutes.editProfile.name,
                  pageBuilder: (c, s) => const NoTransitionPage(child: EditAccountPage()),
                ),
                GoRoute(
                    path: AppRoutes.postDetail.route,
                    name: AppRoutes.postDetail.name,
                    builder: (c, s) {
                      return PostDetailPage(
                        postId: int.parse(s.pathParameters['id'] ?? '0'),
                      );
                    },
                    routes: [
                      GoRoute(
                        path: AppRoutes.editPost.route,
                        name: AppRoutes.editPost.name,
                        pageBuilder: (c, s) => NoTransitionPage(
                          child: CreateEditPostWrapperPage(
                            id: int.tryParse(s.pathParameters['id'] ?? ''),
                          ),
                        ),
                      ),
                    ]),
                GoRoute(
                  path: AppRoutes.tomePost.route,
                  name: AppRoutes.tomePost.name,
                  builder: (c, s) => TomesDetailsWrapperPage(tomesId: s.pathParameters['id'] ?? ''),
                ),
              ],
            ),
          ],
        ),
        StatefulShellBranch(
          navigatorKey: _shellNavigatorCreatePostKey,
          routes: [
            GoRoute(
              path: AppRoutes.createPost.route,
              name: AppRoutes.createPost.name,
              pageBuilder: (c, s) => const NoTransitionPage(child: CreateEditPostWrapperPage()),
            ),
          ],
        ),
      ],
    ),
  ],
);

extension GoRouterExtention on GoRouter {
  String get location => routeInformationProvider.value.location;
  String get location2 => routeInformationProvider.value.uri.path;
}
