// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/constants/app_text_style.dart';
import 'package:tynt_web/utlity/custom_splash_factory.dart';

abstract class AppTheme {
  static ThemeData get lightThemeNormalUser => ThemeData(
        brightness: Brightness.light,
        scaffoldBackgroundColor: AppColors.normalUserLightBackgroundColor,
        colorScheme: const ColorScheme.light(background: AppColors.background2),
        primaryColor: AppColors.primary,
        primaryColorLight: AppColors.primary,
        cardColor: AppColors.white,
        bottomSheetTheme: _bottomSheetThemeLight,
        extensions: const [
          SkeletonizerConfigData(
            containersColor: AppColors.softPeach,
          )
        ],
        dividerTheme: const DividerThemeData(
          color: AppColors.border,
          thickness: 1,
        ),
        appBarTheme: AppBarTheme(
          backgroundColor: AppColors.normalUserLightBackgroundColor,
          elevation: 0,
          centerTitle: false,
          titleTextStyle: _textThemeLight.bodyLarge?.copyWith(
            fontSize: 18,
            fontWeight: FontWeight.w700,
            letterSpacing: 0.8,
            color: AppColors.text,
          ),
          systemOverlayStyle: const SystemUiOverlayStyle(
            statusBarColor: AppColors.transparent,
            statusBarIconBrightness: Brightness.dark,
            statusBarBrightness: Brightness.light,
          ),
        ),
        useMaterial3: false,
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            splashFactory: const CustomSplashFactory(),
          ),
        ),
        iconButtonTheme: IconButtonThemeData(
          style: IconButton.styleFrom(
            splashFactory: const CustomSplashFactory(),
          ),
        ),
        splashFactory: const CustomSplashFactory(),
        textTheme: _textThemeLight,
        tabBarTheme: _tababarTheme,
        textButtonTheme: _textButtonTheme,
        inputDecorationTheme: _inputDecorationThemeLight,
        checkboxTheme: CheckboxThemeData(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
        ),
        dividerColor: AppColors.border,
        textSelectionTheme: TextSelectionThemeData(
          cursorColor: AppColors.primary,
          selectionColor: AppColors.primary.withOpacity(0.2),
          selectionHandleColor: AppColors.primary,
        ),
      );

  static ThemeData get lightThemeTyntUser => lightThemeNormalUser.copyWith(
        scaffoldBackgroundColor: AppColors.tyntUserLightBackgroundColor,
        appBarTheme: lightThemeNormalUser.appBarTheme.copyWith(backgroundColor: AppColors.tyntUserLightBackgroundColor),
        dividerTheme: const DividerThemeData(
          color: AppColors.tyntDivider,
          thickness: 1,
        ),
      );

  static ThemeData get darkThemeNormalUser => ThemeData(
        brightness: Brightness.dark,
        scaffoldBackgroundColor: AppColors.normalUserDarkBackgroundColor,
        extensions: const {
          SkeletonizerConfigData.dark(),
        },
        colorScheme: const ColorScheme.dark(background: AppColors.background2),
        primaryColor: AppColors.primary,
        bottomSheetTheme: _bottomSheetThemeDark,
        primaryColorLight: AppColors.textDark,
        cardColor: AppColors.normalUserDarkCardColor,
        dividerTheme: const DividerThemeData(
          color: AppColors.borderDark,
        ),
        appBarTheme: AppBarTheme(
          backgroundColor: AppColors.normalUserDarkBackgroundColor,
          elevation: 0,
          centerTitle: false,
          titleTextStyle: _textThemeDark.bodyLarge?.copyWith(
            fontSize: 18,
            fontWeight: FontWeight.w700,
            letterSpacing: 0.8,
            color: AppColors.headingTextDark,
          ),
          systemOverlayStyle: const SystemUiOverlayStyle(
            statusBarColor: AppColors.transparent,
            statusBarIconBrightness: Brightness.dark,
            statusBarBrightness: Brightness.light,
          ),
        ),
        useMaterial3: false,
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            splashFactory: const CustomSplashFactory(),
          ),
        ),
        iconButtonTheme: IconButtonThemeData(
          style: IconButton.styleFrom(
            splashFactory: const CustomSplashFactory(),
          ),
        ),
        splashFactory: const CustomSplashFactory(),
        textTheme: _textThemeDark,
        tabBarTheme: _tababarTheme.copyWith(
          labelStyle: _tababarTheme.labelStyle?.copyWith(
            color: AppColors.white,
          ),
          indicatorColor: AppColors.white,
          unselectedLabelColor: AppColors.greyTextColor,
        ),
        textButtonTheme: _textButtonTheme,
        inputDecorationTheme: _inputDecorationThemeDark,
        checkboxTheme: CheckboxThemeData(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
        ),
        dividerColor: AppColors.border,
        textSelectionTheme: TextSelectionThemeData(
          cursorColor: AppColors.primary,
          selectionColor: AppColors.primary.withOpacity(0.2),
          selectionHandleColor: AppColors.primary,
        ),
      );

  static ThemeData get darkThemeTyntUser => darkThemeNormalUser.copyWith(
        scaffoldBackgroundColor: Colors.transparent,
        appBarTheme: darkThemeNormalUser.appBarTheme.copyWith(
          backgroundColor: const Color(0xff0F3352),
          titleTextStyle: _textThemeDark.bodyLarge?.copyWith(
            fontSize: 18,
            fontWeight: FontWeight.w700,
            letterSpacing: 0.8,
            color: AppColors.headingTextDark,
          ),
        ),
        bottomSheetTheme: _bottomSheetThemeDarkTyntUser,
        inputDecorationTheme: _inputDecorationThemeLight,
        cardColor: AppColors.white,
      );

  static TextTheme get _textThemeLight {
    return TextTheme(
      displayLarge: AppTextStyle.displayLarge,
      displayMedium: AppTextStyle.displayMedium,
      displaySmall: AppTextStyle.displaySmall,
      headlineLarge: AppTextStyle.headline,
      headlineMedium: AppTextStyle.headlineMedium,
      headlineSmall: AppTextStyle.headlineSmall,
      bodyLarge: AppTextStyle.bodyLarge,
      bodyMedium: AppTextStyle.bodyMedium,
      bodySmall: AppTextStyle.bodySmall,
      labelLarge: AppTextStyle.labelLarge,
      labelMedium: AppTextStyle.labelMedium,
      labelSmall: AppTextStyle.labelSmall,
    );
  }

  static TextTheme get _textThemeDark {
    return TextTheme(
      displayLarge: AppTextStyle.displayLarge.copyWith(color: AppColors.textDark),
      displayMedium: AppTextStyle.displayMedium.copyWith(color: AppColors.textDark),
      displaySmall: AppTextStyle.displaySmall.copyWith(color: AppColors.textDark),
      headlineLarge: AppTextStyle.headline.copyWith(color: AppColors.headingTextDark),
      headlineMedium: AppTextStyle.headlineMedium.copyWith(color: AppColors.headingTextDark),
      headlineSmall: AppTextStyle.headlineSmall.copyWith(color: AppColors.headingTextDark),
      bodyLarge: AppTextStyle.bodyLarge.copyWith(color: AppColors.textDark),
      bodyMedium: AppTextStyle.bodyMedium.copyWith(color: AppColors.textDark),
      bodySmall: AppTextStyle.bodySmall.copyWith(color: AppColors.textDark),
      labelLarge: AppTextStyle.labelLarge.copyWith(color: AppColors.subTextDark),
      labelMedium: AppTextStyle.labelMedium.copyWith(color: AppColors.subTextDark),
      labelSmall: AppTextStyle.labelSmall.copyWith(color: AppColors.subTextDark),
    );
  }

  static TextButtonThemeData get _textButtonTheme {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        splashFactory: const CustomSplashFactory(),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        minimumSize: const Size(40, 30),
        padding: const EdgeInsets.symmetric(horizontal: 2),
        textStyle: _textThemeLight.bodyMedium?.copyWith(
          fontWeight: FontWeight.w400,
          color: AppColors.subText,
        ),
      ),
    );
  }

  static BottomSheetThemeData get _bottomSheetThemeLight => const BottomSheetThemeData(
        backgroundColor: AppColors.white,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(26),
            topRight: Radius.circular(26),
          ),
        ),
      );

  static BottomSheetThemeData get _bottomSheetThemeDark => const BottomSheetThemeData(
        backgroundColor: AppColors.normalUserDarkCardColor,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(26),
            topRight: Radius.circular(26),
          ),
        ),
      );

  static BottomSheetThemeData get _bottomSheetThemeDarkTyntUser => const BottomSheetThemeData(
        backgroundColor: AppColors.white,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(26),
            topRight: Radius.circular(26),
          ),
        ),
      );

  static InputDecorationTheme get _inputDecorationThemeLight {
    return InputDecorationTheme(
      border: InputBorder.none,
      fillColor: AppColors.white,
      filled: true,
      hintStyle: _textThemeLight.labelLarge?.copyWith(
        fontWeight: FontWeight.w400,
      ),
      counterStyle: _textThemeLight.labelLarge?.copyWith(fontSize: 10),
      contentPadding: const EdgeInsets.all(11),
      iconColor: AppColors.black,
      suffixIconColor: AppColors.black,
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: const BorderSide(color: AppColors.lightGrey),
      ),
      disabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: const BorderSide(color: AppColors.lightGrey),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: const BorderSide(color: AppColors.lightGrey),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: const BorderSide(color: AppColors.red2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: const BorderSide(color: AppColors.red2),
      ),
      errorMaxLines: 2,
      errorStyle: _textThemeLight.bodyMedium?.copyWith(
        color: AppColors.red2,
      ),
    );
  }

  static InputDecorationTheme get _inputDecorationThemeDark {
    return InputDecorationTheme(
      border: InputBorder.none,
      fillColor: AppColors.textfieldFillColorDark,
      filled: true,
      hintStyle: _textThemeDark.labelLarge?.copyWith(
        fontWeight: FontWeight.w400,
      ),
      counterStyle: _textThemeDark.labelLarge?.copyWith(fontSize: 10),
      contentPadding: const EdgeInsets.all(11),
      iconColor: AppColors.black,
      suffixIconColor: AppColors.black,
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: const BorderSide(color: AppColors.textfieldFillColorDark),
      ),
      disabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: const BorderSide(color: AppColors.textfieldFillColorDark),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: const BorderSide(color: AppColors.textfieldFillColorDark),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: const BorderSide(color: AppColors.red2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: const BorderSide(color: AppColors.red2),
      ),
      errorMaxLines: 2,
      errorStyle: _textThemeDark.bodyMedium?.copyWith(
        color: AppColors.red2,
      ),
    );
  }

  static TabBarThemeData get _tababarTheme => TabBarThemeData(
        labelStyle: _textThemeLight.bodyMedium?.copyWith(
          fontWeight: FontWeight.w400,
          color: AppColors.primary,
        ),
        indicator: const UnderlineTabIndicator(
          borderSide: BorderSide(
            width: 2,
            color: AppColors.primary,
          ),
        ),
        indicatorColor: AppColors.primary,
        unselectedLabelColor: AppColors.subText,
      );

  static String? manropeFontFamily = GoogleFonts.manrope().fontFamily;
  static String? spaceGroteskFontFamily = GoogleFonts.spaceGrotesk().fontFamily;
  static String? ibmPlexSans = GoogleFonts.ibmPlexSans().fontFamily;
}
