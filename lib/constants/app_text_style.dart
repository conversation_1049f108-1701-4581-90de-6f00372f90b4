import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tynt_web/constants/app_colors.dart';

abstract class AppTextStyle {
  static final TextStyle _commonStyle = GoogleFonts.notoSans(
    color: AppColors.text,
  );

  static TextStyle displayLarge = _commonStyle.copyWith(
    fontSize: 72,
    fontWeight: FontWeight.w500,
    letterSpacing: -1.5,
  );

  /// displayMedium Text Style
  static TextStyle displayMedium = _commonStyle.copyWith(
    fontSize: 62,
    fontWeight: FontWeight.w500,
    letterSpacing: -0.5,
  );

  /// displaySmall Text Style
  static TextStyle displaySmall = _commonStyle.copyWith(
    fontSize: 49,
    fontWeight: FontWeight.w400,
  );

  /// headline Text Style
  static TextStyle headline = _commonStyle.copyWith(
    fontSize: 40,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.25,
  );

  static TextStyle headlineMedium = _commonStyle.copyWith(
    fontSize: 35,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.25,
  );

  static TextStyle headlineSmall = _commonStyle.copyWith(
    fontSize: 25,
    fontWeight: FontWeight.w400,
  );

  /// bodyLargeBold Text Style
  static TextStyle bodyLarge = _commonStyle.copyWith(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.5,
  );

  /// bodyLargeMedium Text Style
  static TextStyle bodyMedium = _commonStyle.copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.25,
  );

  /// bodyLargeRegular Text Style
  static TextStyle bodySmall = _commonStyle.copyWith(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
  );

  /// labelLarge Text Style
  static TextStyle labelLarge = _commonStyle.copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: AppColors.subText,
  );

  static TextStyle labelSmall = _commonStyle.copyWith(
    fontSize: 10,
    fontWeight: FontWeight.w500,
    color: AppColors.subText,
  );
  static TextStyle labelMedium = _commonStyle.copyWith(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: AppColors.subText,
  );
}
