import 'package:flutter/material.dart';

abstract class AppColors {
  static const Color transparent = Colors.transparent;

  static const Color black = Color(0xff000000);

  static const Color normalUserDarkBackgroundColor = Color(0xff000000);

  static const Color text = Color(0xff1E1614);
  static const Color textDark = Color(0xffB2B2B2);
  static const Color headingTextDark = Color(0xffE5E5E5);
  static const Color textfieldFillColorDark = Color(0xff10161C);

  // static const Color newTextColor = Color(0xff1E1614);
  static const Color orange2 = Color(0xffEC8388); // new added
  static const Color highlights2 = Color(0xffCF872F);

  static const Color text2 = Color(0xff35383F);

  static const Color text3 = Color(0xff18191D);

  static const Color subText = Color(0xff4c5a80);
  static const Color subTextDark = Color(0xff808080);

  static const Color red = Color(0xfffe506b);

  static const Color primary2 = Color(0xffF27772);
  static const Color tyntSwitchOnColor = Color(0xffFF7667);

  static const Color primary = Color(0xff480C0B);

  static const Color secondary = Color(0xffEDDDE5);

  static const Color border = Color(0xffE4E4E4);
  static const Color tyntDivider = Color(0xffE2E3E5);
  static const Color borderDark = Color(0xff545454);

  static const Color white = Color(0xffffffff);
  static const Color background2 = Color(0xffFCF8F5);

  static const Color background = Color(0xfff5f6fa);
  static const Color tribeDetailBackground = Color(0xffF3F5F9);

  static const List<Color> gradient = <Color>[
    Color(0xff2e3790),
    Color(0xff6a75e1),
  ];

  static const Color red2 = Color(0xffff6161);

  static const Color lightBlue = Color(0xffedf3fc);
  static const Color normalUserDarkCardColor = Color(0xff2F3135);

  static const Color green = Color(0xff018934);

  static const Color orange = Color(0xfffb641b);

  static const Color reddishOrange = Color(0xffFFE8DB);

  static const Color ebonyClay = Color(0xff252839);

  static const Color lightGrey = Color(0xffD8DADC);

  static const Color greyTextColor = Color(0xff9E9E9E);

  static const Color cinder = Color(0xff121418);

  static const Color navy = Color(0xff041D42);

  static const Color border2 = Color(0xffCFCBCB);

  static const Color border3 = Color(0xffCDCDCD);

  static const Color greyButtonColor = Color(0xffEEF1F5);

  static const Color blueZodiac = Color(0xff1C274C);

  static const Color softPeach = Color(0xffEFEFEF);

  static const Color davyGrey = Color(0xff545454);

  static const Color boulder = Color(0xff757575);

  static const Color platinum = Color(0xffE2E2E2);

  static const Color tealishBlue = Color(0xff276EF1);

  static const Color unfollowButtonColor = Color(0xffDBD0D0);

  static const Color followButtonColor = Color(0xff276EF1);

  // static const Color tyntModeBackgroundColor = Color(0xffD9D0C9);

  // static const Color tyntModeBackgroundColor = Color(0xffDED6CF);

  static const Color tyntModeBackgroundColor = Color(0xffD2DADE);

  static const Color normalUserLightBackgroundColor = Color(0xffFCF8F5);
  static const Color tyntUserLightBackgroundColor = Color(0xffF0E8EC);

  static const Color shimmerBg = Color(0xffe5e7eb);

  static const Color shimmerLightColor = Color(0xeeffffff);
  static const Color shimmerDarkColor = Color(0xff4c5a80);

  static const Color vistaWhite = Color(0xffFCF8F5);

  static const Color rangoonGreen = Color(0xff1C1C1C);
  static const Color thoughtTextFieldBorderColor = Color(0xffEFE8DB);

  static const Color appbarBorder = Color(0xffE5E8EB);

  static BoxShadow postItemShadow = BoxShadow(
    color: Colors.black.withOpacity(0.10),
    offset: const Offset(1, 2),
    blurRadius: 4,
  );

  static const shimmerGradient = LinearGradient(
    colors: [
      shimmerBg,
      shimmerLightColor,
      shimmerBg,
    ],
    stops: [
      0.1,
      0.3,
      0.4,
    ],
    begin: Alignment(-1, -0.3),
    end: Alignment(1, 0.3),
  );

  static const tyntModeGredient = LinearGradient(
    colors: [
      Color(0xff5C6B98),
      Color(0xff877EA6),
      Color(0xffAC91B2),
      Color(0xffC8A1BC),
      Color(0xffD0A7BD),
      Color(0xffC99EB4),
      Color(0xffD1A7BB),
    ],
    stops: [
      0,
      0.17,
      0.33,
      0.5,
      0.67,
      0.83,
      1,
    ],
  );
  static const tyntModeGredient2 = LinearGradient(
    colors: [
      Color(0xffCAD1D1),
      Color(0xff999E9E),
      Color(0xffCAD1D1),
    ],
    stops: [
      0,
      0.5,
      1,
    ],
  );
  static const tyntModeDarkGredient = LinearGradient(
    colors: [
      Color(0xff0F3352),
      Color(0xff480C0B),
    ],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  static Color shimmerLoadingColor(ThemeMode themeMode) {
    if (themeMode == ThemeMode.light) {
      return shimmerLightColor;
    } else {
      return Colors.grey.withOpacity(0.2);
    }
  }
}
