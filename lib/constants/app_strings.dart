import 'package:tynt_web/utlity/enum/share_type_enum.dart';

abstract class AppStrings {
  static const String appName = 'TYNT';

  //Smartlook
  static const String smartLookKey = '6e6ac0de3353f17a4aba3aeeb0b38ab837aa805f';

  //Pref Keys
  static const String userToken = 'USER_TOKEN';
  static const String tyntUserToken = 'TYNT_USER_TOKEN';
  static const String appLanguage = 'APP_LANGUAGE';
  static const String appOnBoarding = 'APP_ONBOARDING';
  static const String appTheme = 'APP_THEME';

  // Language Keys
  static const String english = 'en';
  static const String spanish = 'es';

  // static keys
  static const String defaultAppLanguage = 'en';
  static const String public = 'PUBLIC';
  static const String pseudo = 'PSEUDO';
  static const String business = 'BUSINESS';
  static const String follow = 'FOLLOW';
  static const String privacyPolicy = 'PRIVACY_POLICY';
  static const String termsCondition = 'TERMS_CONDITION';
  static const String contactWeb = 'CONTACT_WEB';
  static const String contactPhone = 'CONTACT_PHONE';
  static const String contactEmail = 'CONTACT_EMAIL';
  static const String contactAddress = 'CONTACT_ADDRESS';
  static const String socialFacebook = 'SOCIAL_FACEBOOK';
  static const String socialTwitter = 'SOCIAL_TWITTER';
  static const String socialInstagram = 'SOCIAL_INSTAGRAM';
  static const String socialYoutube = 'SOCIAL_YOUTUBE';
  static const String aboutUs = 'ABOUT_US';
  static const String all = 'ALL';
  static const String myPosts = 'MY_POSTS';
  static const String pseudoPosts = 'PSEUDO_POSTS';
  static const String draftPosts = 'DRAFT_POSTS';
  static const String mostNominated = 'MOST_NOMINATED';
  static const String community = 'COMMUNITY';
  static const String tyntY = 'TYNT_Y';
  static const String favorites = 'FAVORITES';

  static const String normal = 'NORMAL';

  static const String promotedKey = 'PROMOTED_KEY';
  static const String unpromotedKey = 'UN_PROMOTED_KEY';

  static const String sampleSmallText = 'Lorem Ipsum is simply dummy simply dummy';
  static const String sampleMediumText = "Lorem Ipsum has been the industry's standard dummy text ever since....";
  static const String sampleLongText =
      "Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.";

  // Notification Type
  static const String openOtherProfile = 'OPEN_OTHER_PROFILE';
  static const String openPost = 'OPEN_POST';
  static const String openChat = 'OPEN_CHAT';

  static const String dynamicLinkPreFixForShare = 'https://link.tyntsocial.com';
  static String tomeShareLink(int id) => '$dynamicLinkPreFixForShare/share?type=${ShareType.tome.name}&id=$id';

  static const String postId = 'post_id';

  //Apis
  static const String baseUrl = 'https://api.tyntsocial.com/api';
  // static const String baseUrl = kDebugMode
  //     ? 'https://staging-api.tyntsocial.com/api' // Test URL
  //     : 'https://api.tyntsocial.com/api';

  static const String version = 'v1';

  static const String storageUrl = 'https://tynt-bucket.s3.us-east-1.amazonaws.com';

  static String getMediaFileUrl(String path) => '$storageUrl/$path';

  // authentication
  static const String _userPath = '$baseUrl/$version/users';
  static const String users = _userPath;
  static const String login = '$_userPath/login';
  static const String register = '$_userPath/register';
  static const String socialLogin = '$_userPath/social-login';
  static const String userResponse = '$_userPath/authenticate';
  static const String resendVerification = '$_userPath/resend-verification';
  static const String userUpdate = '$_userPath/update';
  static const String forgotPassword = '$_userPath/forgot-password';
  static const String changePassword = '$_userPath/change-password';
  static const String changePasscode = '$_userPath/change-passcode';
  static const String setPassword = '$_userPath/set-password';
  static const String forgotPasscode = '$_userPath/forgot-passcode';
  static const String verifyPasscode = '$_userPath/verify-passcode';
  static const String logout = '$_userPath/logout';
  static const String delete = '$_userPath/delete';
  static const String blockedUsersList = '$_userPath/block/list';
  static String followToggle(int id) => '$_userPath/$id/follow-toggle';
  static String followers(int id) => '$_userPath/$id/followers';
  static String followings(int id) => '$_userPath/$id/following';
  static String userDetail(int id) => '$_userPath/$id/detail';
  static String userBlockToggle(int id) => '$_userPath/$id/block-toggle';
  static const String getbyUserName = '$_userPath/get-by-username';

  static const String generatePseudoName = '$baseUrl/$version/pseudo/generate-name';

  static const String chats = '$_userPath/chats';
  static const String chatSend = '$_userPath/chat/send';

  static const String thoughtalogs = '$baseUrl/$version/users/thought-logs/';
  static const String thoughtalogsCreate = '$baseUrl/$version/users/thought-logs/create';
  static String thoughtalogsDelete(int id) => '$baseUrl/$version/users/thought-logs/$id/delete';

  //Posts
  static const String _postsPath = '$baseUrl/$version/posts';
  static const String createPost = '$_postsPath/create';
  static const String posts = _postsPath;
  static const String savedPosts = '$baseUrl/$version/users/saved-posts';
  static String postDetail(int id) => '$_postsPath/$id/detail';
  static String postDelete(int id) => '$_postsPath/$id/delete';
  static String postEdit(int id) => '$_postsPath/$id/edit';
  static String postNominatorToggle(int id) => '$_postsPath/$id/nominators/toggle';
  static const String postSaveToggle = '$baseUrl/$version/users/save-post';
  static String postCommentList(int id) => '$_postsPath/$id/comments';
  static const String postCommentAdd = '$_postsPath/comments/add-comment';
  static String postCommentDelete(int id) => '$_postsPath/comments/$id/delete';
  static String postCommentEdit(int id) => '$_postsPath/comments/$id/edit';

  static const String genresList = '$baseUrl/$version/genres';
  static const String avatars = '$baseUrl/$version/avatars';
  static const String metaData = '$baseUrl/$version/meta-data';
  static const String feedBackCreate = '$baseUrl/$version/feed-back/create';
  static const String faqList = '$baseUrl/$version/faqs';
  static const String postType = '$baseUrl/$version/post-type';
  static const String tagsList = '$baseUrl/$version/tags';
  static const String moviesList = '$baseUrl/$version/movies';
  static const String countriesList = '$baseUrl/$version/countries';
  static const String notificationList = '$baseUrl/$version/notifications';
  static const String createReport = '$baseUrl/$version/reports/create';
  static const String reportsUserList = '$baseUrl/$version/reports';

  // Tomes
  static const String tomesList = '$baseUrl/$version/users/tomes';
  static const String createTomes = '$baseUrl/$version/users/tomes/create';
  static String editTomes(int id) => '$baseUrl/$version/users/tomes/$id/update';
  static String deleteTomes(int id) => '$baseUrl/$version/users/tomes/$id/delete';
  static String tomesDetail(String id) => '$baseUrl/$version/users/tomes/$id/details';
  static String addPostToTomes(int id) => '$baseUrl/$version/users/tomes/$id/add-posts';

  //Community
  static const String _communityPath = '$baseUrl/$version/communities';
  static const String communityList = _communityPath;
  static const String createCommunity = '$_communityPath/create';
  static String editCommunity(int id) => '$_communityPath/$id/edit';
  static String toggleCommunity(int id) => '$_communityPath/$id/toggle';
  static String communityDetail(int id) => '$_communityPath/$id/detail';
  static String communityMembers(int id) => '$_communityPath/$id/members';

  //Font Family
  static const String sfProDisplaySemibold = 'SFProDisplay Semibold';
  static const String sfProDisplayRegular = 'SFProDisplay Regular';
  static const String sfProDisplayMedium = 'SFProDisplay Medium';
  static const String sfProDisplayBold = 'SFProDisplay Bold';
  static const String sfProDisplayHeavy = 'SFProDisplay Heavy';
}
