import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:multi_trigger_autocomplete/multi_trigger_autocomplete.dart';
import 'package:tynt_web/app/cubit/refresh_cubit.dart';
import 'package:tynt_web/auth/bloc/authentication_bloc.dart';
import 'package:tynt_web/auth/cubit/theme_cubit.dart';
import 'package:tynt_web/constants/app_strings.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/l10n/arb/app_localizations.dart';
import 'package:tynt_web/l10n/generated/l10n.dart';
import 'package:tynt_web/routes/go_routers.dart';

class App extends StatelessWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context) {
    return AppWrapper(
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, state) {
          return MaterialApp.router(
            title: 'TYNT',
            theme: state.currentTheme,
            scrollBehavior: CustomScrollBehavior(),
            routeInformationParser: goRouter.routeInformationParser,
            routeInformationProvider: goRouter.routeInformationProvider,
            routerDelegate: goRouter.routerDelegate,
            locale: const Locale.fromSubtags(languageCode: AppStrings.english),
            localizationsDelegates: const [
              I10n.delegate,
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              FlutterQuillLocalizations.delegate,
            ],
            debugShowCheckedModeBanner: false,
            builder: BotToastInit(),
            // builder: (context, child) => const UserProfilePage(),
          );
        },
      ),
    );
  }
}

class AppWrapper extends StatelessWidget {
  const AppWrapper({
    required this.child,
    super.key,
  });
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => getIt<AuthenticationBloc>()..add(const CheckAuthentication()),
        ),
        BlocProvider(
          lazy: false,
          create: (context) => getIt<ThemeCubit>()..loadTheme(),
        ),
        BlocProvider(
          create: (context) => getIt<RefreshCubit>(),
        ),
      ],
      child: Portal(child: child),
    );
  }
}

class CustomScrollBehavior extends MaterialScrollBehavior {
  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
      };
}
