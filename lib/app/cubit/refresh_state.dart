// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'refresh_cubit.dart';

class RefreshState extends Equatable {
  const RefreshState({
    this.postDetail,
    this.deletePost,
    this.newPost,
  });
  final PostModel? postDetail;
  final PostModel? deletePost;
  final PostModel? newPost;

  @override
  List<Object?> get props => [postDetail, deletePost, newPost];

  RefreshState copyWith({
    PostModel? postDetail,
    PostModel? deletePost,
    PostModel? newPost,
  }) {
    return RefreshState(
      postDetail: postDetail ?? this.postDetail,
      deletePost: deletePost ?? this.deletePost,
      newPost: newPost ?? this.newPost,
    );
  }
}
