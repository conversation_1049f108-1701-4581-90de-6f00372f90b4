import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:tynt_web/post_preview/model/post_model.dart';

part 'refresh_state.dart';

@injectable
class RefreshCubit extends Cubit<RefreshState> {
  RefreshCubit() : super(const RefreshState());

  void refreshPostDetail(PostModel post) {
    emit(state.copyWith(postDetail: post));

    clearStateWithDelay();
  }

  void deletePostDetail(PostModel post) {
    emit(state.copyWith(deletePost: post));

    clearStateWithDelay();
  }

  void addNewPostDetail(PostModel post) {
    emit(state.copyWith(newPost: post));

    clearStateWithDelay();
  }

  void clearStateWithDelay() => Future.delayed(const Duration(milliseconds: 500), () => emit(const RefreshState()));
}
