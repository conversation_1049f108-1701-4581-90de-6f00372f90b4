import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:tynt_web/auth/bloc/authentication_bloc.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_strings.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/custom_progress_indicator.dart';

class SplashPage extends StatelessWidget {
  const SplashPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Align(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AppImageAsset(
                AppAssets.appIcon,
                height: context.isMobile ? 60 : 90,
                width: context.isMobile ? 61 : 91,
              ),
              Gap(context.isMobile ? 10 : 25),
              Text(
                AppStrings.appName,
                style: context.textTheme.headlineSmall?.copyWith(
                  fontSize: context.isMobile ? 28 : 38,
                  fontWeight: FontWeight.w700,
                ),
              ),
              Gap(context.isMobile ? 8 : 18),
              Text(
                'Every Tynt tells a Story',
                style: context.textTheme.bodySmall?.copyWith(
                  fontSize: context.isMobile ? 14 : 20,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
        Align(
          alignment: const Alignment(1, 0.6),
          child: BlocBuilder<AuthenticationBloc, AuthenticationState>(
            builder: (context, state) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  state.maybeWhen(
                    orElse: SizedBox.new,
                    unknown: CustomProgressIndicator.new,
                    loading: CustomProgressIndicator.new,
                  ),
                ],
              );
            },
          ),
        ),
      ],
    );
  }
}
