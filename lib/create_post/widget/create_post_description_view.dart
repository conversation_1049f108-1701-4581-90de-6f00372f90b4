// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:multi_trigger_autocomplete/multi_trigger_autocomplete.dart';
import 'package:tynt_web/auth/models/user_model.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/create_post/widget/post_comment_user_search_view.dart';
import 'package:tynt_web/create_post/widget/search_movie_list_view.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/post_preview/model/comman_name_model.dart';
import 'package:tynt_web/post_preview/model/post_type_model.dart';

class CreatePostDescriptionView extends StatefulWidget {
  const CreatePostDescriptionView({
    required this.controller,
    required this.postType,
    super.key,
    this.onUserTap,
    this.onMovieTap,
    this.error,
  });
  final QuillController controller;
  final PostTypeModel postType;
  final ValueChanged<UserModel>? onUserTap;
  final ValueChanged<CommonNameModel>? onMovieTap;
  final String? error;

  @override
  State<CreatePostDescriptionView> createState() => CreatePostDescriptionViewState();
}

class CreatePostDescriptionViewState extends State<CreatePostDescriptionView> {
  final _fcNode = FocusNode();

  @override
  void dispose() {
    _fcNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiTriggerQuillAutocomplete(
      controller: widget.controller,
      focusNode: _fcNode,
      autocompleteTriggers: [
        if (widget.postType.shouldMentionMovieValue)
          AutocompleteTrigger(
            trigger: '@',
            minimumRequiredCharacters: 1,
            optionsViewBuilder: (context, autocompleteQuery, controller) {
              return SearchMovieListView(
                key: UniqueKey(),
                query: autocompleteQuery.query,
                onMovieTap: (value) {
                  final name = value.name ?? '';
                  final selection = widget.controller.selection;
                  final baseOffset = selection.baseOffset;
                  final textToDeleteLength = autocompleteQuery.query.length + 1; // 1 for '@'
                  final startOffset = baseOffset - textToDeleteLength;
                  if (startOffset >= 0) {
                    widget.controller.replaceText(
                      startOffset,
                      textToDeleteLength,
                      '@$name ',
                      TextSelection.collapsed(offset: startOffset + name.length + 2),
                    );
                  }
                  widget.onMovieTap?.call(value);
                },
              );
            },
          )
        else
          AutocompleteTrigger(
            trigger: '@',
            minimumRequiredCharacters: 1,
            optionsViewBuilder: (context, autocompleteQuery, controller) {
              return PostCommentUserSearchView(
                key: UniqueKey(),
                query: autocompleteQuery.query,
                onUserTap: (value) {
                  // MultiTriggerAutocomplete.of(context).acceptAutocompleteOption(value.userName ?? '');
                  final name = value.userName ?? '';
                  final selection = widget.controller.selection;
                  final baseOffset = selection.baseOffset;
                  final textToDeleteLength = autocompleteQuery.query.length + 1; // 1 for '@'
                  final startOffset = baseOffset - textToDeleteLength;
                  if (startOffset >= 0) {
                    widget.controller.replaceText(
                      startOffset,
                      textToDeleteLength,
                      '@$name ',
                      TextSelection.collapsed(offset: startOffset + name.length + 2),
                    );
                  }

                  widget.onUserTap?.call(value);
                },
              );
            },
          ),
      ],
      fieldViewBuilder: (context, textEditingController, focusNode) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Theme(
              data: Theme.of(context).copyWith(
                textSelectionTheme: TextSelectionThemeData(
                  cursorColor: Theme.of(context).brightness == Brightness.dark ? AppColors.textDark : AppColors.text,
                ),
              ),
              child: QuillEditor(
                controller: widget.controller,
                scrollController: ScrollController(),
                focusNode: _fcNode,
                config: QuillEditorConfig(
                  minHeight: 50,
                  placeholder: context.l10n.writeSomething,
                  customStyles: DefaultStyles(
                    // Theme.of(context).brightness == Brightness.dark
                    //     ? context.textTheme.bodyMedium?.color
                    //     : AppColors.subText,
                    placeHolder: DefaultTextBlockStyle(
                      context.theme.inputDecorationTheme.hintStyle!.copyWith(
                        color: AppColors.subText,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                      HorizontalSpacing.zero,
                      const VerticalSpacing(15, 15),
                      const VerticalSpacing(15, 15),
                      null,
                    ),
                  ),
                ),
              ),
            ),
            if (widget.error != null)
              Text(
                widget.error!,
                style: context.theme.inputDecorationTheme.hintStyle?.copyWith(
                  fontSize: 13,
                  color: AppColors.red2,
                ),
              ),
          ],
        );
      },
    );
  }
}
