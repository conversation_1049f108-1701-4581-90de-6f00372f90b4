// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/create_post/widget/create_post_picked_image_icon.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/post_image_view.dart';

class CreatePostImageEmptyView extends StatelessWidget {
  const CreatePostImageEmptyView({
    required this.onPressed,
    super.key,
  });
  final VoidCallback onPressed;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(12),
      child: const Center(
        child: Skeleton.shade(
          child: AppImageAsset(
            AppAssets.albumIcon,
            height: 24,
            width: 24,
          ),
        ),
      ),
    );
  }
}

class CreatePostImageView extends StatelessWidget {
  const CreatePostImageView({
    required this.onClosePressed,
    super.key,
    this.imageUrl,
    this.localFileBytes,
  });
  final String? imageUrl;
  final Uint8List? localFileBytes;
  final VoidCallback onClosePressed;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        height: 45,
        width: 45,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            PostImageView(
              height: 45,
              width: 45,
              imageUrl: imageUrl,
              localFileBytes: localFileBytes,
              borderRadius: 10,
            ),
            Positioned(
              left: -5,
              top: -5,
              child: CreatePostPickedImageIcon(onPressed: onClosePressed),
            ),
          ],
        ),
      ),
    );
  }
}
