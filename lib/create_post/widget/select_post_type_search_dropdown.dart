// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tynt_web/constants/app_theme.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/post_preview/model/post_type_model.dart';
import 'package:tynt_web/widgets/dailog_boxes.dart';

class SelectPostTypeSearchDropdown extends StatelessWidget {
  const SelectPostTypeSearchDropdown({
    super.key,
    this.onChanged,
    this.selectedPostType,
    this.title,
  });
  final ValueChanged<PostTypeModel?>? onChanged;
  final PostTypeModel? selectedPostType;
  final String? title;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        DailogBoxes.searchPostTypeDailog(context, onPostTypeSelected: onChanged);
      },
      child: IgnorePointer(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (title != null) ...[
              Text(
                title ?? '',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontSize: 14,
                      fontFamily: AppTheme.spaceGroteskFontFamily,
                    ),
              ),
              const Gap(6),
            ],
            DropdownSearch<PostTypeModel>(
              dropdownBuilder:
                  selectedPostType == null ? null : (context, selectedItem) => Text(selectedItem?.name ?? ''),
              onChanged: onChanged,
              compareFn: (item1, selected) => item1.id == selected.id,
              selectedItem: selectedPostType,
              items: (filter, loadProps) {
                return [];
              },
              decoratorProps: DropDownDecoratorProps(
                decoration: InputDecoration(
                  suffixIconColor: Theme.of(context).textTheme.headlineMedium?.color,
                  hintText: context.l10n.selectPostType,
                  hintStyle: context.theme.inputDecorationTheme.hintStyle?.copyWith(
                    fontSize: 15,
                  ),
                ),
              ),
              popupProps: PopupProps.menu(
                showSearchBox: true,
                showSelectedItems: true,
                constraints: const BoxConstraints(maxHeight: 250),
                itemBuilder: (context, item, isDisabled, isSelected) {
                  return Text(
                    item.name ?? '',
                    style: context.textTheme.bodyMedium,
                  );
                },
                // itemBuilder: (context, item, isSelected) {
                //   return ListTile(
                //     title: Text(
                //       item.name ?? '',
                //       style: context.textTheme.bodyMedium,
                //     ),
                //   );
                // },
              ),
              // asyncItems: (text) async {
              //   final response = await getIt<IPostRepository>().getPostTypeList(search: text.trim());

              //   return response.getRightFolded()?.data ?? <PostTypeModel>[];
              // },
              // dropdownDecoratorProps: DropDownDecoratorProps(
              //   dropdownSearchDecoration: InputDecoration(
              //     suffixIconColor: Theme.of(context).textTheme.headlineMedium?.color,
              //     hintText: context.l10n.selectPostType,
              //     hintStyle: context.theme.inputDecorationTheme.hintStyle?.copyWith(
              //       fontSize: 15,
              //     ),
              //   ),
              // ),
            ),
          ],
        ),
      ),
    );
  }
}
