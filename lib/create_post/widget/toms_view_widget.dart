import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/auth/cubit/theme_cubit.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/constants/app_theme.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/tomes/model/tomes_model.dart';
import 'package:tynt_web/tomes/widget/tomes_bottom_widget.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/app_image_network.dart';

class TomsViewWidget extends StatelessWidget {
  const TomsViewWidget({
    this.tomes,
    this.onShareTap,
    this.onEditTap,
    this.onDeleteTap,
    this.onTap,
    this.showActionButton = true,
    this.isSelected = false,
    super.key,
  });
  final TomesModel? tomes;
  final VoidCallback? onShareTap;
  final VoidCallback? onEditTap;
  final VoidCallback? onDeleteTap;
  final VoidCallback? onTap;
  final bool showActionButton;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 120,
        width: 94,
        // padding: EdgeInsets.zero,
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(6),
          // border: Border.all(color: AppColors.red),
          boxShadow: [
            BoxShadow(
              color: AppColors.black.withOpacity(0.15),
              blurRadius: 6.83,
              offset: const Offset(0, 1.95),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              alignment: Alignment.center,
              children: [
                Stack(
                  children: [
                    DecoratedBox(
                      decoration: const BoxDecoration(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(6),
                          topRight: Radius.circular(6),
                        ),
                      ),
                      child: Skeleton.shade(
                        child: AppImageNetwork(
                          url: tomes?.imageUrl ?? '',
                          loading: const AppImageLoading(
                            boxShape: BoxShape.rectangle,
                            decoration: BoxDecoration(
                              color: AppColors.white,
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(6),
                                topRight: Radius.circular(6),
                              ),
                            ),
                          ),
                          imageBuilder: (context, imageProvider) => Container(
                            decoration: BoxDecoration(
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(6),
                                topRight: Radius.circular(6),
                              ),
                              image: DecorationImage(image: imageProvider, fit: BoxFit.cover),
                            ),
                          ),
                          customErrorWidget: Container(
                            decoration: const BoxDecoration(
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(6),
                                topRight: Radius.circular(6),
                              ),
                              color: AppColors.white,
                            ),
                            clipBehavior: Clip.hardEdge,
                            height: 80,
                            width: 94,
                            child: const AppImageAsset(AppAssets.noImagePlaceholder),
                          ),
                          height: 80,
                          width: 94,
                        ),
                      ),
                    ),
                    // Skeleton.shade(
                    //   child: PostImageView(
                    //     imageUrl: tomes?.imageUrl ?? '',
                    //     width: MediaQuery.sizeOf(context).width,
                    //     height: context.isMobile ? 80 : 90,
                    //     boxfit: BoxFit.fill,
                    //     borderRadiusGeometry: const BorderRadius.only(
                    //       topLeft: Radius.circular(6),
                    //       topRight: Radius.circular(6),
                    //     ),
                    //   ),
                    // ),
                    // if (widget.index == 0)
                    Container(
                      margin: const EdgeInsets.only(top: 68),
                      decoration: BoxDecoration(
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.white.withOpacity(1),
                            blurRadius: 8,
                            spreadRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                          BoxShadow(
                            color: Colors.white.withOpacity(1),
                            blurRadius: 4,
                            spreadRadius: 4,
                            offset: const Offset(0, 11),
                          ),
                        ],
                      ),
                      width: MediaQuery.sizeOf(context).width,
                      height: 2, // keep the line thin for better effect
                      // color: AppColors.white, // actual color of the line
                    ),
                    if (isSelected)
                      Container(
                        color: AppColors.primary.withOpacity(0.55),
                        width: MediaQuery.sizeOf(context).width,
                        height: 76,
                      ),
                  ],
                ),
                if (isSelected)
                  const AppImageAsset(
                    AppAssets.roundCheckIcon,
                    height: 32,
                    width: 32,
                  ),
              ],
            ),
            BlocBuilder<ThemeCubit, ThemeState>(
              builder: (context, themeState) {
                return Padding(
                  padding: const EdgeInsets.fromLTRB(6, 4, 0, 6),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              tomes?.name ?? '',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: themeState.themeMode == ThemeMode.dark
                                  ? Theme.of(context).textTheme.bodySmall?.copyWith(
                                        fontWeight: FontWeight.w500,
                                        fontFamily: AppTheme.manropeFontFamily,
                                        color: AppColors.text,
                                        fontSize: context.isMobile ? 10 : 14,
                                      )
                                  : Theme.of(context).textTheme.bodySmall?.copyWith(
                                        fontWeight: FontWeight.w500,
                                        fontFamily: AppTheme.manropeFontFamily,
                                        fontSize: context.isMobile ? 10 : 14,
                                      ),
                            ),
                            Text(
                              '${tomes?.postsCount ?? 0} posts',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    fontSize: context.isMobile ? 10 : 13,
                                    color: AppColors.subText,
                                    fontFamily: AppTheme.manropeFontFamily,
                                  ),
                            ),
                          ],
                        ),
                      ),
                      if (showActionButton)
                        TomesBottomWidget(
                          onShareTap: onShareTap,
                          onEditTap: onEditTap,
                          onDeleteTap: onDeleteTap,
                        ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
