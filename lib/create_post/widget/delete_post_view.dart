import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:tynt_web/auth/cubit/theme_cubit.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/constants/app_theme.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/primary_button/views/primary_button_view.dart';
import 'package:tynt_web/utlity/enum/user_type_enum.dart';
import 'package:tynt_web/widgets/popup_wrapper.dart';

class DeletePostView extends StatelessWidget {
  const DeletePostView({super.key, this.onDeletePostTap, this.onSaveDraft});
  final VoidCallback? onDeletePostTap;
  final VoidCallback? onSaveDraft;

  @override
  Widget build(BuildContext context) {
    return PopUpWrapper(
      childrenPadding: const EdgeInsets.fromLTRB(20, 20, 20, 30),
      children: [
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            BlocBuilder<ThemeCubit, ThemeState>(
              builder: (context, state) {
                return Text(
                  context.l10n.areYouSureYouWantToDeletePost,
                  textAlign: TextAlign.center,
                  style: context.textTheme.headlineSmall?.copyWith(
                    fontSize: 22,
                    fontWeight: FontWeight.w700,
                    fontFamily: AppTheme.ibmPlexSans,
                    color: state.themeMode == ThemeMode.dark && state.currentUserType == UserType.normal
                        ? AppColors.headingTextDark
                        : AppColors.text,
                  ),
                );
              },
            ),
            const Gap(24),
            Row(
              children: [
                Expanded(
                  child: BlocBuilder<ThemeCubit, ThemeState>(
                    builder: (context, state) {
                      return PrimaryButton(
                        onPressed: () {
                          onDeletePostTap!();
                          Navigator.pop(context);
                        },
                        text: context.l10n.yesDelete,
                        backgroundColor: state.themeMode == ThemeMode.dark && state.currentUserType == UserType.normal
                            ? AppColors.headingTextDark
                            : AppColors.white,
                        textColor: AppColors.primary,
                        borderColor: AppColors.primary,
                      );
                    },
                  ),
                ),
                const SizedBox(
                  width: 15,
                ),
                Expanded(
                  child: PrimaryButton(
                    onPressed: () {
                      onSaveDraft!();
                      Navigator.pop(context);
                    },
                    text: context.l10n.saveDraft,
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }
}
