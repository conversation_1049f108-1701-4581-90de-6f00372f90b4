// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/widgets/app_icon_button.dart';

class CreatePostPickedImageIcon extends StatelessWidget {
  const CreatePostPickedImageIcon({
    required this.onPressed,
    super.key,
  });
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.red,
        shape: BoxShape.circle,
      ),
      height: 17,
      padding: const EdgeInsets.all(5),
      child: AppIconButton(
        icon: AppAssets.crossIcon,
        size: 11,
        iconSize: 10,
        color: AppColors.white,
        onPressed: onPressed,
      ),
    );
  }
}
