// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tynt_web/auth/models/user_model.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/user/repository/i_user_manager_repository.dart';
import 'package:tynt_web/user_profile/widget/user_list_tile.dart';
import 'package:tynt_web/utlity/easy_debouncer.dart';

class PostCommentUserSearchView extends StatefulWidget {
  const PostCommentUserSearchView({
    required this.query,
    required this.onUserTap,
    super.key,
  });
  final String query;
  final ValueChanged<UserModel> onUserTap;

  @override
  State<PostCommentUserSearchView> createState() => PostCommentUserSearchViewState();
}

class PostCommentUserSearchViewState extends State<PostCommentUserSearchView> {
  final usersList = ValueNotifier<List<UserModel>>([]);

  @override
  void initState() {
    super.initState();
    EasyDebounce.debounce('SearchUser_InComment', const Duration(milliseconds: 500), searchTags);
  }

  Future<void> searchTags() async {
    final failOrSuccess = await getIt<IUserManagerRepository>().getUsersList(search: widget.query, perPage: 20);
    failOrSuccess.fold(
      (l) {
        log('Errror $l');
      },
      (r) {
        if (mounted) {
          usersList.value = [...r.users];
        }
      },
    );
  }

  @override
  void dispose() {
    usersList.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<List<UserModel>>(
      valueListenable: usersList,
      builder: (_, users, __) {
        if (users.isEmpty) return const SizedBox.shrink();
        return Container(
          constraints: const BoxConstraints(maxHeight: 200),
          decoration: const BoxDecoration(
            color: AppColors.background,
            borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
          ),
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 14),
            itemBuilder: (context, index) {
              final user = users[index];
              return UserListTile(
                name: user.fullName ?? '',
                userName: user.userName,
                url: user.formmtedAvatarProfile ?? '',
                showFollowButton: false,
                iconRadius: 16,
                onProfileTap: () {
                  widget.onUserTap(user);
                },
                userNameColor: AppColors.davyGrey,
                fontSize: 14,
              );
            },
            separatorBuilder: (_, __) => const Gap(10),
            itemCount: users.length,
          ),
        );
      },
    );
  }
}
