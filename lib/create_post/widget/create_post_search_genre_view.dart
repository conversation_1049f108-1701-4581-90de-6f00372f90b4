// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/constants/app_theme.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/post_preview/model/post_genres_model.dart';
import 'package:tynt_web/widgets/dailog_boxes.dart';

class CreatePostSearchGenreView extends StatelessWidget {
  const CreatePostSearchGenreView({
    super.key,
    this.onChanged,
    this.selectedGenres,
    this.title,
  });
  final ValueChanged<GenresModel?>? onChanged;
  final GenresModel? selectedGenres;
  final String? title;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        DailogBoxes.searchGenresDailog(context, onSelected: onChanged);
      },
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: IgnorePointer(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (title != null) ...[
              Text(
                title ?? '',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontSize: 14,
                      fontFamily: AppTheme.spaceGroteskFontFamily,
                    ),
              ),
              const Gap(6),
            ],
            DropdownSearch<GenresModel>(
              dropdownBuilder:
                  selectedGenres == null ? null : (context, selectedItem) => Text(selectedItem?.name ?? ''),
              onChanged: onChanged,
              compareFn: (item1, selected) => item1.id == selected.id,
              selectedItem: selectedGenres,
              popupProps: PopupProps.menu(
                showSearchBox: true,
                showSelectedItems: true,
                constraints: const BoxConstraints(maxHeight: 250),
                itemBuilder: (context, item, isDisabled, isSelected) {
                  return Text(
                    item.name ?? '',
                    style: context.textTheme.bodyMedium,
                  );
                },

                // itemBuilder: (context, item, isSelected) {
                //   return ListTile(
                //     title: Text(
                //       item.name ?? '',
                //       style: context.textTheme.bodyMedium,
                //     ),
                //   );
                // },
              ),
              items: (filter, loadProps) {
                return [];
              },
              decoratorProps: DropDownDecoratorProps(
                decoration: InputDecoration(
                  suffixIconColor: Theme.of(context).textTheme.headlineMedium?.color,
                  hintText: context.l10n.selectGenre,
                  hintStyle: context.theme.inputDecorationTheme.hintStyle?.copyWith(
                    fontSize: 15,
                  ),
                ),
              ),
              // asyncItems: (text) async {
              //   final response = await getIt<IUserManagerRepository>().getGenresList(search: text.trim());

              //   return response.getRightFolded()?.genresList ?? <GenresModel>[];
              // },
              // dropdownDecoratorProps: DropDownDecoratorProps(
              //   dropdownSearchDecoration: InputDecoration(
              //     hintText: context.l10n.select,
              //     suffixIconColor: Theme.of(context).textTheme.headlineMedium?.color,
              //     hintStyle: context.theme.inputDecorationTheme.hintStyle?.copyWith(
              //       fontSize: context.isMobile ? 15 : 18,
              //       color: AppColors.text,
              //     ),
              //   ),
              // ),
            ),
          ],
        ),
      ),
    );
  }
}
