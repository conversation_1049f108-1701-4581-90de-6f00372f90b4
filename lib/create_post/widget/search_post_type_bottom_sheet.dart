// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:tynt_web/auth/cubit/theme_cubit.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/post_preview/model/post_type_model.dart';
import 'package:tynt_web/post_preview/repository/i_post_repository.dart';
import 'package:tynt_web/utlity/easy_debouncer.dart';
import 'package:tynt_web/utlity/enum/user_type_enum.dart';
import 'package:tynt_web/utlity/logger_config.dart';
import 'package:tynt_web/utlity/pagination/pagination_mixin.dart';
import 'package:tynt_web/utlity/utlity.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/app_text_form_field.dart';
import 'package:tynt_web/widgets/custom_progress_indicator.dart';
import 'package:tynt_web/widgets/no_data.dart';
import 'package:tynt_web/widgets/popup_wrapper.dart';

class SearchPostBottomSheet extends StatefulWidget {
  const SearchPostBottomSheet({
    super.key,
    this.onPostTypeSelected,
  });
  final ValueChanged<PostTypeModel>? onPostTypeSelected;

  @override
  State<SearchPostBottomSheet> createState() => SearchPostBottomSheetState();
}

class SearchPostBottomSheetState extends State<SearchPostBottomSheet> with PaginationMixin {
  final isLoading = ValueNotifier<bool>(false);
  final postTypeList = ValueNotifier<List<PostTypeModel>>(<PostTypeModel>[]);
  final isLoadingMore = ValueNotifier<bool>(false);
  final searchController = TextEditingController();
  int page = 1;
  bool hasReachedMax = false;

  @override
  void initState() {
    super.initState();
    initiatePagination();
    getPostTypeList();
  }

  @override
  void dispose() {
    isLoading.dispose();
    isLoadingMore.dispose();
    postTypeList.dispose();
    searchController.dispose();
    disposePagination();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopUpWrapper(
      childrenPadding: const EdgeInsets.fromLTRB(20, 20, 20, 30),
      children: [
        BlocBuilder<ThemeCubit, ThemeState>(
          builder: (context, state) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  alignment: Alignment.centerRight,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          context.l10n.selectPostType,
                          style: context.textTheme.bodyLarge?.copyWith(
                            fontSize: 18,
                            letterSpacing: 1,
                            fontWeight: FontWeight.w700,
                            color: state.themeMode == ThemeMode.dark && state.currentUserType == UserType.normal
                                ? AppColors.headingTextDark
                                : AppColors.text,
                          ),
                        ),
                      ],
                    ),
                    InkWell(
                      onTap: () => Navigator.of(context).pop(),
                      child: AppImageAsset(
                        AppAssets.closeIcon,
                        height: 24,
                        width: 24,
                        color: state.themeMode == ThemeMode.dark && state.currentUserType == UserType.normal
                            ? AppColors.headingTextDark
                            : AppColors.text,
                      ),
                    ),
                  ],
                ),
                const Gap(10),
                AppTextFormField(
                  controller: searchController,
                  hintText: context.l10n.search,
                  onChanged: (value) {
                    EasyDebounce.debounce('PostType_search', const Duration(milliseconds: 500), getPostTypeList);
                  },
                ),
                ValueListenableBuilder<bool>(
                  valueListenable: isLoading,
                  builder: (_, value, __) {
                    if (value) {
                      return SizedBox(
                        height: MediaQuery.sizeOf(context).height * 0.25,
                        child: const CustomProgressIndicator(),
                      );
                    }
                    return ValueListenableBuilder<List<PostTypeModel>>(
                      valueListenable: postTypeList,
                      builder: (_, typesList, __) {
                        if (typesList.isEmpty && searchController.text.isNotEmpty) {
                          return SizedBox(
                            height: MediaQuery.sizeOf(context).height * 0.4,
                            child: NoData(
                              text: context.l10n.noPostTypeFound,
                            ),
                          );
                        }
                        return SizedBox(
                          height: MediaQuery.sizeOf(context).height * 0.4,
                          child: RawScrollbar(
                            thumbColor: AppColors.border,
                            radius: const Radius.circular(15),
                            thickness: 6,
                            controller: scrollPaginationController,
                            minThumbLength: 20,
                            trackVisibility: true,
                            crossAxisMargin: 3,
                            child: ValueListenableBuilder<bool>(
                              valueListenable: isLoadingMore,
                              builder: (_, valueMore, __) {
                                return ConstrainedBox(
                                  constraints: BoxConstraints(
                                    maxWidth: Utility.maxWidth(context).maxWidth,
                                  ),
                                  child: ListView.separated(
                                    shrinkWrap: true,
                                    controller: scrollPaginationController,
                                    physics: const AlwaysScrollableScrollPhysics(),
                                    padding: const EdgeInsets.only(bottom: 16, top: 8),
                                    itemBuilder: (_, index) {
                                      if (index == typesList.length && valueMore) {
                                        return const SizedBox(
                                          height: 50,
                                          child: CustomProgressIndicator(),
                                        );
                                      }
                                      final item = typesList[index];
                                      return InkWell(
                                        onTap: () {
                                          widget.onPostTypeSelected?.call(item);
                                          context.pop();
                                        },
                                        overlayColor: WidgetStateProperty.all(AppColors.transparent),
                                        child: Text(
                                          item.name ?? '',
                                          style: context.textTheme.bodyLarge?.copyWith(
                                            color: state.themeMode == ThemeMode.dark &&
                                                    state.currentUserType == UserType.normal
                                                ? AppColors.headingTextDark
                                                : AppColors.text,
                                          ),
                                        ),
                                      );
                                    },
                                    separatorBuilder: (_, __) => const Gap(20),
                                    itemCount: valueMore ? typesList.length + 1 : typesList.length,
                                  ),
                                );
                              },
                            ),
                          ),
                        );
                      },
                    );
                  },
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Future<void> getPostTypeList() async {
    isLoading.value = true;

    final failOrSuccess = await getIt<IPostRepository>().getPostTypeList(
      search: searchController.text.trim(),
    );

    failOrSuccess.fold((l) {
      isLoading.value = false;
      debugError(l);
    }, (r) {
      isLoading.value = false;
      postTypeList.value = [...r.data];
      hasReachedMax = r.data.length < 10;
    });
  }

  Future<void> loadMore() async {
    isLoadingMore.value = true;

    final failOrSuccess = await getIt<IPostRepository>().getPostTypeList(
      search: searchController.text.trim(),
      page: page + 1,
    );

    failOrSuccess.fold((l) {
      isLoadingMore.value = false;
    }, (r) {
      isLoadingMore.value = false;
      postTypeList.value = [...postTypeList.value, ...r.data];
      hasReachedMax = r.data.length < 10;
      page += 1;
    });
  }

  @override
  void onReachedLast() {
    if (!isLoading.value && !isLoadingMore.value && !hasReachedMax) {
      EasyDebounce.debounce('POST_TYPE_PAGINATION', const Duration(milliseconds: 500), loadMore);
    }
  }
}
