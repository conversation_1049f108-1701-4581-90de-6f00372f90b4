// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/post_preview/model/comman_name_model.dart';
import 'package:tynt_web/post_preview/repository/i_post_repository.dart';
import 'package:tynt_web/utlity/easy_debouncer.dart';

class SearchMovieListView extends StatefulWidget {
  const SearchMovieListView({
    required this.query,
    required this.onMovieTap,
    super.key,
  });
  final String query;
  final ValueChanged<CommonNameModel> onMovieTap;

  @override
  State<SearchMovieListView> createState() => SearchMovieListViewState();
}

class SearchMovieListViewState extends State<SearchMovieListView> {
  final moviesListList = ValueNotifier<List<CommonNameModel>>([]);

  @override
  void initState() {
    super.initState();
    EasyDebounce.debounce(
      'movie_search',
      const Duration(milliseconds: 500),
      searchTags,
    );
  }

  Future<void> searchTags() async {
    final failOrSuccess = await getIt<IPostRepository>().getMoviesList(search: widget.query);
    failOrSuccess.fold(
      (l) {
        log('Errror $l');
      },
      (r) {
        if (context.mounted) {
          moviesListList.value = [...r.data];
        }
      },
    );
  }

  @override
  void dispose() {
    moviesListList.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<List<CommonNameModel>>(
      valueListenable: moviesListList,
      builder: (_, tags, __) {
        if (tags.isEmpty) return const SizedBox.shrink();
        return Container(
          constraints: const BoxConstraints(maxHeight: 200),
          decoration: const BoxDecoration(
            color: AppColors.background,
            borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
          ),
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 14),
            itemBuilder: (context, index) {
              final movie = tags[index];
              return InkWell(
                onTap: () {
                  widget.onMovieTap(movie);
                },
                child: Text(
                  movie.name ?? '',
                  style: context.textTheme.bodyLarge,
                ),
              );
            },
            separatorBuilder: (_, __) => const Gap(20),
            itemCount: tags.length,
          ),
        );
      },
    );
  }
}
