// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:multi_trigger_autocomplete/multi_trigger_autocomplete.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/create_post/view/tags_search_list_view.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/post_preview/model/comman_name_model.dart';
import 'package:tynt_web/widgets/app_text_form_field.dart';

class WriteTagWidget extends StatefulWidget {
  const WriteTagWidget({
    required this.onHashTagTap,
    required this.onHashTagChange,
    super.key,
    this.initializeText,
    this.controller,
  });
  final ValueChanged<CommonNameModel> onHashTagTap;
  final void Function(String text) onHashTagChange;
  final String? initializeText;
  final TextEditingController? controller;

  @override
  State<WriteTagWidget> createState() => WriteTagWidgetState();
}

class WriteTagWidgetState extends State<WriteTagWidget> {
  final _controller = TextEditingController();
  final _fcNode = FocusNode();

  @override
  void initState() {
    super.initState();

    if (widget.initializeText != null && _controller.text != widget.initializeText) {
      _controller.text = widget.initializeText!;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _fcNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiTriggerAutocomplete(
      optionsAlignment: OptionsAlignment.topStart,
      textEditingController: widget.controller ?? _controller,
      focusNode: _fcNode,
      autocompleteTriggers: [
        AutocompleteTrigger(
          trigger: '#',
          minimumRequiredCharacters: 2,
          optionsViewBuilder: (context, autocompleteQuery, controller) {
            return TagsSearchListView(
              query: autocompleteQuery.query,
              onHashTagTap: (value) {
                widget.onHashTagTap(value);
                MultiTriggerAutocomplete.of(context).acceptAutocompleteOption(value.name ?? '');
                widget.onHashTagChange(widget.controller?.text ?? _controller.text);
              },
            );
          },
        ),
      ],
      fieldViewBuilder: (context, textEditingController, focusNode) {
        return AppTextFormField(
          maxWidth: MediaQuery.sizeOf(context).width,
          focusNode: focusNode,
          margin: EdgeInsets.zero,
          hintStyle: context.theme.inputDecorationTheme.hintStyle?.copyWith(
            color: AppColors.subText,
          ),
          onChanged: (value) => widget.onHashTagChange(value),
          controller: textEditingController,
          hintText: context.l10n.addTags,
          validator: (value) {
            if (value == null || value.trim().isEmpty) return null;
            final kValue = value.trim();
            if (!kValue.startsWith('#')) {
              return context.l10n.tagErrorMsg;
            }
            final allStringsList = kValue.split(' ');
            if (allStringsList.isNotEmpty && allStringsList.any((element) => !element.trim().startsWith('#'))) {
              return context.l10n.tagErrorMsg;
            }
            return null;
          },
        );
      },
    );
  }
}
