// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/constants/app_theme.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/tomes/model/tomes_model.dart';
import 'package:tynt_web/tomes/widget/toms_view_widget.dart';

class CreatePostTomsListView extends StatelessWidget {
  const CreatePostTomsListView({
    required this.toms,
    required this.selectedToms,
    required this.onSelectTome,
    super.key,
  });
  final List<TomesModel> toms;
  final List<TomesModel> selectedToms;
  final void Function(TomesModel) onSelectTome;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.l10n.addToTome,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontSize: 15,
                fontFamily: AppTheme.spaceGroteskFontFamily,
                color: AppColors.black,
              ),
        ),
        const Gap(16),
        SizedBox(
          height: 120,
          child: ListView(
            scrollDirection: Axis.horizontal,
            clipBehavior: Clip.none,
            children: [
              ...List.generate(toms.length, (index) {
                return Padding(
                  padding: index == 0 ? EdgeInsets.zero : const EdgeInsets.only(left: 14),
                  child: TomsViewWidget(
                    tomes: toms[index],
                    showActionButton: false,
                    isSelected: selectedToms.any((element) => element.id == toms[index].id),
                    onTap: () {
                      onSelectTome(toms[index]);
                    },
                  ),
                );
              }),
            ],
          ),
        ),
      ],
    );
  }
}
