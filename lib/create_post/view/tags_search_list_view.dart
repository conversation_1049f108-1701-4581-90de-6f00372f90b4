// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/post_preview/model/comman_name_model.dart';
import 'package:tynt_web/post_preview/repository/i_post_repository.dart';

class TagsSearchListView extends StatefulWidget {
  const TagsSearchListView({
    required this.query,
    required this.onHashTagTap,
    super.key,
  });
  final String query;
  final ValueChanged<CommonNameModel> onHashTagTap;

  @override
  State<TagsSearchListView> createState() => TagsSearchListViewState();
}

class TagsSearchListViewState extends State<TagsSearchListView> {
  final tagsList = ValueNotifier<List<CommonNameModel>>([]);

  @override
  void initState() {
    super.initState();
    searchTags();
  }

  Future<void> searchTags() async {
    final failOrSuccess = await getIt<IPostRepository>().getTagsList(search: widget.query);
    failOrSuccess.fold(
      (l) {
        log('Errror $l');
      },
      (r) {
        if (context.mounted) {
          tagsList.value = [...r.data];
        }
      },
    );
  }

  @override
  void dispose() {
    tagsList.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<List<CommonNameModel>>(
      valueListenable: tagsList,
      builder: (_, tags, __) {
        if (tags.isEmpty) return const SizedBox.shrink();
        return Container(
          constraints: const BoxConstraints(maxHeight: 200),
          decoration: const BoxDecoration(
            color: AppColors.background,
            borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
          ),
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 14),
            itemBuilder: (context, index) {
              final tag = tags[index];
              return InkWell(
                onTap: () {
                  widget.onHashTagTap(tag);
                },
                child: Row(
                  children: [
                    Text(
                      tag.name ?? '',
                      style: context.textTheme.bodyLarge,
                    ),
                  ],
                ),
              );
            },
            separatorBuilder: (_, __) => const Gap(6),
            itemCount: tags.length,
          ),
        );
      },
    );
  }
}
