import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/app/cubit/refresh_cubit.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/post_preview/model/post_model.dart';
import 'package:tynt_web/post_preview/model/post_type_model.dart';
import 'package:tynt_web/post_preview/repository/i_post_repository.dart';
import 'package:tynt_web/post_preview/widget/post_description_view.dart';
import 'package:tynt_web/post_preview/widget/post_geners_item_view.dart';
import 'package:tynt_web/post_preview/widget/post_header_view.dart';
import 'package:tynt_web/post_preview/widget/post_tag_item_view.dart';
import 'package:tynt_web/routes/app_routes.dart';
import 'package:tynt_web/utlity/utlity.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/app_image_network.dart';
import 'package:tynt_web/widgets/custom_progress_indicator.dart';
import 'package:tynt_web/widgets/dailog_boxes.dart';

class PostDetailPage extends StatefulWidget {
  const PostDetailPage({super.key, this.postId, this.isShared = false});
  final int? postId;
  final bool isShared;

  @override
  State<PostDetailPage> createState() => _PostDetailPageState();
}

class _PostDetailPageState extends State<PostDetailPage> {
  final postModel = ValueNotifier<PostModel?>(null);
  final loading = ValueNotifier<bool>(false);
  final stackLoading = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    getPostDetail();
  }

  Future<void> getPostDetail() async {
    loading.value = true;
    final failOrSuccess = await getIt<IPostRepository>().getPostDetail(widget.postId ?? 0);
    failOrSuccess.fold(
      (l) {
        Utility.toast(message: l.message);
        loading.value = false;
      },
      (r) {
        if (context.mounted) {
          postModel.value = r.post;
        }
        loading.value = false;
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RefreshCubit, RefreshState>(
      listenWhen: (previous, current) => previous.postDetail != current.postDetail,
      listener: (context, state) {
        if (state.postDetail != null) {
          postModel.value = state.postDetail;
        }
      },
      child: ValueListenableBuilder(
          valueListenable: loading,
          builder: (context, loading, _) {
            return ValueListenableBuilder<PostModel?>(
                valueListenable: postModel,
                builder: (context, postDetail, _) {
                  if (loading) {
                    return const PostDetailLoadingView();
                  }
                  return Stack(
                    children: [
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                          child: ConstrainedBox(
                            constraints: const BoxConstraints(
                              maxWidth: 716,
                            ),
                            child: Column(
                              children: [
                                AppBar(
                                  leadingWidth: 24,
                                  titleSpacing: 0,
                                  leading: InkWell(
                                    onTap: () {
                                      context.pop();
                                    },
                                    child: const Center(
                                        child: AppImageAsset(AppAssets.backArrowIcon, height: 24, width: 24)),
                                  ),
                                  title: Padding(
                                    padding: const EdgeInsets.only(left: 24),
                                    child: Text(
                                      context.l10n.post,
                                      style: context.textTheme.bodyMedium?.copyWith(
                                        fontSize: 20,
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                  ),
                                  actions: [
                                    if (!widget.isShared)
                                      IconButton(
                                        constraints: const BoxConstraints(),
                                        padding: EdgeInsets.zero,
                                        splashRadius: 18,
                                        onPressed: () {
                                          context.pushNamed(
                                            AppRoutes.editPost.name,
                                            pathParameters: <String, String>{
                                              'id': widget.postId.toString(),
                                            },
                                          );
                                        },
                                        icon: AppImageAsset(
                                          AppAssets.penIcon,
                                          height: context.isMobile ? 16 : 22,
                                          width: context.isMobile ? 16 : 22,
                                        ),
                                      ),
                                    const Gap(24),
                                    if (!widget.isShared)
                                      IconButton(
                                        constraints: const BoxConstraints(),
                                        padding: EdgeInsets.zero,
                                        splashRadius: 18,
                                        onPressed: () {
                                          DailogBoxes.deletePostDailog(
                                            context,
                                            onDeletePostTap: deletePost,
                                          );
                                        },
                                        icon: AppImageAsset(
                                          AppAssets.binIcon,
                                          height: context.isMobile ? 18 : 24,
                                          width: context.isMobile ? 18 : 24,
                                        ),
                                      ),
                                  ],
                                ),
                                Flexible(
                                  child: SingleChildScrollView(
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(12),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          if (postDetail?.imageUrl != null)
                                            AppImageNetwork(
                                              url: postDetail?.imageUrl ?? '',
                                              width: 716,
                                            ),
                                          Container(
                                            padding: const EdgeInsets.all(20),
                                            width: double.infinity,
                                            constraints: const BoxConstraints(
                                              maxWidth: 716,
                                            ),
                                            decoration: const BoxDecoration(
                                              color: AppColors.white,
                                            ),
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                if (postDetail?.title != null)
                                                  Text(
                                                    postDetail?.title ?? '',
                                                    style: context.textTheme.bodyMedium?.copyWith(fontSize: 32),
                                                  ),
                                                const Gap(10),
                                                Row(
                                                  children: [
                                                    Expanded(
                                                      child: Skeleton.leaf(
                                                        child: PostHeaderView(
                                                          fontSize: 14,
                                                          title: postDetail?.postType?.name ?? '',
                                                          date: postDetail?.formmtedCreatedAt ?? '',
                                                          onPostTypeTap: () {
                                                            // context.navigatePostTypeDetailPage(
                                                            //   widget.postModel.postType?.id,
                                                            //   widget.postModel.postType?.name,
                                                            // );
                                                          },
                                                        ),
                                                      ),
                                                    ),
                                                    Row(
                                                      mainAxisAlignment: MainAxisAlignment.end,
                                                      children: [
                                                        if (postDetail?.isDraft == 1)
                                                          Skeleton.leaf(
                                                            child: Container(
                                                              padding: const EdgeInsets.symmetric(
                                                                  horizontal: 10, vertical: 2),
                                                              decoration: const BoxDecoration(
                                                                color: AppColors.background,
                                                                borderRadius: BorderRadius.all(Radius.circular(5)),
                                                              ),
                                                              child: Text(
                                                                context.l10n.draft,
                                                                style: context.textTheme.bodyMedium?.copyWith(
                                                                  fontSize: context.isMobile ? 12 : 16,
                                                                  color: AppColors.red,
                                                                  fontWeight: FontWeight.w400,
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        if (postDetail?.genre != null)
                                                          PostGenersItemView(
                                                            text: postDetail?.genre?.name ?? '',
                                                            onPressed: () {
                                                              // context.navigateGenrePostDetailPage(
                                                              //   widget.postModel.genre?.id,
                                                              //   widget.postModel.genre?.name,
                                                              // );
                                                            },
                                                          ),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                                if (postDetail?.description != null) ...[
                                                  const Gap(16),
                                                  PostDescriptionView(
                                                    isForPostDetail: true,
                                                    postType: postDetail?.postType ?? const PostTypeModel(id: 0),
                                                    text: postDetail?.description ?? '',
                                                  ),
                                                ],
                                                if (postDetail?.tags != null && postDetail!.tags.isNotEmpty) ...[
                                                  const Gap(16),
                                                  Wrap(
                                                    runSpacing: context.isMobile ? 8 : 16,
                                                    spacing: context.isMobile ? 8 : 16,
                                                    children: List.generate(
                                                      postDetail.tags.length,
                                                      (index) => PostTagItemView(
                                                        text: '#${postDetail.tags[index].name}',
                                                        fontSize: context.isMobile ? 15 : 20,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      ValueListenableBuilder<bool>(
                        valueListenable: stackLoading,
                        builder: (context, loadingStack, _) {
                          if (!loadingStack) return const SizedBox.shrink();
                          return const CustomProgressIndicator();
                        },
                      ),
                    ],
                  );
                });
          }),
    );
  }

  Future<void> deletePost() async {
    stackLoading.value = true;
    final failOrSuccess = await getIt<IPostRepository>().deletePost(widget.postId ?? 0);
    failOrSuccess.fold(
      (l) {
        Utility.toast(message: l.message);
        stackLoading.value = false;
      },
      (r) {
        Utility.toast(message: r.message);

        context.read<RefreshCubit>().deletePostDetail(PostModel(id: widget.postId ?? 0));
        stackLoading.value = false;
        context.pop();
      },
    );
  }
}

class PostDetailLoadingView extends StatelessWidget {
  const PostDetailLoadingView({
    super.key,
    this.isSliverLoading = false,
    this.padding,
  });
  final bool isSliverLoading;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      child: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
          child: ConstrainedBox(
            constraints: const BoxConstraints(
              maxWidth: 716,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),

                Container(
                  width: 300,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                const SizedBox(height: 12),
                // Post description skeleton
                ...List.generate(
                  25,
                  (index) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Container(
                      width: double.infinity,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
