import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:tynt_web/app/cubit/refresh_cubit.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/constants/app_theme.dart';
import 'package:tynt_web/create_post/view/create_post_toms_list_view.dart';
import 'package:tynt_web/create_post/widget/create_post_search_genre_view.dart';
import 'package:tynt_web/create_post/widget/select_post_type_search_dropdown.dart';
import 'package:tynt_web/create_post/widget/write_tag_widget.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/post_preview/model/comman_name_model.dart';
import 'package:tynt_web/post_preview/model/post_genres_model.dart';
import 'package:tynt_web/post_preview/model/post_type_model.dart';
import 'package:tynt_web/post_preview/repository/i_post_repository.dart';
import 'package:tynt_web/primary_button/utility/primary_button_utlity.dart';
import 'package:tynt_web/primary_button/views/primary_button_view.dart';
import 'package:tynt_web/tomes/model/tomes_model.dart';
import 'package:tynt_web/tomes/view/my_account_tomes_list_view.dart';
import 'package:tynt_web/utlity/utlity.dart';
import 'package:tynt_web/widgets/popup_wrapper.dart';

class PostSettingDailogView extends StatefulWidget {
  const PostSettingDailogView({
    required this.postId,
    super.key,
  });
  final int postId;

  @override
  State<PostSettingDailogView> createState() => _PostSettingDailogViewState();
}

class _PostSettingDailogViewState extends State<PostSettingDailogView> {
  final isLoadingPostDetail = ValueNotifier<bool>(false);
  final isLoadingToms = ValueNotifier<bool>(false);
  final isLoadingpostType = ValueNotifier<bool>(false);
  // final isLoadingEditPost = ValueNotifier<bool>(false);
  final isLoadingEditPostButtoncontroller = PrimaryLoadingButtonController();
  final selectedpostType = ValueNotifier<PostTypeModel?>(null);
  final selectedpostGenere = ValueNotifier<GenresModel?>(null);
  final tomsList = ValueNotifier<List<TomesModel>>([]);
  final selectedToms = ValueNotifier<List<TomesModel>>([]);
  final hashTagController = TextEditingController();
  List<CommonNameModel> hasTagList = [];

  @override
  void initState() {
    super.initState();
    getPostDetail();
    getToms();
  }

  Future<void> getPostDetail() async {
    isLoadingPostDetail.value = true;

    final failOrGetPostDetail = await getIt<IPostRepository>().getPostDetail(widget.postId);

    failOrGetPostDetail.fold(
      (l) {
        Utility.toast(message: l.message);
        isLoadingPostDetail.value = false;
      },
      (r) {
        selectedpostType.value = r.post?.postType;
        selectedpostGenere.value = r.post?.genre;
        hashTagController.text = r.post?.tags.map((e) => '#${e.name}').join(' ') ?? '';
        hasTagList = r.post?.tags ?? [];
        selectedToms.value = r.post?.tomes ?? [];
        isLoadingPostDetail.value = false;
        if (selectedpostType.value == null) {
          getPostType();
        }
      },
    );
  }

  Future<void> getToms() async {
    isLoadingToms.value = true;
    final failOrSuccess = await getIt<IPostRepository>().getTomesList();

    failOrSuccess.fold((l) {
      isLoadingToms.value = false;
      Utility.toast(message: l.message);
    }, (r) {
      tomsList.value = r.data;
      isLoadingToms.value = false;
    });
  }

  Future<void> getPostType() async {
    isLoadingpostType.value = true;
    final failOrSuccess = await getIt<IPostRepository>().getPostTypeList(perPage: 1);

    failOrSuccess.fold((l) {
      isLoadingpostType.value = false;
      Utility.toast(message: l.message);
    }, (r) {
      selectedpostType.value = r.data.firstOrNull;
      isLoadingpostType.value = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopUpWrapper(
      childrenPadding: const EdgeInsets.fromLTRB(20, 20, 20, 30),
      children: [
        Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.l10n.postSetting,
              style: context.textTheme.bodyLarge?.copyWith(
                fontSize: 24,
                fontWeight: FontWeight.w700,
                fontFamily: AppTheme.spaceGroteskFontFamily,
                color: AppColors.text,
              ),
            ),
            const Gap(20),
            ValueListenableBuilder<bool>(
              valueListenable: isLoadingToms,
              builder: (context, loading, _) {
                if (loading) {
                  return const MyAccountTomesLoadingListView();
                }
                return ValueListenableBuilder<List<TomesModel>>(
                  valueListenable: selectedToms,
                  builder: (context, selected, _) {
                    return ValueListenableBuilder<List<TomesModel>>(
                      valueListenable: tomsList,
                      builder: (context, list, _) {
                        if (list.isEmpty) return const SizedBox.shrink();
                        return CreatePostTomsListView(
                          toms: list,
                          onSelectTome: (tome) {
                            if (selected.any((element) => element.id == tome.id)) {
                              selectedToms.value = [...selected]..removeWhere((element) => element.id == tome.id);
                            } else {
                              selectedToms.value = [...selected, tome];
                            }
                          },
                          selectedToms: selected,
                        );
                      },
                    );
                  },
                );
              },
            ),
            const Gap(20),
            Row(
              children: [
                Flexible(
                  child: ValueListenableBuilder<PostTypeModel?>(
                    valueListenable: selectedpostType,
                    builder: (context, selected, _) {
                      return SelectPostTypeSearchDropdown(
                        title: context.l10n.typeOfPost,
                        selectedPostType: selected,
                        onChanged: (value) {
                          if (value == null) return;
                          selectedpostType.value = value;
                        },
                      );
                    },
                  ),
                ),
                const Gap(16),
                Flexible(
                  child: ValueListenableBuilder<GenresModel?>(
                    valueListenable: selectedpostGenere,
                    builder: (context, geners, _) {
                      return CreatePostSearchGenreView(
                        title: context.l10n.genre,
                        selectedGenres: geners,
                        onChanged: (value) {
                          if (value == null) return;
                          selectedpostGenere.value = value;
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
            const Gap(16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.l10n.addTags,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontSize: 15,
                        fontFamily: AppTheme.spaceGroteskFontFamily,
                      ),
                ),
                const Gap(6),
                WriteTagWidget(
                  controller: hashTagController,
                  onHashTagTap: (value) {
                    // context.read<CreateEditPostCubit>().changeHashTagList(tagModel: value);
                    hasTagList = [...hasTagList, value];
                  },
                  onHashTagChange: (text) {
                    // context.read<CreateEditPostCubit>().changeHashTag(text);
                    // hasTagString = text;
                  },
                ),
              ],
            ),
            const Gap(20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                PrimaryButton(
                  width: 164,
                  onPressed: () {
                    context.pop();
                  },
                  text: context.l10n.cancel,
                  backgroundColor: AppColors.transparent,
                  borderColor: AppColors.primary,
                  textColor: AppColors.primary,
                ),
                const Gap(15),
                PrimaryButton(
                  width: 164,
                  onPressed: editPost,
                  primaryLoadingButtonController: isLoadingEditPostButtoncontroller,
                  text: context.l10n.done,
                ),
              ],
            ),
          ],
        )
      ],
    );
  }

  Future<void> editPost() async {
    isLoadingEditPostButtoncontroller.start();
    final allMentionHasgTag = Utility.getAllHashTag(hashTagController.text);
    var newHashTag = <String>[];
    var appHashTags = <CommonNameModel>[];

    for (final hashTag in allMentionHasgTag) {
      final tag = hasTagList.firstWhereOrNull((element) => element.name == hashTag);
      if (tag != null) {
        appHashTags = [...appHashTags, tag];
      } else {
        newHashTag = [...newHashTag, hashTag];
      }
    }
    final failOrSucess = await getIt<IPostRepository>().editPost(
      postId: widget.postId,
      genresId: selectedpostGenere.value?.id,
      postTypeId: selectedpostType.value?.id,
      tomsIds: [
        ...[...selectedToms.value]..removeWhere((element) => element.id == null),
      ].map((e) => e.id!).toList(),
      tagsIds: appHashTags.map((e) => e.id).toList(),
      newTags: newHashTag,
    );
    failOrSucess.fold(
      (l) {
        Utility.toast(message: l.message);
        isLoadingEditPostButtoncontroller.error();
      },
      (r) {
        if (r.post != null) {
          context.read<RefreshCubit>().refreshPostDetail(r.post!);
        }
        isLoadingEditPostButtoncontroller.success();
        Utility.toast(message: r.message);
        context.pop();
      },
    );
  }
}
