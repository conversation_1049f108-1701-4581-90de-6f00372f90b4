// ignore_for_file: unawaited_futures, use_build_context_synchronously

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_quill/quill_delta.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/app/cubit/refresh_cubit.dart';
import 'package:tynt_web/auth/models/app_file_data.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/constants/app_theme.dart';
import 'package:tynt_web/create_post/cubit/create_edit_post_cubit.dart';
import 'package:tynt_web/create_post/widget/create_post_description_view.dart';
import 'package:tynt_web/create_post/widget/create_post_image_view.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/post_preview/model/post_type_model.dart';
import 'package:tynt_web/primary_button/primary_button.dart';
import 'package:tynt_web/routes/app_routes.dart';
import 'package:tynt_web/utlity/app_validation.dart';
import 'package:tynt_web/utlity/easy_debouncer.dart';
import 'package:tynt_web/utlity/tuples.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/app_text_form_field.dart';
import 'package:tynt_web/widgets/custom_progress_indicator.dart';
import 'package:tynt_web/widgets/custom_switch.dart';
import 'package:tynt_web/widgets/dailog_boxes.dart';

class CreatePostPage extends StatefulWidget {
  const CreatePostPage({super.key, this.id});
  final int? id;

  @override
  State<CreatePostPage> createState() => _CreatePostPageState();
}

class _CreatePostPageState extends State<CreatePostPage> {
  final titleController = TextEditingController();

  final tagsController = TextEditingController();

  final _validateKey = GlobalKey<FormState>();
  final descriptionDisplay = ValueNotifier<String?>(null);
  late final isForEdit = widget.id != null;
  final titleImage = ValueNotifier<AppFileData>(const AppFileData());
  final descriptionController = QuillController.basic();

  final isBulletListSelected = ValueNotifier<bool>(false);
  final isNumberListSelected = ValueNotifier<bool>(false);
  final isBoldSelected = ValueNotifier<bool>(false);
  final isItalicSelected = ValueNotifier<bool>(false);
  final isUnderlineSelected = ValueNotifier<bool>(false);
  final isQuoteSelected = ValueNotifier<bool>(false);
  final descriptionError = ValueNotifier<String?>(null);
  bool isPostCreated = false;

  @override
  void initState() {
    setInitialData();
    super.initState();
  }

  @override
  void dispose() {
    titleController.dispose();
    descriptionController
      ..removeListener(() {})
      ..dispose();
    tagsController.dispose();
    titleImage.dispose();
    isBulletListSelected.dispose();
    isNumberListSelected.dispose();
    isBoldSelected.dispose();
    isItalicSelected.dispose();
    isUnderlineSelected.dispose();
    isQuoteSelected.dispose();

    super.dispose();
  }

  void htmlEditorButtonToggaleListner() {
    descriptionController.addListener(() {
      final selectionStyle = descriptionController.getSelectionStyle();
      isBulletListSelected.value = selectionStyle.attributes.containsValue(Attribute.ul);
      isNumberListSelected.value = selectionStyle.attributes.containsValue(Attribute.ol);
      isBoldSelected.value = selectionStyle.containsKey(Attribute.bold.key);
      isItalicSelected.value = selectionStyle.containsKey(Attribute.italic.key);
      isUnderlineSelected.value = selectionStyle.containsKey(Attribute.underline.key);
      isQuoteSelected.value = selectionStyle.containsKey(Attribute.blockQuote.key);
      final state = context.read<CreateEditPostCubit>().state;
      if (jsonEncode(descriptionController.document.toDelta().toJson()) != state.description) {
        isPostCreated = true;
        context
            .read<CreateEditPostCubit>()
            .changeDescription(jsonEncode(descriptionController.document.toDelta().toJson()));
      }
    });
  }

  void setInitialData() {
    final state = context.read<CreateEditPostCubit>().state;

    // Init blank controller if not yet present
    final controller = descriptionController;
    htmlEditorButtonToggaleListner();

    titleController.text = state.title ?? '';

    final raw = state.description;
    if (raw == null || raw.trim().isEmpty) return;

    try {
      final parsed = jsonDecode(raw);
      if (parsed is! List || parsed.isEmpty) {
        final delta = Delta()..insert(raw);
        controller.document.compose(delta, ChangeSource.remote);
        return;
      }

      final delta = Delta.fromJson(parsed);
      if (delta.isEmpty) return;

      controller.document.compose(delta, ChangeSource.remote);

      controller.updateSelection(
        TextSelection.collapsed(offset: controller.document.length - 1),
        ChangeSource.remote,
      );
    } catch (e, stack) {
      debugPrint('❌ Failed to load delta: $e\n$stack');
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CreateEditPostCubit, CreateEditPostState>(
      listenWhen: (previous, current) =>
          previous.createPostId == null && current.createPostId != null && current.addPost != null,
      listener: (context, state) {
        context.read<RefreshCubit>().addNewPostDetail(state.addPost!);
      },
      child: BlocListener<CreateEditPostCubit, CreateEditPostState>(
        listenWhen: (previous, current) => previous.addPost != current.addPost,
        listener: (context, state) {
          context.read<RefreshCubit>().refreshPostDetail(state.addPost!);
        },
        child: BlocListener<CreateEditPostCubit, CreateEditPostState>(
          listenWhen: (previous, current) {
            var previousPlainDescription = '';
            var currentPlainDescription = '';
            if (previous.description != null && previous.description!.trim().isNotEmpty) {
              final previousDelta = Delta.fromJson(jsonDecode(previous.description?.trim() ?? '') as List);
              previousPlainDescription = Document.fromDelta(previousDelta).toPlainText().trim();
            }
            if (current.description != null && current.description!.trim().isNotEmpty) {
              final currentDelta = Delta.fromJson(jsonDecode(current.description?.trim() ?? '') as List);
              currentPlainDescription = Document.fromDelta(currentDelta).toPlainText().trim();
            }
            return previous.title?.trim() != current.title?.trim() ||
                previousPlainDescription != currentPlainDescription;
          },
          listener: (context, state) {
            if (isForEdit) return;
            EasyDebounce.debounce('autoSave', const Duration(seconds: 1), () {
              context.read<CreateEditPostCubit>().autoSavePost(showComments: state.showComments);
            });
          },
          child: BlocListener<CreateEditPostCubit, CreateEditPostState>(
            listenWhen: (previous, current) => previous.postModel == null && current.postModel != null,
            listener: (context, state) {
              setInitialData();
            },
            child: Stack(
              children: [
                SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Center(
                      child: ConstrainedBox(
                        constraints: const BoxConstraints(maxWidth: 588),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Gap(30),
                            Text(isForEdit ? context.l10n.editPost : context.l10n.createPost,
                                style:
                                    context.textTheme.bodyLarge?.copyWith(fontSize: 20, fontWeight: FontWeight.w700)),
                            const Gap(20),
                            Container(
                              width: double.infinity,
                              height: 538,
                              constraints: const BoxConstraints(maxWidth: 588),
                              padding: const EdgeInsets.fromLTRB(20, 24, 20, 0),
                              decoration: BoxDecoration(
                                color: AppColors.white,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Form(
                                key: _validateKey,
                                child: Center(
                                  child: BlocSelector<CreateEditPostCubit, CreateEditPostState, bool>(
                                    selector: (state) => state.isInitialLoading,
                                    builder: (context, isInitialLoading) {
                                      return Skeletonizer(
                                        enabled: false,
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            BlocSelector<CreateEditPostCubit, CreateEditPostState,
                                                Tuple2<bool, PostTypeModel>>(
                                              selector: (state) => tuple2(
                                                state.isForTyntY,
                                                state.selectedPosType ?? const PostTypeModel(id: 0),
                                              ),
                                              builder: (context, value) {
                                                return Theme(
                                                  data: Theme.of(context).copyWith(
                                                    inputDecorationTheme: const InputDecorationTheme(
                                                      hoverColor: Colors.transparent,
                                                    ),
                                                  ),
                                                  child: AppTextFormField(
                                                    isDense: true,
                                                    maxWidth: MediaQuery.sizeOf(context).width,
                                                    prefixIcon: !value.value1
                                                        ? null
                                                        : Padding(
                                                            padding: const EdgeInsets.only(top: 8),
                                                            child: Text(
                                                              '  Y ',
                                                              style: context.textTheme.headlineSmall?.copyWith(
                                                                fontSize: 18,
                                                                color: AppColors.primary,
                                                                height: 1.3,
                                                                fontWeight: FontWeight.w700,
                                                              ),
                                                            ),
                                                          ),
                                                    preFixIconConstraints: !value.value1
                                                        ? null
                                                        : const BoxConstraints(maxHeight: 30, maxWidth: 30),
                                                    isRequired: value.value2.isTitleRequiredValue,
                                                    controller: titleController,
                                                    cursorColor: Theme.of(context).brightness == Brightness.dark
                                                        ? AppColors.textDark
                                                        : AppColors.text,
                                                    fillColor: AppColors.transparent,
                                                    suffixIconConstraints: const BoxConstraints(
                                                      maxHeight: 40,
                                                      maxWidth: 40,
                                                    ),
                                                    suffixIcon: BlocSelector<CreateEditPostCubit, CreateEditPostState,
                                                        AppFileData?>(
                                                      selector: (state) => state.postImage,
                                                      builder: (context, postImage) {
                                                        if (postImage == null ||
                                                            (postImage.imageBytes == null &&
                                                                postImage.networkUrl == null)) {
                                                          return CreatePostImageEmptyView(
                                                            onPressed: () async {
                                                              final pickedImage = await ImagePicker()
                                                                  .pickImage(source: ImageSource.gallery);
                                                              if (pickedImage != null && context.mounted) {
                                                                final byte = await pickedImage.readAsBytes();
                                                                isPostCreated = true;
                                                                context.read<CreateEditPostCubit>().setImage(byte);
                                                                if (isForEdit) return;
                                                                final state = context.read<CreateEditPostCubit>().state;
                                                                await context.read<CreateEditPostCubit>().autoSavePost(
                                                                      image: byte,
                                                                      showComments: state.showComments,
                                                                    );
                                                              }
                                                            },
                                                          );
                                                        }
                                                        return CreatePostImageView(
                                                          onClosePressed: () {
                                                            context.read<CreateEditPostCubit>().clearPickedImage();
                                                            if (isForEdit) return;
                                                            final state = context.read<CreateEditPostCubit>().state;
                                                            context.read<CreateEditPostCubit>().autoSavePost(
                                                                  deleteImage: true,
                                                                  showComments: state.showComments,
                                                                );
                                                          },
                                                          imageUrl: postImage.networkUrl,
                                                          localFileBytes: postImage.imageBytes,
                                                        );
                                                      },
                                                    ),
                                                    border: const UnderlineInputBorder(
                                                      borderSide: BorderSide(color: AppColors.border),
                                                    ),
                                                    contentPadding: const EdgeInsets.only(bottom: 10),
                                                    hintStyle: context.theme.inputDecorationTheme.hintStyle?.copyWith(
                                                      color: AppColors.subText,
                                                      fontSize: 18,
                                                      fontWeight: FontWeight.w700,
                                                      letterSpacing: 0.5,
                                                      fontFamily: AppTheme.manropeFontFamily,
                                                    ),
                                                    textCapitalization: TextCapitalization.sentences,
                                                    margin: EdgeInsets.zero,
                                                    validator: (value) =>
                                                        AppValidation.titleValidation(context, value: value),
                                                    hintText: context.l10n.title,
                                                    onChanged: (value) {
                                                      context.read<CreateEditPostCubit>().changeTitle(
                                                            titleController.text.trim(),
                                                          );
                                                      isPostCreated = true;
                                                    },
                                                  ),
                                                );
                                              },
                                            ),
                                            const Gap(17),
                                            BlocSelector<CreateEditPostCubit, CreateEditPostState, PostTypeModel>(
                                              selector: (state) => state.selectedPosType ?? const PostTypeModel(id: 0),
                                              builder: (context, selectedPosType) {
                                                return ValueListenableBuilder<String?>(
                                                  valueListenable: descriptionError,
                                                  builder: (context, error, _) {
                                                    return CreatePostDescriptionView(
                                                      controller: descriptionController,
                                                      postType: selectedPosType,
                                                      error: error,
                                                      onMovieTap: (value) {
                                                        context.read<CreateEditPostCubit>().addMovies(value);
                                                      },
                                                      onUserTap: (value) {
                                                        context.read<CreateEditPostCubit>().addMentionUser(value);
                                                      },
                                                    );
                                                  },
                                                );
                                              },
                                            ),
                                            const Spacer(),
                                            const Gap(20),
                                            Column(
                                              mainAxisSize: MainAxisSize.min,
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: [
                                                    Expanded(
                                                      child: Row(
                                                        children: [
                                                          Text(
                                                            context.l10n.allowCommenting,
                                                            style: context.textTheme.bodyMedium?.copyWith(
                                                              fontSize: 12,
                                                              color: Theme.of(context).textTheme.bodyMedium?.color,
                                                            ),
                                                          ),
                                                          const Gap(20),
                                                          Skeleton.leaf(
                                                            child: BlocSelector<CreateEditPostCubit,
                                                                CreateEditPostState, bool>(
                                                              selector: (state) => state.showComments,
                                                              builder: (context, value) {
                                                                return CustomSwitch(
                                                                  height: 22,
                                                                  width: 45,
                                                                  padding: 3,
                                                                  toggleSize: 18,
                                                                  value: value,
                                                                  activeColor:
                                                                      Theme.of(context).brightness == Brightness.dark
                                                                          ? AppColors.headingTextDark
                                                                          : AppColors.primary,
                                                                  inactiveColor:
                                                                      Theme.of(context).brightness == Brightness.dark
                                                                          ? AppColors.headingTextDark
                                                                          : AppColors.secondary,
                                                                  toggleColor:
                                                                      Theme.of(context).brightness == Brightness.dark
                                                                          ? AppColors.primary
                                                                          : AppColors.white,
                                                                  onToggle: (__) {
                                                                    if (isForEdit) return;
                                                                    if (context
                                                                        .read<CreateEditPostCubit>()
                                                                        .state
                                                                        .isForTyntY) {
                                                                      return;
                                                                    }
                                                                    context
                                                                        .read<CreateEditPostCubit>()
                                                                        .changeShowComments();
                                                                    isPostCreated = true;
                                                                    EasyDebounce.debounce(
                                                                        'comment', const Duration(seconds: 1), () {
                                                                      context.read<CreateEditPostCubit>().autoSavePost(
                                                                            showComments: !value,
                                                                          );
                                                                    });
                                                                  },
                                                                );
                                                              },
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                ValueListenableBuilder<bool>(
                                                  valueListenable: isQuoteSelected,
                                                  builder: (context, quoteSelected, _) {
                                                    return ValueListenableBuilder<bool>(
                                                      valueListenable: isUnderlineSelected,
                                                      builder: (context, underlineSelected, _) {
                                                        return ValueListenableBuilder<bool>(
                                                          valueListenable: isItalicSelected,
                                                          builder: (context, italicSelected, _) {
                                                            return ValueListenableBuilder<bool>(
                                                              valueListenable: isBoldSelected,
                                                              builder: (context, boldSelected, _) {
                                                                return ValueListenableBuilder<bool>(
                                                                  valueListenable: isNumberListSelected,
                                                                  builder: (context, numberPointSelected, _) {
                                                                    return ValueListenableBuilder<bool>(
                                                                      valueListenable: isBulletListSelected,
                                                                      builder: (context, bulletPointSelected, _) {
                                                                        return QuillSimpleToolbar(
                                                                          controller: descriptionController,
                                                                          config: QuillSimpleToolbarConfig(
                                                                            toolbarIconAlignment: WrapAlignment.start,
                                                                            toolbarIconCrossAlignment:
                                                                                WrapCrossAlignment.start,
                                                                            toolbarSectionSpacing: 0,
                                                                            showIndent: false,
                                                                            showLink: false,
                                                                            showStrikeThrough: false,
                                                                            showSubscript: false,
                                                                            showSuperscript: false,
                                                                            showFontSize: false,
                                                                            showFontFamily: false,
                                                                            showColorButton: false,
                                                                            showRedo: false,
                                                                            showUndo: false,
                                                                            showClearFormat: false,
                                                                            showCenterAlignment: false,
                                                                            showCodeBlock: false,
                                                                            showHeaderStyle: false,
                                                                            showSearchButton: false,
                                                                            showJustifyAlignment: false,
                                                                            showDividers: false,
                                                                            showInlineCode: false,
                                                                            showLeftAlignment: false,
                                                                            showListCheck: false,
                                                                            showRightAlignment: false,
                                                                            showBoldButton: false,
                                                                            showItalicButton: false,
                                                                            showUnderLineButton: false,
                                                                            showListBullets: false,
                                                                            showListNumbers: false,
                                                                            showQuote: false,
                                                                            // spacerWidget: const SizedBox.shrink(),
                                                                            showBackgroundColorButton: false,
                                                                            color: AppColors.transparent,
                                                                            customButtons: [
                                                                              QuillToolbarCustomButtonOptions(
                                                                                iconTheme: const QuillIconTheme(
                                                                                  iconButtonSelectedData:
                                                                                      IconButtonData(iconSize: 35),
                                                                                  iconButtonUnselectedData:
                                                                                      IconButtonData(iconSize: 35),
                                                                                ),
                                                                                icon: Container(
                                                                                  padding: const EdgeInsets.all(5),
                                                                                  decoration: BoxDecoration(
                                                                                    color: bulletPointSelected
                                                                                        ? Theme.of(context)
                                                                                                    .brightness ==
                                                                                                Brightness.dark
                                                                                            ? AppColors.headingTextDark
                                                                                            : Theme.of(context)
                                                                                                .primaryColor
                                                                                        : Colors.transparent,
                                                                                    borderRadius:
                                                                                        BorderRadius.circular(4),
                                                                                  ),
                                                                                  child: AppImageAsset(
                                                                                    AppAssets.bulletPointIcon,
                                                                                    color: bulletPointSelected
                                                                                        ? Theme.of(context)
                                                                                                    .brightness ==
                                                                                                Brightness.dark
                                                                                            ? AppColors.primary
                                                                                            : Colors.white
                                                                                        : Theme.of(context)
                                                                                                    .brightness ==
                                                                                                Brightness.dark
                                                                                            ? AppColors.headingTextDark
                                                                                            : null,
                                                                                  ),
                                                                                ),
                                                                                onPressed: () {
                                                                                  if (bulletPointSelected) {
                                                                                    descriptionController
                                                                                        .formatSelection(
                                                                                      Attribute.fromKeyValue(
                                                                                          Attribute.ul.key, null),
                                                                                    );

                                                                                    return;
                                                                                  }
                                                                                  descriptionController
                                                                                      .formatSelection(Attribute.ul);
                                                                                },
                                                                              ),
                                                                              QuillToolbarCustomButtonOptions(
                                                                                iconTheme: const QuillIconTheme(
                                                                                  iconButtonSelectedData:
                                                                                      IconButtonData(iconSize: 35),
                                                                                  iconButtonUnselectedData:
                                                                                      IconButtonData(iconSize: 35),
                                                                                ),
                                                                                icon: Container(
                                                                                  padding: const EdgeInsets.all(5),
                                                                                  decoration: BoxDecoration(
                                                                                    color: numberPointSelected
                                                                                        ? Theme.of(context)
                                                                                                    .brightness ==
                                                                                                Brightness.dark
                                                                                            ? AppColors.headingTextDark
                                                                                            : Theme.of(context)
                                                                                                .primaryColor
                                                                                        : Colors.transparent,
                                                                                    borderRadius:
                                                                                        BorderRadius.circular(4),
                                                                                  ),
                                                                                  child: AppImageAsset(
                                                                                    AppAssets.numberPointIcon,
                                                                                    color: numberPointSelected
                                                                                        ? Theme.of(context)
                                                                                                    .brightness ==
                                                                                                Brightness.dark
                                                                                            ? AppColors.primary
                                                                                            : Colors.white
                                                                                        : Theme.of(context)
                                                                                                    .brightness ==
                                                                                                Brightness.dark
                                                                                            ? AppColors.headingTextDark
                                                                                            : null,
                                                                                  ),
                                                                                ),
                                                                                onPressed: () {
                                                                                  if (numberPointSelected) {
                                                                                    descriptionController
                                                                                        .formatSelection(
                                                                                      Attribute.fromKeyValue(
                                                                                          Attribute.ol.key, null),
                                                                                    );

                                                                                    return;
                                                                                  }
                                                                                  descriptionController
                                                                                      .formatSelection(Attribute.ol);
                                                                                },
                                                                              ),
                                                                              QuillToolbarCustomButtonOptions(
                                                                                iconTheme: const QuillIconTheme(
                                                                                  iconButtonSelectedData:
                                                                                      IconButtonData(iconSize: 35),
                                                                                  iconButtonUnselectedData:
                                                                                      IconButtonData(iconSize: 35),
                                                                                ),
                                                                                icon: Container(
                                                                                  padding: const EdgeInsets.all(5),
                                                                                  decoration: BoxDecoration(
                                                                                    color: boldSelected
                                                                                        ? Theme.of(context)
                                                                                                    .brightness ==
                                                                                                Brightness.dark
                                                                                            ? AppColors.headingTextDark
                                                                                            : Theme.of(context)
                                                                                                .primaryColor
                                                                                        : Colors.transparent,
                                                                                    borderRadius:
                                                                                        BorderRadius.circular(4),
                                                                                  ),
                                                                                  child: AppImageAsset(
                                                                                    AppAssets.boldIcon,
                                                                                    color: boldSelected
                                                                                        ? Theme.of(context)
                                                                                                    .brightness ==
                                                                                                Brightness.dark
                                                                                            ? AppColors.primary
                                                                                            : Colors.white
                                                                                        : Theme.of(context)
                                                                                                    .brightness ==
                                                                                                Brightness.dark
                                                                                            ? AppColors.headingTextDark
                                                                                            : null,
                                                                                  ),
                                                                                ),
                                                                                onPressed: () {
                                                                                  if (boldSelected) {
                                                                                    descriptionController
                                                                                        .formatSelection(
                                                                                      Attribute.fromKeyValue(
                                                                                          Attribute.bold.key, null),
                                                                                    );

                                                                                    return;
                                                                                  }
                                                                                  descriptionController
                                                                                      .formatSelection(Attribute.bold);
                                                                                },
                                                                              ),
                                                                              QuillToolbarCustomButtonOptions(
                                                                                iconTheme: const QuillIconTheme(
                                                                                  iconButtonSelectedData:
                                                                                      IconButtonData(iconSize: 35),
                                                                                  iconButtonUnselectedData:
                                                                                      IconButtonData(iconSize: 35),
                                                                                ),
                                                                                icon: Container(
                                                                                  padding: const EdgeInsets.all(5),
                                                                                  decoration: BoxDecoration(
                                                                                    color: italicSelected
                                                                                        ? Theme.of(context)
                                                                                                    .brightness ==
                                                                                                Brightness.dark
                                                                                            ? AppColors.headingTextDark
                                                                                            : Theme.of(context)
                                                                                                .primaryColor
                                                                                        : Colors.transparent,
                                                                                    borderRadius:
                                                                                        BorderRadius.circular(4),
                                                                                  ),
                                                                                  child: AppImageAsset(
                                                                                    AppAssets.italicIcon,
                                                                                    color: italicSelected
                                                                                        ? Theme.of(context)
                                                                                                    .brightness ==
                                                                                                Brightness.dark
                                                                                            ? AppColors.primary
                                                                                            : Colors.white
                                                                                        : Theme.of(context)
                                                                                                    .brightness ==
                                                                                                Brightness.dark
                                                                                            ? AppColors.headingTextDark
                                                                                            : null,
                                                                                  ),
                                                                                ),
                                                                                onPressed: () {
                                                                                  if (italicSelected) {
                                                                                    descriptionController
                                                                                        .formatSelection(
                                                                                      Attribute.fromKeyValue(
                                                                                          Attribute.italic.key, null),
                                                                                    );

                                                                                    return;
                                                                                  }
                                                                                  descriptionController.formatSelection(
                                                                                      Attribute.italic);
                                                                                },
                                                                              ),
                                                                              QuillToolbarCustomButtonOptions(
                                                                                iconTheme: const QuillIconTheme(
                                                                                  iconButtonSelectedData:
                                                                                      IconButtonData(iconSize: 35),
                                                                                  iconButtonUnselectedData:
                                                                                      IconButtonData(iconSize: 35),
                                                                                ),
                                                                                icon: Container(
                                                                                  padding: const EdgeInsets.all(5),
                                                                                  decoration: BoxDecoration(
                                                                                    color: underlineSelected
                                                                                        ? Theme.of(context)
                                                                                                    .brightness ==
                                                                                                Brightness.dark
                                                                                            ? AppColors.headingTextDark
                                                                                            : Theme.of(context)
                                                                                                .primaryColor
                                                                                        : Colors.transparent,
                                                                                    borderRadius:
                                                                                        BorderRadius.circular(4),
                                                                                  ),
                                                                                  child: AppImageAsset(
                                                                                    AppAssets.underlineIcon,
                                                                                    color: underlineSelected
                                                                                        ? Theme.of(context)
                                                                                                    .brightness ==
                                                                                                Brightness.dark
                                                                                            ? AppColors.primary
                                                                                            : Colors.white
                                                                                        : Theme.of(context)
                                                                                                    .brightness ==
                                                                                                Brightness.dark
                                                                                            ? AppColors.headingTextDark
                                                                                            : null,
                                                                                  ),
                                                                                ),
                                                                                onPressed: () {
                                                                                  if (underlineSelected) {
                                                                                    descriptionController
                                                                                        .formatSelection(
                                                                                      Attribute.fromKeyValue(
                                                                                          Attribute.underline.key,
                                                                                          null),
                                                                                    );

                                                                                    return;
                                                                                  }
                                                                                  descriptionController.formatSelection(
                                                                                      Attribute.underline);
                                                                                },
                                                                              ),
                                                                              QuillToolbarCustomButtonOptions(
                                                                                iconTheme: const QuillIconTheme(
                                                                                  iconButtonSelectedData:
                                                                                      IconButtonData(iconSize: 35),
                                                                                  iconButtonUnselectedData:
                                                                                      IconButtonData(iconSize: 35),
                                                                                ),
                                                                                icon: Container(
                                                                                  padding: const EdgeInsets.all(5),
                                                                                  decoration: BoxDecoration(
                                                                                    color: quoteSelected
                                                                                        ? Theme.of(context)
                                                                                                    .brightness ==
                                                                                                Brightness.dark
                                                                                            ? AppColors.headingTextDark
                                                                                            : Theme.of(context)
                                                                                                .primaryColor
                                                                                        : Colors.transparent,
                                                                                    borderRadius:
                                                                                        BorderRadius.circular(4),
                                                                                  ),
                                                                                  child: AppImageAsset(
                                                                                    AppAssets.quoteIcon,
                                                                                    color: quoteSelected
                                                                                        ? Theme.of(context)
                                                                                                    .brightness ==
                                                                                                Brightness.dark
                                                                                            ? AppColors.primary
                                                                                            : Colors.white
                                                                                        : Theme.of(context)
                                                                                                    .brightness ==
                                                                                                Brightness.dark
                                                                                            ? AppColors.headingTextDark
                                                                                            : null,
                                                                                  ),
                                                                                ),
                                                                                onPressed: () {
                                                                                  if (quoteSelected) {
                                                                                    descriptionController
                                                                                        .formatSelection(
                                                                                      Attribute.fromKeyValue(
                                                                                          Attribute.blockQuote.key,
                                                                                          null),
                                                                                    );

                                                                                    return;
                                                                                  }
                                                                                  descriptionController.formatSelection(
                                                                                      Attribute.blockQuote);
                                                                                },
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        );
                                                                      },
                                                                    );
                                                                  },
                                                                );
                                                              },
                                                            );
                                                          },
                                                        );
                                                      },
                                                    );
                                                  },
                                                ),
                                                const SizedBox(height: 6),
                                              ],
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ),
                            ),
                            const Gap(20),
                            Align(
                              alignment: context.isMobile ? Alignment.center : Alignment.centerRight,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  PrimaryButton(
                                    width: 164,
                                    onPressed: () async {
                                      if (isPostCreated) {
                                        if (isForEdit) {
                                          DailogBoxes.discardPostChangesDialog(
                                            context,
                                            onDiscardTap: () {
                                              if (context.canPop()) {
                                                context.pop();
                                              } else {
                                                context.pushNamed(AppRoutes.profile.name);
                                              }
                                            },
                                            onSaveTap: () async {
                                              await context.read<CreateEditPostCubit>().savePost();
                                              if (context.canPop()) {
                                                context.pop();
                                              } else {
                                                context.pushNamed(AppRoutes.profile.name);
                                              }
                                            },
                                          );
                                        } else {
                                          if (titleController.text.trim().isEmpty &&
                                              descriptionController.document.toPlainText().trim().isEmpty) {
                                            await context.read<CreateEditPostCubit>().deletePost();
                                            if (context.canPop()) {
                                              context.pop();
                                            } else {
                                              context.pushNamed(AppRoutes.profile.name);
                                            }
                                            return;
                                          }
                                          DailogBoxes.deletePostDialog(
                                            context,
                                            onDeletePostTap: () async {
                                              await context.read<CreateEditPostCubit>().deletePost();
                                              if (context.canPop()) {
                                                context.pop();
                                              } else {
                                                context.pushNamed(AppRoutes.profile.name);
                                              }
                                            },
                                            onSaveDraft: () async {
                                              await context.read<CreateEditPostCubit>().autoSavePost(showLoading: true);
                                              if (context.canPop()) {
                                                context.pop();
                                              } else {
                                                context.pushNamed(AppRoutes.profile.name);
                                              }
                                            },
                                          );
                                        }
                                      } else {
                                        context.pop();
                                      }
                                    },
                                    text: context.l10n.cancel,
                                    backgroundColor: AppColors.white,
                                    textColor: AppColors.primary,
                                    borderColor: AppColors.primary,
                                  ),
                                  const Gap(15),
                                  PrimaryButton(
                                    width: 164,
                                    onPressed: () {
                                      final state = context.read<CreateEditPostCubit>().state;
                                      if (state.isStackLoading || state.isInitialLoading) return;
                                      descriptionError.value = null;

                                      if (descriptionController.document.toPlainText().trim().isEmpty) {
                                        descriptionError.value = context.l10n.emptyDescriptionMsg;
                                      }
                                      if (_validateKey.currentState!.validate() && descriptionError.value == null) {
                                        context.read<CreateEditPostCubit>().onPreviewTap();
                                      }
                                    },
                                    text: context.l10n.previewPost,
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                BlocBuilder<CreateEditPostCubit, CreateEditPostState>(
                  buildWhen: (previous, current) =>
                      previous.isStackLoading != current.isStackLoading ||
                      previous.isInitialLoading != current.isInitialLoading,
                  builder: (context, state) {
                    if (state.isStackLoading || state.isInitialLoading) return const CustomProgressIndicator();
                    return const SizedBox();
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}


// bottom view


  