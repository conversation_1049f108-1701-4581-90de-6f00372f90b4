import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tynt_web/auth/bloc/authentication_bloc.dart';
import 'package:tynt_web/create_post/cubit/create_edit_post_cubit.dart';
import 'package:tynt_web/create_post/view/create_post_page.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/post_preview/view/post_preview_page.dart';

class CreateEditPostWrapperPage extends StatelessWidget {
  const CreateEditPostWrapperPage({
    super.key,
    this.id,
    this.isForTyntY = false,
  });
  final int? id;
  final bool isForTyntY;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<CreateEditPostCubit>(
        param1: CreateEditPostParams(
          user: context.read<AuthenticationBloc>().state.currentUser,
          postId: id,
          isForTyntY: isForTyntY,
        ),
      )..load(),
      child: Builder(
        builder: (context) {
          return MultiBlocListener(
            listeners: [
              BlocListener<CreateEditPostCubit, CreateEditPostState>(
                listenWhen: (previous, current) =>
                    previous.isActionLoading != current.isActionLoading && !current.isActionLoading,
                listener: (context, state) {},
              ),
              BlocListener<CreateEditPostCubit, CreateEditPostState>(
                listenWhen: (previous, current) =>
                    previous.editedPost != current.editedPost && current.editedPost != null,
                listener: (context, state) {
                  // context.read<RefreshCubit>().refreshPostDetail(state.editedPost!);
                },
              ),
            ],
            child: BlocSelector<CreateEditPostCubit, CreateEditPostState, bool>(
              selector: (state) => state.showPreviewPage,
              builder: (context, value) {
                if (value) {
                  return PostPreviewPage(id: id);
                }
                return CreatePostPage(
                  id: id,
                );
              },
            ),
          );
        },
      ),
    );
  }
}
