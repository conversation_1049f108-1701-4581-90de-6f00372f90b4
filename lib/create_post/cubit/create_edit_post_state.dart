part of 'create_edit_post_cubit.dart';

class CreateEditPostState {
  const CreateEditPostState({
    required this.user,
    this.createPostId,
    this.isCreatePostLoading = false,
    this.isCreatePostEditLoading = false,
    this.postImage,
    this.showComments = true,
    this.mentionsUsersList = const [],
    this.mentionsMoviesList = const [],
    this.isStackLoading = false,
    this.postTomesList = const [],
    this.postModel,
    this.editedPost,
    this.isInitialLoading = false,
    this.isActionLoading = false,
    this.postTypeList = const [],
    this.selectedGenre,
    this.selectedPosType,
    this.title,
    this.description,
    this.postId,
    this.showPreviewPage = false,
    this.selctedHasTagList = const [],
    this.newHasTagsList = const [],
    this.selectedHasTag,
    this.successText,
    this.errorText,
    this.deleteImage,
    this.selectedCommunity,
    this.isForTyntY = false,
    this.showCommunity = false,
    this.addPost,
  });
  final UserModel user;
  final int? createPostId;
  final bool isCreatePostLoading;
  final bool isCreatePostEditLoading;
  final AppFileData? postImage;
  final bool showComments;
  final List<UserModel> mentionsUsersList;
  final List<CommonNameModel> mentionsMoviesList;
  final bool isStackLoading;
  final List<TomesModel> postTomesList;
  final PostModel? postModel;

  // OLD Variables
  final PostModel? editedPost;
  final bool isInitialLoading;
  final bool isActionLoading;
  final List<PostTypeModel> postTypeList;

  // Save data
  final GenresModel? selectedGenre;
  final PostTypeModel? selectedPosType;
  final String? title;
  final String? description;
  final int? postId;
  final bool showPreviewPage;
  final List<CommonNameModel> selctedHasTagList;
  final List<String> newHasTagsList;
  final String? selectedHasTag;
  final String? successText;
  final String? errorText;
  final String? deleteImage;
  final CommunityModel? selectedCommunity;
  final bool isForTyntY;
  final bool showCommunity;
  final PostModel? addPost;

  CreateEditPostState copyWith({
    UserModel? user,
    int? createPostId,
    bool? isCreatePostLoading,
    bool? isCreatePostEditLoading,
    AppFileData? postImage,
    bool? showComments,
    List<UserModel>? mentionsUsersList,
    List<CommonNameModel>? mentionsMoviesList,
    bool? isStackLoading,
    List<TomesModel>? postTomesList,
    PostModel? postModel,
    PostModel? editedPost,
    bool? isInitialLoading,
    bool? isActionLoading,
    List<PostTypeModel>? postTypeList,
    GenresModel? selectedGenre,
    PostTypeModel? selectedPosType,
    String? title,
    String? description,
    int? postId,
    bool? showPreviewPage,
    List<CommonNameModel>? selctedHasTagList,
    List<String>? newHasTagsList,
    String? selectedHasTag,
    String? successText,
    String? errorText,
    String? deleteImage,
    CommunityModel? selectedCommunity,
    bool? isForTyntY,
    bool? showCommunity,
    PostModel? addPost,
  }) {
    return CreateEditPostState(
      user: user ?? this.user,
      createPostId: createPostId ?? this.createPostId,
      isCreatePostLoading: isCreatePostLoading ?? this.isCreatePostLoading,
      isCreatePostEditLoading: isCreatePostEditLoading ?? this.isCreatePostEditLoading,
      postImage: postImage ?? this.postImage,
      showComments: showComments ?? this.showComments,
      mentionsUsersList: mentionsUsersList ?? this.mentionsUsersList,
      mentionsMoviesList: mentionsMoviesList ?? this.mentionsMoviesList,
      isStackLoading: isStackLoading ?? this.isStackLoading,
      postTomesList: postTomesList ?? this.postTomesList,
      postModel: postModel ?? this.postModel,
      editedPost: editedPost ?? this.editedPost,
      isInitialLoading: isInitialLoading ?? this.isInitialLoading,
      isActionLoading: isActionLoading ?? this.isActionLoading,
      postTypeList: postTypeList ?? this.postTypeList,
      selectedGenre: selectedGenre ?? this.selectedGenre,
      selectedPosType: selectedPosType ?? this.selectedPosType,
      title: title ?? this.title,
      description: description ?? this.description,
      postId: postId ?? this.postId,
      showPreviewPage: showPreviewPage ?? this.showPreviewPage,
      selctedHasTagList: selctedHasTagList ?? this.selctedHasTagList,
      newHasTagsList: newHasTagsList ?? this.newHasTagsList,
      selectedHasTag: selectedHasTag ?? this.selectedHasTag,
      successText: successText ?? this.successText,
      errorText: errorText ?? this.errorText,
      deleteImage: deleteImage ?? this.deleteImage,
      selectedCommunity: selectedCommunity ?? this.selectedCommunity,
      isForTyntY: isForTyntY ?? this.isForTyntY,
      showCommunity: showCommunity ?? this.showCommunity,
      addPost: addPost ?? this.addPost,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CreateEditPostState &&
          runtimeType == other.runtimeType &&
          user == other.user &&
          createPostId == other.createPostId &&
          isCreatePostLoading == other.isCreatePostLoading &&
          isCreatePostEditLoading == other.isCreatePostEditLoading &&
          postImage == other.postImage &&
          showComments == other.showComments &&
          listEquals(mentionsUsersList, other.mentionsUsersList) &&
          listEquals(mentionsMoviesList, other.mentionsMoviesList) &&
          isStackLoading == other.isStackLoading &&
          listEquals(postTomesList, other.postTomesList) &&
          postModel == other.postModel &&
          editedPost == other.editedPost &&
          isInitialLoading == other.isInitialLoading &&
          isActionLoading == other.isActionLoading &&
          listEquals(postTypeList, other.postTypeList) &&
          selectedGenre == other.selectedGenre &&
          selectedPosType == other.selectedPosType &&
          title == other.title &&
          description == other.description &&
          postId == other.postId &&
          showPreviewPage == other.showPreviewPage &&
          listEquals(selctedHasTagList, other.selctedHasTagList) &&
          listEquals(newHasTagsList, other.newHasTagsList) &&
          selectedHasTag == other.selectedHasTag &&
          successText == other.successText &&
          errorText == other.errorText &&
          deleteImage == other.deleteImage &&
          selectedCommunity == other.selectedCommunity &&
          isForTyntY == other.isForTyntY &&
          showCommunity == other.showCommunity &&
          addPost == other.addPost;

  @override
  int get hashCode => Object.hashAll([
        user,
        createPostId,
        isCreatePostLoading,
        isCreatePostEditLoading,
        postImage,
        showComments,
        mentionsUsersList,
        mentionsMoviesList,
        isStackLoading,
        postTomesList,
        postModel,
        editedPost,
        isInitialLoading,
        isActionLoading,
        postTypeList,
        selectedGenre,
        selectedPosType,
        title,
        description,
        postId,
        showPreviewPage,
        selctedHasTagList,
        newHasTagsList,
        selectedHasTag,
        successText,
        errorText,
        deleteImage,
        selectedCommunity,
        isForTyntY,
        showCommunity,
        addPost,
      ]);
}
