// ignore_for_file: avoid_redundant_argument_values

import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:tynt_web/auth/models/app_file_data.dart';
import 'package:tynt_web/auth/models/user_model.dart';
import 'package:tynt_web/extentions/fpdart_extentions.dart';
import 'package:tynt_web/post_preview/model/comman_name_model.dart';
import 'package:tynt_web/post_preview/model/community_model.dart';
import 'package:tynt_web/post_preview/model/post_genres_model.dart';
import 'package:tynt_web/post_preview/model/post_model.dart';
import 'package:tynt_web/post_preview/model/post_type_model.dart';
import 'package:tynt_web/post_preview/repository/i_post_repository.dart';
import 'package:tynt_web/tomes/model/tomes_model.dart';
import 'package:tynt_web/utlity/logger_config.dart';
import 'package:tynt_web/utlity/utlity.dart';

part 'create_edit_post_state.dart';

@injectable
class CreateEditPostCubit extends Cubit<CreateEditPostState> {
  CreateEditPostCubit({
    required this.postRepository,
    @factoryParam required CreateEditPostParams params,
  }) : super(CreateEditPostState(user: params.user, postId: params.postId, isForTyntY: params.isForTyntY));

  final IPostRepository postRepository;

  Future<void> load() async {
    emit(state.copyWith(isInitialLoading: true));

    final failOrGetApiResults = await Future.wait([
      if (state.postId != null) postRepository.getPostDetail(state.postId!),
    ]);

    final failOrGetPost = state.postId == null ? null : failOrGetApiResults[0];

    final postDetail = failOrGetPost?.getRightFolded()?.post;

    emit(
      state.copyWith(
        isInitialLoading: false,
        // postTypeList: postTypeList,
        postModel: postDetail,
        // selectedPosType: postDetail?.postType ?? postTypeList.firstOrNull,
        description: postDetail?.descriptionDelta,
        title: postDetail?.title,
        isForTyntY: postDetail?.isTyntYPost ?? state.isForTyntY,
        postImage:
            postDetail == null || postDetail.imageUrl == null ? null : AppFileData(networkUrl: postDetail.imageUrl),
        selectedHasTag: postDetail?.tags.map((e) => '#${e.name}').join(' '),
        selectedGenre: postDetail?.genre,
        showComments: postDetail == null || (postDetail.isCommentOn == 1),
        selectedCommunity: postDetail?.community,
        // showCommunity: showCommunity,
        selctedHasTagList: postDetail?.tags ?? <CommonNameModel>[],
        postTomesList: postDetail?.tomes ?? <TomesModel>[],
      ),
    );
  }

  void changeStoryType(PostTypeModel? selectedPostType) {
    emit(state.copyWith(selectedPosType: selectedPostType));
  }

  void changeSelectGenres(GenresModel genres) {
    emit(state.copyWith(selectedGenre: genres));
  }

  void changeHashTagList({CommonNameModel? tagModel}) {
    if (tagModel == null) {
      return;
    }
    final selectedTagsList = [...state.selctedHasTagList];
    final isExistsTag = selectedTagsList.firstWhereOrNull((element) => element.id == tagModel.id) != null;

    if (isExistsTag) {
      selectedTagsList.remove(tagModel);
    } else {
      selectedTagsList.add(tagModel);
    }

    emit(state.copyWith(selctedHasTagList: [...selectedTagsList]));
  }

  void changeHashTag(String? value) {
    emit(state.copyWith(selectedHasTag: value));
  }

  void changeShowComments() {
    emit(state.copyWith(showComments: !state.showComments));
  }

  void changeDescription(String value) {
    emit(state.copyWith(description: value));
  }

  void changeTitle(String value) {
    emit(state.copyWith(title: value));
  }

  void onPreviewTap() {
    final allMentionHasgTag = Utility.getAllHashTag(state.selectedHasTag ?? '');
    final newHashTag = <String>[];
    final appHashTags = [...state.selctedHasTagList];

    for (final hashTag in allMentionHasgTag) {
      final tag = appHashTags.firstWhereOrNull((element) => element.name == hashTag);
      if (tag != null) {
        appHashTags.add(tag);
      } else {
        newHashTag.add(hashTag);
      }
    }

    emit(
      state.copyWith(
        showPreviewPage: true,
        selctedHasTagList: [...appHashTags],
        newHasTagsList: [...newHashTag],
      ),
    );
  }

  void changeClosePreview() {
    if (state.isActionLoading) return;
    emit(state.copyWith(showPreviewPage: false));
  }

  Future<void> createPost({bool isForDraft = true}) async {
    if (state.isActionLoading) return;
    emit(state.copyWith(isCreatePostLoading: true));

    final newMentionsUsers = <UserModel>[];

    final newMentionsMovies = <CommonNameModel>[];

    for (final user in state.mentionsUsersList) {
      if (state.description?.contains(user.userName ?? '') ?? false) {
        newMentionsUsers.add(user);
      }
    }

    for (final movie in state.mentionsMoviesList) {
      if (state.description?.contains(movie.name ?? '') ?? false) {
        newMentionsMovies.add(movie);
      }
    }

    final failOrSuccess = await postRepository.createPost(
      title: state.title,
      description: state.description ?? '',
      postTypeId: state.selectedPosType?.id,
      genresId: state.selectedGenre?.id,
      newTags: state.newHasTagsList.toList(),
      tagsIds: state.selctedHasTagList.map((e) => e.id).toList(),
      isPseudo: state.user.isPseudoUser,
      image: state.postImage?.imageBytes,
      isDraft: isForDraft,
      showComments: state.showComments,
      isTyntYPost: state.isForTyntY,
      communityId: state.selectedCommunity?.id,
      mentionMovieIds: newMentionsMovies.map((e) => e.id).toList(),
      mentionUserIds: newMentionsUsers.map((e) => e.id).toList(),
    );

    emit(
      failOrSuccess.fold(
        (l) {
          debugLog(l);
          return state.copyWith(isCreatePostLoading: false, errorText: l.message);
        },
        (r) => state.copyWith(
          isCreatePostLoading: false,
          successText: r.message,
          createPostId: r.post?.id,
        ),
      ),
    );
  }

  Future<void> autoSavePost({
    // required String title,
    // required String description,
    Uint8List? image,
    bool showComments = true,
    bool deleteImage = false,
    bool showLoading = false,
  }) async {
    if (state.isActionLoading || state.isCreatePostLoading) return;

    emit(state.copyWith(isCreatePostLoading: true, isStackLoading: showLoading));

    final newMentionsUsers = <UserModel>[];

    final newMentionsMovies = <CommonNameModel>[];

    for (final user in state.mentionsUsersList) {
      if (state.description?.contains(user.userName ?? '') ?? false) {
        newMentionsUsers.add(user);
      }
    }

    for (final movie in state.mentionsMoviesList) {
      if (state.description?.contains(movie.name ?? '') ?? false) {
        newMentionsMovies.add(movie);
      }
    }

    final failOrSuccess = state.createPostId != null
        ? await postRepository.editPost(
            postId: state.createPostId!,
            title: state.title,
            description: state.description,
            image: image,
            isDraft: true,
            showComments: showComments,
            mentionMovieIds: newMentionsMovies.map((e) => e.id).toList(),
            mentionUserIds: newMentionsUsers.map((e) => e.id).toList(),
            deleteImage: deleteImage ? '1' : null,
            tomsIds: state.postTomesList.map((e) => e.id!).toList(),
          )
        : await postRepository.createPost(
            title: state.title,
            description: state.description ?? '',
            isPseudo: state.user.isPseudoUser,
            image: image,
            isDraft: true,
            showComments: showComments,
            isTyntYPost: state.isForTyntY,
            mentionMovieIds: newMentionsMovies.map((e) => e.id).toList(),
            mentionUserIds: newMentionsUsers.map((e) => e.id).toList(),
          );

    emit(
      failOrSuccess.fold(
        (l) {
          debugLog(l);
          return state.copyWith(isCreatePostLoading: false, errorText: l.message);
        },
        (r) => state.copyWith(
          isCreatePostLoading: false,
          isStackLoading: false,
          successText: r.message,
          createPostId: r.post?.id,
          description: state.description,
          title: state.title,
          addPost: r.post,
          // addPost: isForDraft
          //     ? null
          //     : r.post == null
          //         ? null
          //         : r.post?.copyWith(postType: state.selectedPosType),
        ),
      ),
    );
  }

  Future<PostModel?> savePost() async {
    emit(state.copyWith(isStackLoading: true));
    final failOrSuccess = await postRepository.editPost(
      postId: state.postId!,
      title: state.title,
      description: state.description,
      image: state.postImage?.imageBytes,
      deleteImage: state.deleteImage,
      tomsIds: state.postTomesList.map((e) => e.id!).toList(),
    );

    emit(
      failOrSuccess.fold(
        (l) {
          return state.copyWith(isStackLoading: false, errorText: l.message);
        },
        (r) => state.copyWith(
          isStackLoading: false,
          successText: r.message,
        ),
      ),
    );

    return failOrSuccess.fold(
      (l) => null,
      (r) => r.post,
    );
  }

  Future<bool> publishPost() async {
    if (state.createPostId == null) return false;
    final failOrSuccess = await postRepository.editPost(
      postId: state.createPostId!,
      tomsIds: state.postTomesList.map((e) => e.id!).toList(),
    );
    return failOrSuccess.fold(
      (l) {
        Utility.toast(message: l.message);
        return false;
      },
      (r) {
        return true;
      },
    );
  }

  void saveDataToState({
    String? title,
    String? description,
  }) {
    if (state.isStackLoading) return;
    emit(
      state.copyWith(
        title: title,
        description: description,
      ),
    );
  }

  Future<void> deletePost() async {
    if (state.isStackLoading || state.createPostId == null) return;

    emit(state.copyWith(isStackLoading: true));

    final failOrSuccess = await postRepository.deletePost(state.createPostId!);

    emit(
      failOrSuccess.fold(
        (l) {
          log(l.toString());
          return state.copyWith(isStackLoading: false, errorText: l.message);
        },
        (r) => state.copyWith(
          isStackLoading: false,
          successText: r.message,
        ),
      ),
    );
  }

  Future<void> editPost({bool isForDraft = false}) async {
    if (state.isActionLoading) return;
    if (state.createPostId == null) return;
    emit(state.copyWith(isActionLoading: true));

    final newMentionsUsers = <UserModel>[];

    final newMentionsMovies = <CommonNameModel>[];

    for (final user in state.mentionsUsersList) {
      if (state.description?.contains(user.userName ?? '') ?? false) {
        newMentionsUsers.add(user);
      }
    }

    for (final movie in state.mentionsMoviesList) {
      if (state.description?.contains(movie.name ?? '') ?? false) {
        newMentionsMovies.add(movie);
      }
    }

    final failOrSuccess = await postRepository.editPost(
      postId: state.postId!,
      title: state.title ?? '',
      description: state.description ?? '',
      postTypeId: state.selectedPosType?.id ?? 1,
      genresId: state.selectedGenre?.id,
      newTags: state.newHasTagsList.toList(),
      deleteImage: state.deleteImage,
      tagsIds: state.selctedHasTagList.map((e) => e.id).toList(),
      isDraft: isForDraft,
      image: state.postImage?.imageBytes,
      communityId: state.selectedCommunity?.id,
      mentionMovieIds: newMentionsMovies.map((e) => e.id).toList(),
      mentionUserIds: newMentionsUsers.map((e) => e.id).toList(),
      tomsIds: state.postTomesList.map((e) => e.id!).toList(),
    );

    emit(
      failOrSuccess.fold(
        (l) {
          debugLog(l);
          return state.copyWith(isActionLoading: false, errorText: l.message);
        },
        (r) => state.copyWith(isActionLoading: false, successText: r.message, editedPost: r.post),
      ),
    );
  }

  void setImage(Uint8List fileBytes) {
    emit(state.copyWith(postImage: AppFileData(imageBytes: fileBytes)));
  }

  void clearPickedImage() {
    final appFileData = state.postImage;
    emit(state.copyWith(postImage: const AppFileData(), deleteImage: appFileData?.networkUrl));
  }

  void changeCommunity(CommunityModel? community) {
    emit(state.copyWith(selectedCommunity: community));
  }

  Future<void> saveAsDraftPost() async {
    if (state.postId == null) {
      await createPost();
      return;
    }

    await editPost(isForDraft: true);
  }

  void addMentionUser(UserModel user) {
    if (state.mentionsUsersList.any((element) => element.id == user.id)) return;
    emit(state.copyWith(mentionsUsersList: [...state.mentionsUsersList, user]));
  }

  void addMovies(CommonNameModel movie) {
    if (state.mentionsMoviesList.any((element) => element.id == movie.id)) return;
    emit(state.copyWith(mentionsMoviesList: [...state.mentionsMoviesList, movie]));
  }
}

class CreateEditPostParams {
  CreateEditPostParams({
    required this.user,
    this.isForTyntY = false,
    this.postId,
  });
  final UserModel user;
  final int? postId;
  final bool isForTyntY;
}
