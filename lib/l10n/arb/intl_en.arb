{"@@locale": "en", "splashText1": "One Platform, Two Identities", "email": "Email", "emptyEmailMsg": "Please enter your email", "errorEmailMsg": "The email address is not valid", "password": "Password", "emptyPasswordMsg": "Please enter your password", "errorPasswordMsg": "Password must be at least 6 characters", "tryAgain": "Try Again", "createTome": "Create <PERSON><PERSON>", "editTome": "<PERSON>", "youCanAddPostsAndMakeGroups": "You can add posts and make groups", "details": "Details", "emptyTomDetailsMsg": "Please enter tom details", "tomeName": "<PERSON><PERSON>", "upload": "Upload", "emptyTomNameMsg": "Please enter tom name", "selectFrom": "Select From", "avatar": "Avatar", "gallery": "Gallery", "cancel": "Cancel", "posts": "Posts", "following": "Following", "followers": "Followers", "tomes": "<PERSON><PERSON>", "draft": "Draft", "tapToReadMore": "Tap to read more", "post": "Post", "postFilter": "Post Filter", "viewAll": "View All", "myPosts": "My Posts", "tribe": "Tribe", "draftPosts": "Draft Posts", "share": "Share", "edit": "Edit", "delete": "Delete", "enterPasscode": "Enter passcode", "forgotPasscode": "Forgot passcode?", "showMore": "Show More", "postSetting": "Post Setting", "done": "Done", "addToTome": "Add to Tom<PERSON>", "nothingToseeHere": "Nothing to see here", "search": "Search", "noPostTypeFound": "No Post Type Found", "selectPostType": "Select Post Type", "postType": "Post Type", "postTypeSearch": "Post Type Search", "selectGenre": "Select Genre", "noGenreFound": "No Genre Found", "tagErrorMsg": "Invalid tag, please start with #", "addTags": "Add tags", "genre": "Genre", "typeOfPost": "Type of Post", "allowCommenting": "Allow Commenting", "emptyTitleMsg": "Please enter title", "title": "Title", "writeSomething": "Write something", "createPost": "Create Post", "editPost": "Edit Post", "areYouSureYouWantToDeleteTome": "Are you sure you want\nto delete tome?", "areYouSureYouWantToDiscardChanges": "Are you sure you want\nto discard changes", "discard": "Discard", "save": "Save", "areYouSureYouWantToDeletePost": "Are you sure you want\nto delete post?", "yesDelete": "Yes, Delete", "saveDraft": "Save as Draft", "previewPost": "Preview Post", "emptyDescriptionMsg": "Please enter description", "postPreview": "Post Preview", "publishPost": "Publish Post", "all": "All", "public": "Public", "createTyntProfile": "Create Tynt Profile", "thisDetailsWillMakeYouAnonymousOnThisPlatform": "This details will make you anonymous on this platform", "selectAvatar": "Select Avatar", "uploadPic": "Upload Pic", "tyntName": "Tynt Name", "generate": "Generate", "tyntNameInfoDescription1": "Your Tynt name serves as a pseudo profile, complementing your existing public profile. With your Tynt name, you can create new posts, comment, and trend posts, all under your pseudonym.", "tyntNameInfoDescription2": "Tynt name and public profile are separate profiles, offering users an alternative option for posting content without using their real name.", "tyntPasscode": "Tynt Passcode", "errorTyntPasscodeMsg": "Please enter 4 digit passcode", "tyntPasscodeInfoDescription": "The Tynt passcode is utilized to safeguard your identity from other users who may have access to your phone.", "emptyFirstNameMsg": "Please enter your first name", "emptyLastNameMsg": "Please enter your last name", "lastName": "Last Name", "firstName": "First Name", "name": "Name", "userName": "User Name", "male": "Male", "female": "Female", "other": "Other", "state": "State", "country": "Country", "select": "Select", "phoneNumber": "Phone Number", "userBio": "User Bio", "gender": "Gender", "verify": "Verify", "userNameEmptyError": "User name can't be empty", "userNameInvalidError": "Invalid user name", "selectCountry": "Select Country", "signOut": "Sign out", "profile": "Profile", "continueWithGoogle": "Continue with Google"}