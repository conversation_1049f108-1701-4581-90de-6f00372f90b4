// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class I10n {
  I10n();

  static I10n? _current;

  static I10n get current {
    assert(_current != null,
        'No instance of I10n was loaded. Try to initialize the I10n delegate before accessing I10n.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<I10n> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = I10n();
      I10n._current = instance;

      return instance;
    });
  }

  static I10n of(BuildContext context) {
    final instance = I10n.maybeOf(context);
    assert(instance != null,
        'No instance of I10n present in the widget tree. Did you add I10n.delegate in localizationsDelegates?');
    return instance!;
  }

  static I10n? maybeOf(BuildContext context) {
    return Localizations.of<I10n>(context, I10n);
  }

  /// `One Platform, Two Identities`
  String get splashText1 {
    return Intl.message(
      'One Platform, Two Identities',
      name: 'splashText1',
      desc: '',
      args: [],
    );
  }

  /// `Email`
  String get email {
    return Intl.message(
      'Email',
      name: 'email',
      desc: '',
      args: [],
    );
  }

  /// `Please enter your email`
  String get emptyEmailMsg {
    return Intl.message(
      'Please enter your email',
      name: 'emptyEmailMsg',
      desc: '',
      args: [],
    );
  }

  /// `The email address is not valid`
  String get errorEmailMsg {
    return Intl.message(
      'The email address is not valid',
      name: 'errorEmailMsg',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get password {
    return Intl.message(
      'Password',
      name: 'password',
      desc: '',
      args: [],
    );
  }

  /// `Please enter your password`
  String get emptyPasswordMsg {
    return Intl.message(
      'Please enter your password',
      name: 'emptyPasswordMsg',
      desc: '',
      args: [],
    );
  }

  /// `Password must be at least 6 characters`
  String get errorPasswordMsg {
    return Intl.message(
      'Password must be at least 6 characters',
      name: 'errorPasswordMsg',
      desc: '',
      args: [],
    );
  }

  /// `Try Again`
  String get tryAgain {
    return Intl.message(
      'Try Again',
      name: 'tryAgain',
      desc: '',
      args: [],
    );
  }

  /// `Create Tome`
  String get createTome {
    return Intl.message(
      'Create Tome',
      name: 'createTome',
      desc: '',
      args: [],
    );
  }

  /// `Edit Tome`
  String get editTome {
    return Intl.message(
      'Edit Tome',
      name: 'editTome',
      desc: '',
      args: [],
    );
  }

  /// `You can add posts and make groups`
  String get youCanAddPostsAndMakeGroups {
    return Intl.message(
      'You can add posts and make groups',
      name: 'youCanAddPostsAndMakeGroups',
      desc: '',
      args: [],
    );
  }

  /// `Details`
  String get details {
    return Intl.message(
      'Details',
      name: 'details',
      desc: '',
      args: [],
    );
  }

  /// `Please enter tom details`
  String get emptyTomDetailsMsg {
    return Intl.message(
      'Please enter tom details',
      name: 'emptyTomDetailsMsg',
      desc: '',
      args: [],
    );
  }

  /// `Tome Name`
  String get tomeName {
    return Intl.message(
      'Tome Name',
      name: 'tomeName',
      desc: '',
      args: [],
    );
  }

  /// `Upload`
  String get upload {
    return Intl.message(
      'Upload',
      name: 'upload',
      desc: '',
      args: [],
    );
  }

  /// `Please enter tom name`
  String get emptyTomNameMsg {
    return Intl.message(
      'Please enter tom name',
      name: 'emptyTomNameMsg',
      desc: '',
      args: [],
    );
  }

  /// `Select From`
  String get selectFrom {
    return Intl.message(
      'Select From',
      name: 'selectFrom',
      desc: '',
      args: [],
    );
  }

  /// `Avatar`
  String get avatar {
    return Intl.message(
      'Avatar',
      name: 'avatar',
      desc: '',
      args: [],
    );
  }

  /// `Gallery`
  String get gallery {
    return Intl.message(
      'Gallery',
      name: 'gallery',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get cancel {
    return Intl.message(
      'Cancel',
      name: 'cancel',
      desc: '',
      args: [],
    );
  }

  /// `Posts`
  String get posts {
    return Intl.message(
      'Posts',
      name: 'posts',
      desc: '',
      args: [],
    );
  }

  /// `Following`
  String get following {
    return Intl.message(
      'Following',
      name: 'following',
      desc: '',
      args: [],
    );
  }

  /// `Followers`
  String get followers {
    return Intl.message(
      'Followers',
      name: 'followers',
      desc: '',
      args: [],
    );
  }

  /// `Tomes`
  String get tomes {
    return Intl.message(
      'Tomes',
      name: 'tomes',
      desc: '',
      args: [],
    );
  }

  /// `Draft`
  String get draft {
    return Intl.message(
      'Draft',
      name: 'draft',
      desc: '',
      args: [],
    );
  }

  /// `Tap to read more`
  String get tapToReadMore {
    return Intl.message(
      'Tap to read more',
      name: 'tapToReadMore',
      desc: '',
      args: [],
    );
  }

  /// `Post`
  String get post {
    return Intl.message(
      'Post',
      name: 'post',
      desc: '',
      args: [],
    );
  }

  /// `Post Filter`
  String get postFilter {
    return Intl.message(
      'Post Filter',
      name: 'postFilter',
      desc: '',
      args: [],
    );
  }

  /// `View All`
  String get viewAll {
    return Intl.message(
      'View All',
      name: 'viewAll',
      desc: '',
      args: [],
    );
  }

  /// `My Posts`
  String get myPosts {
    return Intl.message(
      'My Posts',
      name: 'myPosts',
      desc: '',
      args: [],
    );
  }

  /// `Tribe`
  String get tribe {
    return Intl.message(
      'Tribe',
      name: 'tribe',
      desc: '',
      args: [],
    );
  }

  /// `Draft Posts`
  String get draftPosts {
    return Intl.message(
      'Draft Posts',
      name: 'draftPosts',
      desc: '',
      args: [],
    );
  }

  /// `Share`
  String get share {
    return Intl.message(
      'Share',
      name: 'share',
      desc: '',
      args: [],
    );
  }

  /// `Edit`
  String get edit {
    return Intl.message(
      'Edit',
      name: 'edit',
      desc: '',
      args: [],
    );
  }

  /// `Delete`
  String get delete {
    return Intl.message(
      'Delete',
      name: 'delete',
      desc: '',
      args: [],
    );
  }

  /// `Enter passcode`
  String get enterPasscode {
    return Intl.message(
      'Enter passcode',
      name: 'enterPasscode',
      desc: '',
      args: [],
    );
  }

  /// `Forgot passcode?`
  String get forgotPasscode {
    return Intl.message(
      'Forgot passcode?',
      name: 'forgotPasscode',
      desc: '',
      args: [],
    );
  }

  /// `Show More`
  String get showMore {
    return Intl.message(
      'Show More',
      name: 'showMore',
      desc: '',
      args: [],
    );
  }

  /// `Post Setting`
  String get postSetting {
    return Intl.message(
      'Post Setting',
      name: 'postSetting',
      desc: '',
      args: [],
    );
  }

  /// `Done`
  String get done {
    return Intl.message(
      'Done',
      name: 'done',
      desc: '',
      args: [],
    );
  }

  /// `Add to Tome`
  String get addToTome {
    return Intl.message(
      'Add to Tome',
      name: 'addToTome',
      desc: '',
      args: [],
    );
  }

  /// `Nothing to see here`
  String get nothingToseeHere {
    return Intl.message(
      'Nothing to see here',
      name: 'nothingToseeHere',
      desc: '',
      args: [],
    );
  }

  /// `Search`
  String get search {
    return Intl.message(
      'Search',
      name: 'search',
      desc: '',
      args: [],
    );
  }

  /// `No Post Type Found`
  String get noPostTypeFound {
    return Intl.message(
      'No Post Type Found',
      name: 'noPostTypeFound',
      desc: '',
      args: [],
    );
  }

  /// `Select Post Type`
  String get selectPostType {
    return Intl.message(
      'Select Post Type',
      name: 'selectPostType',
      desc: '',
      args: [],
    );
  }

  /// `Post Type`
  String get postType {
    return Intl.message(
      'Post Type',
      name: 'postType',
      desc: '',
      args: [],
    );
  }

  /// `Post Type Search`
  String get postTypeSearch {
    return Intl.message(
      'Post Type Search',
      name: 'postTypeSearch',
      desc: '',
      args: [],
    );
  }

  /// `Select Genre`
  String get selectGenre {
    return Intl.message(
      'Select Genre',
      name: 'selectGenre',
      desc: '',
      args: [],
    );
  }

  /// `No Genre Found`
  String get noGenreFound {
    return Intl.message(
      'No Genre Found',
      name: 'noGenreFound',
      desc: '',
      args: [],
    );
  }

  /// `Invalid tag, please start with #`
  String get tagErrorMsg {
    return Intl.message(
      'Invalid tag, please start with #',
      name: 'tagErrorMsg',
      desc: '',
      args: [],
    );
  }

  /// `Add tags`
  String get addTags {
    return Intl.message(
      'Add tags',
      name: 'addTags',
      desc: '',
      args: [],
    );
  }

  /// `Genre`
  String get genre {
    return Intl.message(
      'Genre',
      name: 'genre',
      desc: '',
      args: [],
    );
  }

  /// `Type of Post`
  String get typeOfPost {
    return Intl.message(
      'Type of Post',
      name: 'typeOfPost',
      desc: '',
      args: [],
    );
  }

  /// `Allow Commenting`
  String get allowCommenting {
    return Intl.message(
      'Allow Commenting',
      name: 'allowCommenting',
      desc: '',
      args: [],
    );
  }

  /// `Please enter title`
  String get emptyTitleMsg {
    return Intl.message(
      'Please enter title',
      name: 'emptyTitleMsg',
      desc: '',
      args: [],
    );
  }

  /// `Title`
  String get title {
    return Intl.message(
      'Title',
      name: 'title',
      desc: '',
      args: [],
    );
  }

  /// `Write something`
  String get writeSomething {
    return Intl.message(
      'Write something',
      name: 'writeSomething',
      desc: '',
      args: [],
    );
  }

  /// `Create Post`
  String get createPost {
    return Intl.message(
      'Create Post',
      name: 'createPost',
      desc: '',
      args: [],
    );
  }

  /// `Edit Post`
  String get editPost {
    return Intl.message(
      'Edit Post',
      name: 'editPost',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want\nto delete tome?`
  String get areYouSureYouWantToDeleteTome {
    return Intl.message(
      'Are you sure you want\nto delete tome?',
      name: 'areYouSureYouWantToDeleteTome',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want\nto discard changes`
  String get areYouSureYouWantToDiscardChanges {
    return Intl.message(
      'Are you sure you want\nto discard changes',
      name: 'areYouSureYouWantToDiscardChanges',
      desc: '',
      args: [],
    );
  }

  /// `Discard`
  String get discard {
    return Intl.message(
      'Discard',
      name: 'discard',
      desc: '',
      args: [],
    );
  }

  /// `Save`
  String get save {
    return Intl.message(
      'Save',
      name: 'save',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want\nto delete post?`
  String get areYouSureYouWantToDeletePost {
    return Intl.message(
      'Are you sure you want\nto delete post?',
      name: 'areYouSureYouWantToDeletePost',
      desc: '',
      args: [],
    );
  }

  /// `Yes, Delete`
  String get yesDelete {
    return Intl.message(
      'Yes, Delete',
      name: 'yesDelete',
      desc: '',
      args: [],
    );
  }

  /// `Save as Draft`
  String get saveDraft {
    return Intl.message(
      'Save as Draft',
      name: 'saveDraft',
      desc: '',
      args: [],
    );
  }

  /// `Preview Post`
  String get previewPost {
    return Intl.message(
      'Preview Post',
      name: 'previewPost',
      desc: '',
      args: [],
    );
  }

  /// `Please enter description`
  String get emptyDescriptionMsg {
    return Intl.message(
      'Please enter description',
      name: 'emptyDescriptionMsg',
      desc: '',
      args: [],
    );
  }

  /// `Post Preview`
  String get postPreview {
    return Intl.message(
      'Post Preview',
      name: 'postPreview',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<I10n> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<I10n> load(Locale locale) => I10n.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
