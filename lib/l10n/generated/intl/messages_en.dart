// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "addTags": MessageLookupByLibrary.simpleMessage("Add tags"),
        "addToTome": MessageLookupByLibrary.simpleMessage("Add to Tome"),
        "allowCommenting":
            MessageLookupByLibrary.simpleMessage("Allow Commenting"),
        "areYouSureYouWantToDeletePost": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want\nto delete post?"),
        "areYouSureYouWantToDeleteTome": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want\nto delete tome?"),
        "areYouSureYouWantToDiscardChanges":
            MessageLookupByLibrary.simpleMessage(
                "Are you sure you want\nto discard changes"),
        "avatar": MessageLookupByLibrary.simpleMessage("Avatar"),
        "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
        "createPost": MessageLookupByLibrary.simpleMessage("Create Post"),
        "createTome": MessageLookupByLibrary.simpleMessage("Create Tome"),
        "delete": MessageLookupByLibrary.simpleMessage("Delete"),
        "details": MessageLookupByLibrary.simpleMessage("Details"),
        "discard": MessageLookupByLibrary.simpleMessage("Discard"),
        "done": MessageLookupByLibrary.simpleMessage("Done"),
        "draft": MessageLookupByLibrary.simpleMessage("Draft"),
        "draftPosts": MessageLookupByLibrary.simpleMessage("Draft Posts"),
        "edit": MessageLookupByLibrary.simpleMessage("Edit"),
        "editPost": MessageLookupByLibrary.simpleMessage("Edit Post"),
        "editTome": MessageLookupByLibrary.simpleMessage("Edit Tome"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "emptyDescriptionMsg":
            MessageLookupByLibrary.simpleMessage("Please enter description"),
        "emptyEmailMsg":
            MessageLookupByLibrary.simpleMessage("Please enter your email"),
        "emptyPasswordMsg":
            MessageLookupByLibrary.simpleMessage("Please enter your password"),
        "emptyTitleMsg":
            MessageLookupByLibrary.simpleMessage("Please enter title"),
        "emptyTomDetailsMsg":
            MessageLookupByLibrary.simpleMessage("Please enter tom details"),
        "emptyTomNameMsg":
            MessageLookupByLibrary.simpleMessage("Please enter tom name"),
        "enterPasscode": MessageLookupByLibrary.simpleMessage("Enter passcode"),
        "errorEmailMsg": MessageLookupByLibrary.simpleMessage(
            "The email address is not valid"),
        "errorPasswordMsg": MessageLookupByLibrary.simpleMessage(
            "Password must be at least 6 characters"),
        "followers": MessageLookupByLibrary.simpleMessage("Followers"),
        "following": MessageLookupByLibrary.simpleMessage("Following"),
        "forgotPasscode":
            MessageLookupByLibrary.simpleMessage("Forgot passcode?"),
        "gallery": MessageLookupByLibrary.simpleMessage("Gallery"),
        "genre": MessageLookupByLibrary.simpleMessage("Genre"),
        "myPosts": MessageLookupByLibrary.simpleMessage("My Posts"),
        "noGenreFound": MessageLookupByLibrary.simpleMessage("No Genre Found"),
        "noPostTypeFound":
            MessageLookupByLibrary.simpleMessage("No Post Type Found"),
        "nothingToseeHere":
            MessageLookupByLibrary.simpleMessage("Nothing to see here"),
        "password": MessageLookupByLibrary.simpleMessage("Password"),
        "post": MessageLookupByLibrary.simpleMessage("Post"),
        "postFilter": MessageLookupByLibrary.simpleMessage("Post Filter"),
        "postPreview": MessageLookupByLibrary.simpleMessage("Post Preview"),
        "postSetting": MessageLookupByLibrary.simpleMessage("Post Setting"),
        "postType": MessageLookupByLibrary.simpleMessage("Post Type"),
        "postTypeSearch":
            MessageLookupByLibrary.simpleMessage("Post Type Search"),
        "posts": MessageLookupByLibrary.simpleMessage("Posts"),
        "previewPost": MessageLookupByLibrary.simpleMessage("Preview Post"),
        "save": MessageLookupByLibrary.simpleMessage("Save"),
        "saveDraft": MessageLookupByLibrary.simpleMessage("Save as Draft"),
        "search": MessageLookupByLibrary.simpleMessage("Search"),
        "selectFrom": MessageLookupByLibrary.simpleMessage("Select From"),
        "selectGenre": MessageLookupByLibrary.simpleMessage("Select Genre"),
        "selectPostType":
            MessageLookupByLibrary.simpleMessage("Select Post Type"),
        "share": MessageLookupByLibrary.simpleMessage("Share"),
        "showMore": MessageLookupByLibrary.simpleMessage("Show More"),
        "splashText1": MessageLookupByLibrary.simpleMessage(
            "One Platform, Two Identities"),
        "tagErrorMsg": MessageLookupByLibrary.simpleMessage(
            "Invalid tag, please start with #"),
        "tapToReadMore":
            MessageLookupByLibrary.simpleMessage("Tap to read more"),
        "title": MessageLookupByLibrary.simpleMessage("Title"),
        "tomeName": MessageLookupByLibrary.simpleMessage("Tome Name"),
        "tomes": MessageLookupByLibrary.simpleMessage("Tomes"),
        "tribe": MessageLookupByLibrary.simpleMessage("Tribe"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("Try Again"),
        "typeOfPost": MessageLookupByLibrary.simpleMessage("Type of Post"),
        "upload": MessageLookupByLibrary.simpleMessage("Upload"),
        "viewAll": MessageLookupByLibrary.simpleMessage("View All"),
        "writeSomething":
            MessageLookupByLibrary.simpleMessage("Write something"),
        "yesDelete": MessageLookupByLibrary.simpleMessage("Yes, Delete"),
        "youCanAddPostsAndMakeGroups": MessageLookupByLibrary.simpleMessage(
            "You can add posts and make groups")
      };
}
