import 'package:flutter/foundation.dart';

extension StringExtention on String {
  String? get isDebugging => kDebugMode ? this : null;
  String get inCaps => length > 0 ? '${this[0].toUpperCase()}${substring(1).toLowerCase()}' : '';
  String get allInCaps => toUpperCase();
  String get capitalizeFirstofEach => replaceAll(RegExp(' +'), ' ').split(' ').map((str) => str.inCaps).join(' ');
  String get morderncapitalizeFirstofEach =>
      replaceAll(RegExp(' +'), ' ').split(' ').map((str) => str.length <= 2 ? str.toLowerCase() : str.inCaps).join(' ');
}

extension StringNullExtention on String? {
  bool get isPureValid => this != null && this!.trim().isNotEmpty;
  bool get isNotPureValid => this == null || (this?.trim().isEmpty ?? false);
}
