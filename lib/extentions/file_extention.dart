import 'dart:typed_data';

import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:tynt_web/utlity/logger_config.dart';

extension FileExtention on Uint8List {
  Future<Uint8List> compressFile() async {
    final kFile = this;
    try {
      final compressedFile = await FlutterImageCompress.compressWithList(
        kFile,
        quality: _qualityForCompress,
      );
      debugLog('Image Comressed');
      return compressedFile;
    } catch (e) {
      debugError('IMAGE COMPRESS ERRROR $e');
      return kFile;
    }
  }

  int get _qualityForCompress {
    final sizeInBytes = lengthInBytes;
    final sizeInMb = sizeInBytes / (1024 * 1024);

    final isSizeMoreThan5Mb = sizeInMb >= 5;
    final isSizeMoreThan1Mb = sizeInMb >= 1;

    final quality = isSizeMoreThan5Mb
        ? 60
        : isSizeMoreThan1Mb
            ? 75
            : 100;

    return quality;
  }
}
