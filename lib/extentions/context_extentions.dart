import 'package:flutter/material.dart';
import 'package:responsive_builder/responsive_builder.dart';

extension ContextExtentions on BuildContext {
  ThemeData get theme => Theme.of(this);
  TextTheme get textTheme => theme.textTheme;

  bool get isMobile => getValueForScreenType(context: this, mobile: true, tablet: false, desktop: false);
  bool get isTablet => getValueForScreenType(context: this, mobile: false, tablet: true, desktop: false);

  bool get isLargeorExtraLarge =>
      getValueForRefinedSize(context: this, normal: false, small: false, extraLarge: true, large: true);
  bool get isNormal =>
      getValueForRefinedSize(context: this, normal: true, small: false, extraLarge: false, large: false);
  bool get isSmall =>
      getValueForRefinedSize(context: this, normal: false, small: true, extraLarge: false, large: false);
}
