import 'package:equatable/equatable.dart';

class CountryModel extends Equatable {
  const CountryModel({
    this.id,
    this.name,
    this.phoneCode,
  });

  factory CountryModel.fromJson(Map<String, dynamic> json) {
    return CountryModel(
      id: json['id'] as int?,
      name: json['name'] as String?,
      phoneCode: json['phone_code'] as String?,
    );
  }
  final int? id;
  final String? name;
  final String? phoneCode;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone_code': phoneCode,
    };
  }

  @override
  List<Object?> get props => [id, name, phoneCode];

  @override
  String toString() {
    return 'CountryModel{id: $id, name: $name, phoneCode: $phoneCode}';
  }
}
