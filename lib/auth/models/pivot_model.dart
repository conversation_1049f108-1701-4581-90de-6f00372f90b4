import 'package:equatable/equatable.dart';

class PivotModel extends Equatable {
  const PivotModel({
    this.fromUserId,
    this.toUserId,
    this.message,
  });

  factory PivotModel.fromJson(Map<String, dynamic> json) {
    return PivotModel(
      fromUserId: json['from_user_id'] as int?,
      toUserId: json['to_user_id'] as int?,
      message: json['message'] as String?,
    );
  }
  final int? fromUserId;
  final int? toUserId;
  final String? message;

  Map<String, dynamic> toJson() {
    return {
      'from_user_id': fromUserId,
      'to_user_id': toUserId,
      'message': message,
    };
  }

  @override
  List<Object?> get props => [fromUserId, toUserId, message];

  @override
  String toString() {
    return 'PivotModel{fromUserId: $fromUserId, toUserId: $toUserId, message: $message}';
  }
}
