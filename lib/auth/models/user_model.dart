import 'package:equatable/equatable.dart';
import 'package:tynt_web/auth/models/avatar_model.dart';
import 'package:tynt_web/auth/models/country_model.dart';
import 'package:tynt_web/auth/models/pivot_model.dart';
import 'package:tynt_web/constants/app_strings.dart';
import 'package:tynt_web/extentions/string_extentions.dart';
import 'package:tynt_web/utlity/utlity.dart';

class UserModel extends Equatable {
  const UserModel({
    required this.id,
    this.type = AppStrings.public,
    this.firstName,
    this.lastName,
    this.userName,
    this.email,
    this.phoneNumber,
    this.phoneCode,
    this.countryId,
    this.profileImagePath,
    this.bio,
    this.isEmailVerified = 0,
    this.emailVerifiedAt,
    this.isFollowing = false,
    this.createdAt,
    this.followersCount = 0,
    this.followingCount = 0,
    this.isPasswordSet = false,
    this.isFollowBack = false,
    this.postsCount = 0,
    this.country,
    this.authenticateId,
    this.state,
    this.gender,
    this.avatar,
    this.pivot,
    this.updatedAt,
    this.isTempUser = false,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as int,
      type: (json['type'] ?? AppStrings.public) as String,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      userName: json['user_name'] as String?,
      email: json['email'] as String?,
      phoneNumber: json['phone_number'] as String?,
      phoneCode: json['phone_code'] as String?,
      countryId: json['country_id'] != null ? (json['country_id'] as int) : null,
      profileImagePath: json['profile_image_path'] as String?,
      bio: json['bio'] as String?,
      isEmailVerified: json['is_email_verified'] != null ? (json['is_email_verified'] as int) : null,
      emailVerifiedAt: json['email_verified_at'] as String?,
      isFollowing: (json['is_following'] ?? false) as bool,
      createdAt: json['created_at'] as String?,
      followersCount: json['followers_count'] != null ? (json['followers_count'] as int) : null,
      followingCount: json['following_count'] != null ? (json['following_count'] as int) : null,
      isPasswordSet: (json['is_password_set'] ?? false) as bool,
      isFollowBack: (json['is_follow_back'] ?? false) as bool,
      postsCount: json['posts_count'] != null ? (json['posts_count'] as int) : null,
      country: json['country'] != null ? CountryModel.fromJson(json['country'] as Map<String, dynamic>) : null,
      authenticateId: json['authenticate_id'] != null ? (json['authenticate_id'] as int) : null,
      state: json['state'] as String?,
      gender: json['gender'] as String?,
      avatar: json['avatar'] != null ? AvatarModel.fromJson(json['avatar'] as Map<String, dynamic>) : null,
      pivot: json['pivot'] != null ? PivotModel.fromJson(json['pivot'] as Map<String, dynamic>) : null,
      updatedAt: json['updated_at'] as String?,

      // `is_temp_user` is intentionally excluded from JSON mapping
    );
  }
  final int id;
  final String type;
  final String? firstName;
  final String? lastName;
  final String? userName;
  final String? email;
  final String? phoneNumber;
  final String? phoneCode;
  final int? countryId;
  final String? profileImagePath;
  final String? bio;
  final int? isEmailVerified;
  final String? emailVerifiedAt;
  final bool isFollowing;
  final String? createdAt;
  final int? followersCount;
  final int? followingCount;
  final bool isPasswordSet;
  final bool isFollowBack;
  final int? postsCount;
  final CountryModel? country;
  final int? authenticateId;
  final String? state;
  final String? gender;
  final AvatarModel? avatar;
  final PivotModel? pivot;
  final String? updatedAt;
  final bool isTempUser;
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'first_name': firstName,
      'last_name': lastName,
      'user_name': userName,
      'email': email,
      'phone_number': phoneNumber,
      'phone_code': phoneCode,
      'country_id': countryId,
      'profile_image_path': profileImagePath,
      'bio': bio,
      'is_email_verified': isEmailVerified,
      'email_verified_at': emailVerifiedAt,
      'is_following': isFollowing,
      'created_at': createdAt,
      'followers_count': followersCount,
      'following_count': followingCount,
      'is_password_set': isPasswordSet,
      'is_follow_back': isFollowBack,
      'posts_count': postsCount,
      'country': country?.toJson(),
      'authenticate_id': authenticateId,
      'state': state,
      'gender': gender,
      'avatar': avatar?.toJson(),
      'pivot': pivot?.toJson(),
      'updated_at': updatedAt,
      // `is_temp_user` excluded from JSON output as per original
    };
  }

  @override
  List<Object?> get props => [
        id,
        type,
        firstName,
        lastName,
        userName,
        email,
        phoneNumber,
        phoneCode,
        countryId,
        profileImagePath,
        bio,
        isEmailVerified,
        emailVerifiedAt,
        isFollowing,
        createdAt,
        followersCount,
        followingCount,
        isPasswordSet,
        isFollowBack,
        postsCount,
        country,
        authenticateId,
        state,
        gender,
        avatar,
        pivot,
        updatedAt,
        isTempUser,
      ];

  static const empty = UserModel(id: 0);

  // ---------------- Computed Getters ----------------

  String? get getProfileImageUrl => profileImagePath == null ? null : '${AppStrings.storageUrl}/$profileImagePath';

  String? get getAvatarImageUrl => avatar == null ? null : '${AppStrings.storageUrl}/${avatar?.iconPath ?? ''}';

  String? get formmtedAvatarProfile => getProfileImageUrl ?? getAvatarImageUrl;

  String? get _fullName {
    if (isPseudoUser || isBusinessUser) return userName;
    if (firstName != null && lastName != null) return '$firstName $lastName';
    if (firstName != null) return firstName;
    return userName;
  }

  String? get fullName => (_fullName ?? 'Pseudo profile').inCaps;

  String get formmtedUserName => userName?.toLowerCase() ?? '';

  bool get isPseudoUserNotSetuped => userName == null && isPseudoUser;

  bool get isPseudoUser => type == AppStrings.pseudo;
  bool get isPublicUser => type == AppStrings.public;
  bool get isBusinessUser => type == AppStrings.business;

  bool get showFollowButton => isPublicUser || isBusinessUser;

  String get formettedPostCount => Utility.formatNumber(postsCount ?? 0);
  String get formettedFollowingCount => Utility.formatNumber(followingCount ?? 0);
  String get formettedFollowersCount => Utility.formatNumber(followersCount ?? 0);

  @override
  String toString() {
    return 'UserModel{id: $id, type: $type, firstName: $firstName, lastName: $lastName, userName: $userName, email: $email, phoneNumber: $phoneNumber, phoneCode: $phoneCode, countryId: $countryId, profileImagePath: $profileImagePath, bio: $bio, isEmailVerified: $isEmailVerified, emailVerifiedAt: $emailVerifiedAt, isFollowing: $isFollowing, createdAt: $createdAt, followersCount: $followersCount, followingCount: $followingCount, isPasswordSet: $isPasswordSet, isFollowBack: $isFollowBack, postsCount: $postsCount, country: $country, authenticateId: $authenticateId, state: $state, gender: $gender, avatar: $avatar, pivot: $pivot, updatedAt: $updatedAt, isTempUser: $isTempUser}';
  }
}
