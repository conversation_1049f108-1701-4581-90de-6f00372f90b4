import 'package:equatable/equatable.dart';
import 'package:tynt_web/constants/app_strings.dart';

class AvatarModel extends Equatable {
  const AvatarModel({this.id, this.iconPath});

  factory AvatarModel.fromJson(Map<String, dynamic> json) {
    return AvatarModel(
      id: json['id'] as int?,
      iconPath: json['icon_path'] as String?,
    );
  }
  final int? id;
  final String? iconPath;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'icon_path': iconPath,
    };
  }

  String? get imageUrl => iconPath == null ? null : '${AppStrings.storageUrl}/$iconPath';

  @override
  List<Object?> get props => [id, iconPath];

  @override
  String toString() {
    return 'AvatarModel{id: $id, iconPath: $iconPath}';
  }
}
