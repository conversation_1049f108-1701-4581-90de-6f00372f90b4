// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:tynt_web/auth/models/avatar_model.dart';
import 'package:tynt_web/constants/app_assets.dart';
// import 'package:taletalks/constants/app_assets.dart';
// import 'package:taletalks/user/models/avatar_model.dart';

@immutable
class AppFileData {
  const AppFileData({
    this.imageBytes,
    this.networkUrl,
    this.selectedAvatar,
    this.defaultAvatar = AppAssets.defaultMan,
  });
  final Uint8List? imageBytes;
  final String? networkUrl;
  final AvatarModel? selectedAvatar;
  final String defaultAvatar;
  // File? get getStorageFile => imageBytes == null || imageBytes!.isEmpty ? null : File.fromRawPath(imageBytes!);
  @override
  String toString() {
    return 'AppAvatarData(imagePath: $imageBytes, networkUrl: $networkUrl, selectedAvatar: $selectedAvatar, defaultAvatar: $defaultAvatar)';
  }

  AppFileData copyWith({
    Uint8List? imageBytes,
    String? networkUrl,
    AvatarModel? selectedAvatar,
    String? defaultAvatar,
  }) {
    return AppFileData(
      imageBytes: imageBytes ?? this.imageBytes,
      networkUrl: networkUrl ?? this.networkUrl,
      selectedAvatar: selectedAvatar ?? this.selectedAvatar,
      defaultAvatar: defaultAvatar ?? this.defaultAvatar,
    );
  }

  @override
  bool operator ==(covariant AppFileData other) {
    if (identical(this, other)) return true;

    return other.imageBytes == imageBytes &&
        other.networkUrl == networkUrl &&
        other.selectedAvatar == selectedAvatar &&
        other.defaultAvatar == defaultAvatar;
  }

  @override
  int get hashCode {
    return imageBytes.hashCode ^ networkUrl.hashCode ^ selectedAvatar.hashCode ^ defaultAvatar.hashCode;
  }
}
