import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:tynt_web/auth/models/user_model.dart';
import 'package:tynt_web/auth/repository/i_auth_repository.dart';
import 'package:tynt_web/local_storage/repository/i_local_storage_repository.dart';
import 'package:tynt_web/user/repository/i_user_manager_repository.dart';
import 'package:tynt_web/user/response/user_response.dart';
import 'package:tynt_web/utlity/failure/http_failure.dart';

part 'authentication_event.dart';
part 'authentication_state.dart';

@lazySingleton
class AuthenticationBloc extends Bloc<AuthenticationEvent, AuthenticationState> {
  AuthenticationBloc({
    required this.authRepository,
    required this.userManagerRepository,
    required this.localStorageRepository,
  }) : super(const AuthenticationState.unknown()) {
    on<CheckAuthentication>(_onCheckAuthentication);
    on<ChangeCurrentUserEvent>(_onChangeCurrentUserEvent);
    on<UpdateUserEvent>(_onUpdateUser);
  }
  final IAuthRepository authRepository;
  final IUserManagerRepository userManagerRepository;
  final ILocalStorageRepository localStorageRepository;

  Future<void> _onCheckAuthentication(
    CheckAuthentication event,
    Emitter<AuthenticationState> emit,
  ) async {
    if (event.user != null && event.user?.publicUser != null) {
      emit(AuthenticationState.autheticated(event.user!, event.user!.publicUser!));
      return;
    }

    if (localStorageRepository.getUserToken == null) {
      emit(const AuthenticationState.unAutheticated(null));
      return;
    }

    final userResponse = await userManagerRepository.getUserResponse();

    emit(
      userResponse.fold(AuthenticationState.unAutheticated, (user) {
        if (user.data == null || user.data?.publicUser == null) {
          return AuthenticationState.unAutheticated(HttpFailure.unauthorized());
        }

        final currentUser = localStorageRepository.getTyntUserToken != null
            ? (user.data!.pseudoUser ?? user.data!.publicUser!)
            : user.data!.publicUser!;

        return AuthenticationState.autheticated(user.data!, currentUser);
      }),
    );
  }

  Future<void> _onChangeCurrentUserEvent(
    ChangeCurrentUserEvent event,
    Emitter<AuthenticationState> emit,
  ) async {
    if (!state.isAutheticated) return;
    final newAuthState = state;

    final isTyntUser = newAuthState.currentUser.isPseudoUser;

    final updatedCurrentUser =
        (isTyntUser ? newAuthState.user.publicUser : newAuthState.user.pseudoUser) ?? newAuthState.currentUser;

    emit(newAuthState.copyWith(currentUser: updatedCurrentUser));
  }

  void _onUpdateUser(
    UpdateUserEvent event,
    Emitter<AuthenticationState> emit,
  ) {
    if (!state.isAutheticated || event.user == null) return;
    final newAuthState = state;
    emit(
      newAuthState.copyWith(
        user: event.user,
        currentUser: newAuthState.currentUser.isPublicUser || newAuthState.currentUser.isBusinessUser
            ? event.user!.publicUser ?? newAuthState.currentUser
            : event.user!.pseudoUser ?? newAuthState.currentUser,
      ),
    );
  }
}
