part of 'authentication_bloc.dart';

enum AuthenticationStatus { unknown, loading, autheticated, unAutheticated }

final class AuthenticationState extends Equatable {
  const AuthenticationState._({
    this.status = AuthenticationStatus.unknown,
    this.currentUser = UserModel.empty,
    this.user = UserResponse.empty,
    this.failure,
    this.isLoading = false,
    this.isStackLoading = false,
  });

  const AuthenticationState.unknown() : this._();

  const AuthenticationState.loading() : this._(status: AuthenticationStatus.loading);

  const AuthenticationState.autheticated(UserResponse user, UserModel currentUser)
      : this._(status: AuthenticationStatus.autheticated, user: user, currentUser: currentUser);

  const AuthenticationState.unAutheticated(HttpFailure? failure)
      : this._(status: AuthenticationStatus.unAutheticated, failure: failure);

  final AuthenticationStatus status;
  final UserModel currentUser;
  final UserResponse user;
  final bool isLoading;
  final bool isStackLoading;
  final HttpFailure? failure;

  @override
  List<Object?> get props => [status, user, currentUser, failure, isLoading, isStackLoading];

  @override
  String toString() {
    return 'AuthenticationState(status: $status, currentUser: $currentUser, user: $user, failure: $failure, isLoading: $isLoading, isStackLoading: $isStackLoading)';
  }

  AuthenticationState copyWith({
    AuthenticationStatus? status,
    UserModel? currentUser,
    UserResponse? user,
    HttpFailure? failure,
    HttpFailure? likeFailure,
    bool? isLoading,
    bool? isStackLoading,
  }) {
    return AuthenticationState._(
      status: status ?? this.status,
      currentUser: currentUser ?? this.currentUser,
      user: user ?? this.user,
      failure: failure ?? this.failure,
      isLoading: isLoading ?? this.isLoading,
      isStackLoading: isStackLoading ?? this.isStackLoading,
    );
  }

  R? whenOrNull<R>({
    R Function()? unknown,
    R Function()? loading,
    R Function(UserModel user)? autheticated,
    R Function(HttpFailure? failure)? unAutheticated,
  }) {
    switch (status) {
      case AuthenticationStatus.unknown:
        return unknown?.call();
      case AuthenticationStatus.loading:
        return loading?.call();
      case AuthenticationStatus.autheticated:
        return autheticated?.call(currentUser);
      case AuthenticationStatus.unAutheticated:
        return unAutheticated?.call(failure);
    }
  }

  R when<R>({
    required R Function() unknown,
    required R Function() loading,
    required R Function(UserModel user) autheticated,
    required R Function(HttpFailure? failure) unAutheticated,
  }) {
    switch (status) {
      case AuthenticationStatus.unknown:
        return unknown();
      case AuthenticationStatus.loading:
        return loading();
      case AuthenticationStatus.autheticated:
        return autheticated(currentUser);
      case AuthenticationStatus.unAutheticated:
        return unAutheticated(failure);
    }
  }

  R maybeWhen<R>({
    required R Function() orElse,
    R Function()? unknown,
    R Function()? loading,
    R Function(UserModel user)? autheticated,
    R Function(HttpFailure? failure)? unAutheticated,
  }) {
    switch (status) {
      case AuthenticationStatus.unknown:
        return unknown?.call() ?? orElse();
      case AuthenticationStatus.loading:
        return loading?.call() ?? orElse();
      case AuthenticationStatus.autheticated:
        return autheticated?.call(currentUser) ?? orElse();
      case AuthenticationStatus.unAutheticated:
        return unAutheticated?.call(failure) ?? orElse();
    }
  }

  bool get isAutheticated => status == AuthenticationStatus.autheticated;

  bool get isUnAutheticated => status == AuthenticationStatus.unAutheticated || status == AuthenticationStatus.unknown;

  bool get isAuthLoading => status == AuthenticationStatus.loading || status == AuthenticationStatus.unknown;
}
