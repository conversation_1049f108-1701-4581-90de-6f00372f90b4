part of 'authentication_bloc.dart';

sealed class AuthenticationEvent extends Equatable {
  const AuthenticationEvent();

  @override
  List<Object?> get props => [];
}

final class CheckAuthentication extends AuthenticationEvent {
  const CheckAuthentication({this.user});

  final UserResponse? user;

  @override
  List<Object?> get props => [user];

  @override
  String toString() {
    return 'CheckAuthentication(user: $user)';
  }
}

final class ChangeCurrentUserEvent extends AuthenticationEvent {
  const ChangeCurrentUserEvent();

  @override
  List<Object?> get props => [];
}

final class GetUser extends AuthenticationEvent {
  const GetUser();
}

final class UpdateUserEvent extends AuthenticationEvent {
  const UpdateUserEvent({this.user});
  final UserResponse? user;

  @override
  List<Object?> get props => [];
}
