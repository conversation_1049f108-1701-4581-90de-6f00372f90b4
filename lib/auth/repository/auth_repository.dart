part of 'i_auth_repository.dart';

@Injectable(as: IAuthRepository)
class AuthRepository extends IAuthRepository {
  AuthRepository(super.client);

  @override
  ApiResult<LoginResponse> login({
    required String email,
    required String password,
  }) async {
    final response = await client.post(
      url: AppStrings.login,
      requests: <String, dynamic>{
        'email': email,
        'password': password,
      },
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(() => LoginResponse.fromJson(r), (o, s) => HttpFailure.parsing(o.toString(), 500, s)),
    );
  }

  @override
  ApiResult<LoginResponse> appleSignIn() {
    // TODO: implement appleSignIn
    throw UnimplementedError();
  }

  @override
  ApiResult<CommonResponse> forgotPassword({required String email}) {
    // TODO: implement forgotPassword
    throw UnimplementedError();
  }

  @override
  ApiResult<LoginResponse> googleSignIn() async {
    // TODO: implement forgotPassword
    throw UnimplementedError();
  }

  @override
  ApiResult<StringResponse> verifyPasscode({required String passcode}) async {
    final response = await client.post(
      url: AppStrings.verifyPasscode,
      requests: <String, dynamic>{'passcode': passcode},
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(() => StringResponse.fromJson(r), (o, s) => HttpFailure.parsing(o.toString(), 500, s)),
    );
  }
}
