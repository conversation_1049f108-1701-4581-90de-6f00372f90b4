part of 'i_auth_repository.dart';

@Injectable(as: IAuthRepository)
class AuthRepository extends IAuthRepository {
  AuthRepository(super.client);

  @override
  ApiResult<LoginResponse> login({
    required String email,
    required String password,
  }) async {
    final response = await client.post(
      url: AppStrings.login,
      requests: <String, dynamic>{
        'email': email,
        'password': password,
      },
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(() => LoginResponse.fromJson(r), (o, s) => HttpFailure.parsing(o.toString(), 500, s)),
    );
  }

  @override
  ApiResult<LoginResponse> appleSignIn() {
    // TODO: implement appleSignIn
    throw UnimplementedError();
  }

  @override
  ApiResult<CommonResponse> forgotPassword({required String email}) {
    // TODO: implement forgotPassword
    throw UnimplementedError();
  }

  @override
  ApiResult<LoginResponse> googleSignIn() async {
    // TODO: implement appleSignIn
    throw UnimplementedError();
  }

  ApiResult<LoginResponse> _socialLogin(
    SoicalLoginType type,
    String accessToken, {
    String? firstName,
    String? lastName,
  }) async {
    final response = await client.post(
      url: AppStrings.socialLogin,
      requests: <String, dynamic>{
        'provider': type.name,
        'access_token': accessToken,
        // 'firebase_id': FirebaseMessagingService.token ?? '',
        if (firstName?.isPureValid ?? false) 'first_name': firstName,
        if (lastName?.isPureValid ?? false) 'last_name': lastName,
      },
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(() => LoginResponse.fromJson(r), (o, s) => HttpFailure.parsing(o.toString(), 500, s)),
    );
  }

  @override
  ApiResult<StringResponse> verifyPasscode({required String passcode}) async {
    final response = await client.post(
      url: AppStrings.verifyPasscode,
      requests: <String, dynamic>{'passcode': passcode},
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(() => StringResponse.fromJson(r), (o, s) => HttpFailure.parsing(o.toString(), 500, s)),
    );
  }

  @override
  Future<void> googleSignInOut() {
    // TODO: implement googleSignInOut
    throw UnimplementedError();
  }
}
