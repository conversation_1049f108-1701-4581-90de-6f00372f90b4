import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:tynt_web/auth/response/common_response.dart';
import 'package:tynt_web/auth/response/login_response.dart';
import 'package:tynt_web/auth/response/string_response.dart';
import 'package:tynt_web/constants/app_strings.dart';
import 'package:tynt_web/utlity/app_typedefs/http_typedef.dart';
import 'package:tynt_web/utlity/enum/social_login_enums.dart';
import 'package:tynt_web/utlity/extentions/string_extentions.dart';
import 'package:tynt_web/utlity/failure/http_failure.dart';
import 'package:tynt_web/utlity/network/client.dart';

part 'auth_repository.dart';

abstract class IAuthRepository {
  IAuthRepository(this.client);
  final Client client;

  ApiResult<LoginResponse> login({required String email, required String password});

  ApiResult<CommonResponse> forgotPassword({required String email});

  ApiResult<LoginResponse> googleSignIn();

  ApiResult<LoginResponse> appleSignIn();

  ApiResult<StringResponse> verifyPasscode({required String passcode});

  Future<void> googleSignInOut();

  // ApiResult<CommonResponse> deleteAccount();

  // ApiResult<StringResponse> verifyPasscode({required String passcode});

  // ApiResult<CommonResponse> forgotPasscode();
}
