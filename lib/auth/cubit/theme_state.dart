// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'theme_cubit.dart';

class ThemeState extends Equatable {
  const ThemeState({
    required this.currentTheme,
    required this.themeMode,
    required this.currentUserType,
  });
  final ThemeData currentTheme;
  final ThemeMode themeMode;
  final UserType currentUserType;

  @override
  List<Object?> get props => [currentTheme, themeMode, currentUserType];

  ThemeState copyWith({
    ThemeData? currentTheme,
    ThemeMode? themeMode,
    UserType? currentUserType,
  }) {
    return ThemeState(
      currentTheme: currentTheme ?? this.currentTheme,
      themeMode: themeMode ?? this.themeMode,
      currentUserType: currentUserType ?? this.currentUserType,
    );
  }
}
