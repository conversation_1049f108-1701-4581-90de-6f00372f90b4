import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:tynt_web/constants/app_theme.dart';
import 'package:tynt_web/local_storage/repository/i_local_storage_repository.dart';
import 'package:tynt_web/utlity/enum/user_type_enum.dart';

part 'theme_state.dart';

@lazySingleton
class ThemeCubit extends Cubit<ThemeState> {
  ThemeCubit({
    required this.localStorageRepository,
  }) : super(
          ThemeState(
            currentTheme: AppTheme.lightThemeNormalUser,
            themeMode: ThemeMode.light,
            currentUserType: UserType.normal,
          ),
        );
  final ILocalStorageRepository localStorageRepository;

  void loadTheme() {
    final currentUserType = localStorageRepository.getTyntUserToken != null ? UserType.tynt : UserType.normal;
    emit(
      state.copyWith(
        currentTheme: currentUserType == UserType.normal ? AppTheme.lightThemeNormalUser : AppTheme.lightThemeTyntUser,
        currentUserType: currentUserType == UserType.normal ? UserType.normal : UserType.tynt,
      ),
    );
  }

  void changeUserToggle() {
    final userTheme = localStorageRepository.getTheme;
    if (userTheme == ThemeMode.light.name) {
      emit(
        state.copyWith(
          currentTheme:
              state.currentUserType == UserType.normal ? AppTheme.lightThemeTyntUser : AppTheme.lightThemeNormalUser,
          currentUserType: state.currentUserType == UserType.normal ? UserType.tynt : UserType.normal,
        ),
      );
      return;
    }
    emit(
      state.copyWith(
        currentTheme:
            state.currentUserType == UserType.normal ? AppTheme.darkThemeTyntUser : AppTheme.darkThemeNormalUser,
        currentUserType: state.currentUserType == UserType.normal ? UserType.tynt : UserType.normal,
      ),
    );
  }

  void changeTheme({required ThemeMode themeMode}) {
    localStorageRepository.setTheme(themeMode.name);
    if (themeMode == ThemeMode.light) {
      emit(
        state.copyWith(
          themeMode: themeMode,
          currentTheme:
              state.currentUserType == UserType.normal ? AppTheme.lightThemeNormalUser : AppTheme.lightThemeTyntUser,
        ),
      );
      return;
    }

    emit(
      state.copyWith(
        themeMode: themeMode,
        currentTheme:
            state.currentUserType == UserType.normal ? AppTheme.darkThemeNormalUser : AppTheme.darkThemeTyntUser,
      ),
    );
  }

  void setDefaultTheme() {
    localStorageRepository.setTheme(ThemeMode.light.name);
    emit(
      state.copyWith(
        themeMode: ThemeMode.light,
        currentTheme: AppTheme.lightThemeNormalUser,
        currentUserType: UserType.normal,
      ),
    );
  }
}
