import 'package:equatable/equatable.dart';

class StringResponse extends Equatable {
  const StringResponse({
    this.data,
    this.message = '',
    this.status = '0',
  });

  factory StringResponse.fromJson(Map<String, dynamic> json) {
    return StringResponse(
      data: json['data'] as String?,
      message: json['message'] as String? ?? '',
      status: json['status'] as String? ?? '0',
    );
  }
  final String? data;
  final String message;
  final String status;

  Map<String, dynamic> toJson() {
    return {
      'data': data,
      'message': message,
      'status': status,
    };
  }

  @override
  List<Object?> get props => [data, message, status];

  @override
  String toString() {
    return 'StringResponse{data: $data, message: $message, status: $status}';
  }
}
