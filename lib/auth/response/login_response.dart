import 'package:equatable/equatable.dart';
import 'package:tynt_web/user/response/user_response.dart';

class LoginResponse extends Equatable {
  const LoginResponse({
    this.data,
    this.token,
    this.message,
    this.status,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      data: json['data'] == null ? null : UserResponse.fromJson(json['data'] as Map<String, dynamic>),
      token: json['token'] as String?,
      message: json['message'] as String?,
      status: json['status'] as String?,
    );
  }

  final UserResponse? data;
  final String? token;
  final String? message;
  final String? status;

  Map<String, dynamic> toJson() => {
        'data': data?.toJson(),
        'token': token,
        'message': message,
        'status': status,
      };

  @override
  List<Object?> get props => [
        data,
        token,
        message,
        status,
      ];

  @override
  String toString() {
    return 'LoginResponse{data: $data, token: $token, message: $message, status: $status}';
  }
}
