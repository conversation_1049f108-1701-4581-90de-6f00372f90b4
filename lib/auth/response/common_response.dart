import 'package:equatable/equatable.dart';
import 'package:tynt_web/auth/models/user_model.dart';

class CommonResponse extends Equatable {
  const CommonResponse({
    this.data,
    this.token,
    this.message,
    this.status,
  });

  factory CommonResponse.fromJson(Map<String, dynamic> json) {
    return CommonResponse(
      data: json['data'] == null ? null : UserModel.fromJson(json['data'] as Map<String, dynamic>),
      token: json['token'] as String?,
      message: json['message'] as String?,
      status: json['status'] as String?,
    );
  }
  final UserModel? data;
  final String? token;
  final String? message;
  final String? status;

  Map<String, dynamic> toJson() {
    return {
      'data': data?.toJson(),
      'token': token,
      'message': message,
      'status': status,
    };
  }

  @override
  List<Object?> get props => [data, token, message, status];

  @override
  String toString() {
    return 'CommonResponse{data: $data, token: $token, message: $message, status: $status}';
  }
}
