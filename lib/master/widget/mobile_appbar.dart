// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:tynt_web/auth/bloc/authentication_bloc.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/master/widget/app_title.dart';
import 'package:tynt_web/master/widget/appbar_divider.dart';
import 'package:tynt_web/routes/app_routes.dart';
import 'package:tynt_web/widgets/home_leading_widget.dart';

class MobileAppbar extends StatelessWidget implements PreferredSizeWidget {
  const MobileAppbar({
    super.key,
    this.onMenuTap,
  });
  final VoidCallback? onMenuTap;

  @override
  Widget build(BuildContext context) {
    return BlocSelector<AuthenticationBloc, AuthenticationState, bool>(
      selector: (state) => state.isAuthLoading,
      builder: (context, isAuthLoading) {
        return Visibility(
          visible: !isAuthLoading,
          child: AppBar(
            elevation: 0,
            leading: InkWell(
              onTap: () {
                context.pushNamed(AppRoutes.editProfile.name);
              },
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: SvgPicture.asset(
                  AppAssets.appIcon,
                  height: 80,
                ),
              ),
            ),
            centerTitle: false,
            actions: const [
              HomeLeadingWidget(),
              SizedBox(
                width: 16,
              )
            ],
            title: const AppTitle(),
            bottom: const AppbarDivider(),
          ),
        );
      },
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
