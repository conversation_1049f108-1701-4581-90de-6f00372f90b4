import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:tynt_web/auth/bloc/authentication_bloc.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/master/widget/app_title.dart';
import 'package:tynt_web/master/widget/appbar_divider.dart';
import 'package:tynt_web/routes/app_routes.dart';
import 'package:tynt_web/routes/go_routers.dart';
import 'package:tynt_web/widgets/common_button_widget.dart';
import 'package:tynt_web/widgets/home_leading_widget.dart';

class DesktopAppbar extends StatelessWidget implements PreferredSizeWidget {
  const DesktopAppbar({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<AuthenticationBloc, AuthenticationState, bool>(
      selector: (state) => state.isAuthLoading,
      builder: (context, isAuthLoading) {
        return Visibility(
          visible: !isAuthLoading,
          child: AppBar(
            leadingWidth: 90,
            titleSpacing: 0,
            leading: InkWell(
              onTap: () {
                context.pushNamed(AppRoutes.editProfile.name);
              },
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: SvgPicture.asset(
                  AppAssets.appIcon,
                  height: 80,
                ),
              ),
            ),
            actions: [
              BlocSelector<AuthenticationBloc, AuthenticationState, bool>(
                selector: (state) => state.user.id != 0,
                builder: (context, isLoggedIn) {
                  return Visibility(
                    visible: isLoggedIn,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (goRouter.location != AppRoutes.createPost.route)
                          CommonButton(
                            buttonText: context.l10n.createPost,
                            onTap: () {
                              if (goRouter.location == AppRoutes.createPost.route) return;
                              context.pushNamed(AppRoutes.createPost.name);
                            },
                          ),
                      ],
                    ),
                  );
                },
              ),
              const HomeLeadingWidget(),
              if (!context.isMobile)
                const SizedBox(
                  width: 20,
                )
            ],
            centerTitle: false,
            title: const AppTitle(),
            bottom: const AppbarDivider(),
          ),
        );
      },
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
