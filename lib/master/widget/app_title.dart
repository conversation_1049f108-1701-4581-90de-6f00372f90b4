import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/routes/app_routes.dart';

class AppTitle extends StatelessWidget {
  const AppTitle({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        context.pushNamed(AppRoutes.editProfile.name);
      },
      child: Text(
        'TYNT',
        style: GoogleFonts.notoSans(
          color: AppColors.black,
          fontSize: 18,
          letterSpacing: 0,
          wordSpacing: 0,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
