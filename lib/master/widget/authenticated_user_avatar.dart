// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/widgets/app_image_network.dart';

class AuthenticatedUserAvatar extends StatelessWidget {
  const AuthenticatedUserAvatar({
    required this.radius,
    super.key,
    this.usePseudoUserProfile = false,
    this.newImageUrl,
  });
  final double radius;
  final bool usePseudoUserProfile;
  final String? newImageUrl;

  @override
  Widget build(BuildContext context) {
    return Skeleton.shade(
      child: AppImageNetwork(
        url: newImageUrl ?? '',
        height: radius * 2,
        width: radius * 2,
        errorAssetIcon: AppAssets.defaultMan,
        imageBuilder: (context, imageProvider) => CircleAvatar(
          backgroundImage: imageProvider,
          radius: radius * 2,
          backgroundColor: AppColors.white,
        ),
      ),
    );
  }
}
