import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:tynt_web/auth/bloc/authentication_bloc.dart';
import 'package:tynt_web/auth/cubit/theme_cubit.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/colors_extnetions.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/local_storage/repository/i_local_storage_repository.dart';
import 'package:tynt_web/utlity/enum/user_type_enum.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/dailog_boxes.dart';

class UserSwitcher extends StatefulWidget {
  const UserSwitcher({
    required this.onTap,
    super.key,
    this.width = 130,
    this.animationDuration = const Duration(milliseconds: 600),
  });

  final double width;

  final ValueChanged<TapDownDetails> onTap;

  final Duration animationDuration;

  @override
  RollingSwitchState createState() => RollingSwitchState();
}

class RollingSwitchState extends State<UserSwitcher> with SingleTickerProviderStateMixin {
  late AnimationController animationController;
  late Animation<double> animation;

  double animationValue = 0;
  final ValueNotifier<bool> isChaningUser = ValueNotifier(false);

  final ValueNotifier<bool> isTyntUser = ValueNotifier(false);

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );
    animation = CurvedAnimation(parent: animationController, curve: Curves.easeInOut);
    animationController.addListener(changeAnimationValueListener);
    setInitalValue();
  }

  void changeAnimationValueListener() {
    if (mounted) {
      setState(() {
        animationValue = animation.value;
      });
    }
  }

  void setInitalValue() {
    final isPseudoUser = context.read<AuthenticationBloc>().state.currentUser.isPseudoUser;

    if (isPseudoUser) {
      animationController.forward();
      isTyntUser.value = true;
    }
  }

  @override
  void dispose() {
    animationController.removeListener(changeAnimationValueListener);
    disposeAnimationController();
    super.dispose();
  }

  void disposeAnimationController() => animationController.dispose();

  @override
  Widget build(BuildContext context) {
    //Color transition animation
    // final transitionColor = Color.lerp(AppColors.secondary, AppColors.primary, animationValue);

    return BlocListener<ThemeCubit, ThemeState>(
      listenWhen: (previous, current) => previous.currentUserType != current.currentUserType,
      listener: (context, state) {
        if (state.currentUserType == UserType.tynt) {
          animationController.forward();
          return;
        }
        if (state.currentUserType == UserType.normal) {
          animationController.reverse();
        }
      },
      child: GestureDetector(
        onDoubleTap: _action,
        onTapDown: (detail) {
          _action(details: detail);
        },
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxHeight: 30, maxWidth: 54, minHeight: 30),
          child: Container(
            padding: const EdgeInsets.all(3),
            width: widget.width,
            decoration: BoxDecoration(
              color: animationController.isCompleted
                  ? Theme.of(context).brightness == Brightness.dark
                      ? AppColors.tyntSwitchOnColor
                      : AppColors.primary
                  : AppColors.softPeach,
              borderRadius: BorderRadius.circular(50),
              boxShadow: [
                BoxShadow(
                  color: AppColors.black.withOpacity2(0.010),
                  offset: const Offset(0, 6),
                  blurRadius: 8,
                  spreadRadius: 3,
                ),
              ],
            ),
            child: Stack(
              children: <Widget>[
                Opacity(
                  opacity: (1 - animationValue).clamp(0.0, 1.0),
                  child: Container(
                    constraints: const BoxConstraints(maxHeight: 26, maxWidth: 54),
                    decoration: BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                          offset: const Offset(-1, -1),
                          blurRadius: 20,
                          color: AppColors.black.withOpacity2(0.05),
                        ),
                        BoxShadow(
                          offset: const Offset(1, 1),
                          blurRadius: 20,
                          color: AppColors.black.withOpacity2(0.05),
                        ),
                      ],
                    ),
                  ),
                ),
                Transform.translate(
                  offset: isRTL(context)
                      ? Offset((-widget.width + 28) * animationValue, 0)
                      : Offset((widget.width - 28) * animationValue, 0),
                  child: Transform.rotate(
                    angle: 0,
                    child: Container(
                      height: 22,
                      width: 22,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppColors.white,
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.black.withOpacity2(0.25),
                            blurRadius: 6,
                            offset: const Offset(2, 1),
                          ),
                        ],
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
                      child: Stack(
                        children: <Widget>[
                          Center(
                            child: Opacity(
                              opacity: (1 - animationValue).clamp(0.0, 1.0),
                              child: const AppImageAsset(
                                AppAssets.tIcon,
                              ),
                            ),
                          ),
                          Center(
                            child: Opacity(
                              opacity: animationValue.clamp(0.0, 1.0),
                              child: const AppImageAsset(
                                AppAssets.tIcon,
                                color: AppColors.primary,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _action({TapDownDetails? details}) {
    if (isChaningUser.value) return;
    if (animationController.isAnimating) return;
    _determine(details: details);
  }

  Future<void> _determine({TapDownDetails? details}) async {
    isChaningUser.value = true;

    final isPseudoUser = context.read<AuthenticationBloc>().state.currentUser.isPseudoUser;
    if (isPseudoUser) {
      // await animationController.reverse();
      await getIt<ILocalStorageRepository>().removeTyntUserToken();
      _changeUserTheme(details);
      isChaningUser.value = false;
      isTyntUser.value = false;
      return;
    }

    final tyntUser = context.read<AuthenticationBloc>().state.user.pseudoUser;
    final isTyntUserNotCreated = tyntUser?.isPseudoUserNotSetuped ?? true;

    if (isTyntUserNotCreated) {
      final isTyntUserSetuped = await DailogBoxes.createPseudoProfileBottomSheet(context, isFromBottomNavigation: true);

      if (isTyntUserSetuped ?? false) {
        await verifyPasscode(details);
      }
      isChaningUser.value = false;

      return;
    }

    await verifyPasscode(details);
    isChaningUser.value = false;
  }

  Future<void> verifyPasscode(TapDownDetails? details) async {
    if (mounted) {
      final isVerified = await DailogBoxes.verifyPasscode(context);

      if (isVerified ?? false) {
        // await animationController.forward();
        _changeUserTheme(details);
        isTyntUser.value = true;
      }
    }
  }

  void _changeUserTheme(TapDownDetails? details) {
    widget.onTap.call(details ?? TapDownDetails());
    // Future.delayed(const Duration(milliseconds: 590), () => widget.onTap.call(TapDownDetails()));
  }
}

bool isRTL(BuildContext context) {
  return Bidi.isRtlLanguage(Localizations.localeOf(context).languageCode);
}
