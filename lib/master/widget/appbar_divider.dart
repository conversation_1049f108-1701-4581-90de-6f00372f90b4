import 'package:flutter/material.dart';
import 'package:tynt_web/constants/app_colors.dart';

class AppbarDivider extends StatelessWidget implements PreferredSizeWidget {
  const AppbarDivider({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 1,
      color: AppColors.appbarBorder,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(1);
}
