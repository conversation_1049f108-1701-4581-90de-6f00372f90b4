// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_builder/responsive_builder.dart';
import 'package:tynt_web/auth/bloc/authentication_bloc.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/master/widget/desktop_appbar.dart';
import 'package:tynt_web/master/widget/mobile_appbar.dart';
import 'package:tynt_web/routes/app_routes.dart';
import 'package:tynt_web/routes/go_routers.dart';
import 'package:tynt_web/widgets/common_button_widget.dart';

class NestedNavigationPage extends StatefulWidget {
  const NestedNavigationPage({
    required this.navigationShell,
    super.key,
  });
  final StatefulNavigationShell navigationShell;

  @override
  State<NestedNavigationPage> createState() => _NestedNavigationPageState();
}

class _NestedNavigationPageState extends State<NestedNavigationPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      appBar: getValueForScreenType<PreferredSizeWidget?>(
        context: context,
        mobile: MobileAppbar(
          onMenuTap: () {
            _scaffoldKey.currentState?.openDrawer();
          },
        ),
        tablet: const DesktopAppbar(),
        desktop: const DesktopAppbar(),
      ),
      body: getValueForScreenType<Widget>(
        context: context,
        mobile: Stack(
          children: [
            widget.navigationShell,
            Positioned(
              bottom: 10,
              right: 10,
              child: BlocSelector<AuthenticationBloc, AuthenticationState, bool>(
                selector: (state) => state.user.id != 0,
                builder: (context, isLoggedIn) {
                  return Visibility(
                    visible: isLoggedIn,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (goRouter.location != AppRoutes.createPost.route)
                          CommonButton(
                            buttonText: context.l10n.createPost,
                            onTap: () {
                              if (goRouter.location == AppRoutes.createPost.route) return;
                              context.pushNamed(AppRoutes.createPost.name);
                            },
                          ),
                      ],
                    ),
                  );
                },
              ),
            )
          ],
        ),
        tablet: widget.navigationShell,
        desktop: widget.navigationShell,
      ),
    );
  }
}
