import 'package:equatable/equatable.dart';
import 'package:tynt_web/post_preview/model/post_model.dart';

class PostDetailResponse extends Equatable {
  const PostDetailResponse({
    this.post,
    this.message = '',
    this.status = '0',
  });

  factory PostDetailResponse.fromJson(Map<String, dynamic> json) {
    return PostDetailResponse(
      post: json['data'] != null ? PostModel.fromJson(json['data'] as Map<String, dynamic>) : null,
      message: json['message'] as String? ?? '',
      status: json['status'] as String? ?? '0',
    );
  }
  final PostModel? post;
  final String message;
  final String status;

  Map<String, dynamic> toJson() {
    return {
      'data': post?.toJson(),
      'message': message,
      'status': status,
    };
  }

  @override
  List<Object?> get props => [post, message, status];

  @override
  String toString() {
    return 'PostDetailResponse{post: $post, message: $message, status: $status}';
  }
}
