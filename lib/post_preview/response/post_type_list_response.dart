import 'package:equatable/equatable.dart';
import 'package:tynt_web/post_preview/model/post_type_model.dart';

class PostTypeListResponse extends Equatable {
  const PostTypeListResponse({
    this.data = const [],
    this.message = '',
    this.status = '0',
  });

  factory PostTypeListResponse.fromJson(Map<String, dynamic> json) {
    return PostTypeListResponse(
      data: (json['data'] as List<dynamic>?)?.map((e) => PostTypeModel.fromJson(e as Map<String, dynamic>)).toList() ??
          [],
      message: json['message'] as String? ?? '',
      status: json['status'] as String? ?? '0',
    );
  }
  final List<PostTypeModel> data;
  final String message;
  final String status;

  Map<String, dynamic> toJson() {
    return {
      'data': data.map((e) => e.toJson()).toList(),
      'message': message,
      'status': status,
    };
  }

  @override
  List<Object?> get props => [data, message, status];

  @override
  String toString() {
    return 'PostTypeListResponse{data: $data, message: $message, status: $status}';
  }
}
