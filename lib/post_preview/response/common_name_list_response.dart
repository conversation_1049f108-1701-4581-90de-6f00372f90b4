import 'package:equatable/equatable.dart';
import 'package:tynt_web/post_preview/model/comman_name_model.dart';

class CommonNameListResponse extends Equatable {
  const CommonNameListResponse({
    this.data = const [],
    this.message = '',
    this.status = '0',
  });

  factory CommonNameListResponse.fromJson(Map<String, dynamic> json) {
    return CommonNameListResponse(
      data:
          (json['data'] as List<dynamic>?)?.map((e) => CommonNameModel.fromJson(e as Map<String, dynamic>)).toList() ??
              [],
      message: json['message'] as String? ?? '',
      status: json['status'] as String? ?? '0',
    );
  }
  final List<CommonNameModel> data;
  final String message;
  final String status;

  Map<String, dynamic> toJson() {
    return {
      'data': data.map((e) => e.toJson()).toList(),
      'message': message,
      'status': status,
    };
  }

  @override
  List<Object?> get props => [data, message, status];

  @override
  String toString() {
    return 'CommonNameListResponse{data: $data, message: $message, status: $status}';
  }
}
