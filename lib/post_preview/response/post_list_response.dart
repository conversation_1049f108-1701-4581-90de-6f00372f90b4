import 'package:equatable/equatable.dart';
import 'package:tynt_web/post_preview/model/post_model.dart';

class PostListResponse extends Equatable {
  const PostListResponse({
    this.posts = const [],
    this.message = '',
    this.status = '0',
  });

  factory PostListResponse.fromJson(Map<String, dynamic> json) {
    return PostListResponse(
      posts: (json['data'] as List<dynamic>?)?.map((e) => PostModel.fromJson(e as Map<String, dynamic>)).toList() ?? [],
      message: json['message'] as String? ?? '',
      status: json['status'] as String? ?? '0',
    );
  }
  final List<PostModel> posts;
  final String message;
  final String status;

  Map<String, dynamic> toJson() {
    return {
      'data': posts.map((e) => e.toJson()).toList(),
      'message': message,
      'status': status,
    };
  }

  @override
  List<Object?> get props => [posts, message, status];

  @override
  String toString() {
    return 'PostListResponse{posts: $posts, message: $message, status: $status}';
  }
}
