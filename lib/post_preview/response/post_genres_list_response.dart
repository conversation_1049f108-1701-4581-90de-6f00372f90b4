import 'package:equatable/equatable.dart';
import 'package:tynt_web/post_preview/model/post_genres_model.dart';

class PostGenresListResponse extends Equatable {
  const PostGenresListResponse({
    this.data = const [],
    this.message = '',
    this.status = '0',
  });

  factory PostGenresListResponse.fromJson(Map<String, dynamic> json) {
    return PostGenresListResponse(
      data:
          (json['data'] as List<dynamic>?)?.map((e) => GenresModel.fromJson(e as Map<String, dynamic>)).toList() ?? [],
      message: json['message'] as String? ?? '',
      status: json['status'] as String? ?? '0',
    );
  }
  final List<GenresModel> data;
  final String message;
  final String status;

  Map<String, dynamic> toJson() {
    return {
      'data': data.map((e) => e.toJson()).toList(),
      'message': message,
      'status': status,
    };
  }

  @override
  List<Object?> get props => [data, message, status];

  @override
  String toString() {
    return 'PostGenresListResponse{data: $data, message: $message, status: $status}';
  }
}
