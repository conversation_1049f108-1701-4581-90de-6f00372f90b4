import 'package:equatable/equatable.dart';

class PostTypeModel extends Equatable {
  const PostTypeModel({
    required this.id,
    this.name,
    this.isTitleRequired = 0,
    this.shouldMentionMovie = 0,
  });

  factory PostTypeModel.fromJson(Map<String, dynamic> json) {
    return PostTypeModel(
      id: json['id'] as int,
      name: json['name'] as String?,
      isTitleRequired: (json['is_title_required'] ?? 0) as int,
      shouldMentionMovie: (json['should_mention_movie'] ?? 0) as int,
    );
  }
  final int id;
  final String? name;
  final int isTitleRequired;
  final int shouldMentionMovie;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'is_title_required': isTitleRequired,
      'should_mention_movie': shouldMentionMovie,
    };
  }

  @override
  List<Object?> get props => [id, name, isTitleRequired, shouldMentionMovie];

  bool get isTitleRequiredValue => isTitleRequired == 1;

  bool get shouldMentionMovieValue => shouldMentionMovie == 1;

  @override
  String toString() {
    return 'PostTypeModel{id: $id, name: $name, isTitleRequired: $isTitleRequired, shouldMentionMovie: $shouldMentionMovie}';
  }
}
