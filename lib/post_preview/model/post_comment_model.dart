import 'package:equatable/equatable.dart';
import 'package:tynt_web/auth/models/user_model.dart';
import 'package:tynt_web/utlity/utlity.dart';

class PostCommentModel extends Equatable {
  const PostCommentModel({
    required this.id,
    required this.postId,
    required this.userId,
    this.user,
    this.repliedTo,
    this.message,
    this.createdAt,
    this.updatedAt,
    this.replies = const [],
  });

  factory PostCommentModel.fromJson(Map<String, dynamic> json) {
    return PostCommentModel(
      id: json['id'] as int,
      postId: json['post_id'] as int,
      userId: json['user_id'] as int? ?? 0,
      user: json['user'] == null ? null : UserModel.fromJson(json['user'] as Map<String, dynamic>),
      repliedTo: json['replied_to'] as int?,
      message: json['message'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      replies: (json['replies'] as List<dynamic>? ?? [])
          .map((e) => PostCommentModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
  final int id;
  final int postId;
  final int userId;
  final UserModel? user;
  final int? repliedTo;
  final String? message;
  final String? createdAt;
  final String? updatedAt;
  final List<PostCommentModel> replies;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'post_id': postId,
      'user_id': userId,
      'user': user?.toJson(),
      'replied_to': repliedTo,
      'message': message,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'replies': replies.map((e) => e.toJson()).toList(),
    };
  }

  DateTime get createdAtDate => DateTime.tryParse(createdAt ?? '') ?? DateTime.now();

  String get formmtedCreatedAt => Utility.getTimeAgo(createdAtDate);

  // Custom copy method (since no freezed)
  PostCommentModel copyWith({
    int? id,
    int? postId,
    int? userId,
    UserModel? user,
    int? repliedTo,
    String? message,
    String? createdAt,
    String? updatedAt,
    List<PostCommentModel>? replies,
  }) {
    return PostCommentModel(
      id: id ?? this.id,
      postId: postId ?? this.postId,
      userId: userId ?? this.userId,
      user: user ?? this.user,
      repliedTo: repliedTo ?? this.repliedTo,
      message: message ?? this.message,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      replies: replies ?? this.replies,
    );
  }

  @override
  List<Object?> get props => [
        id,
        postId,
        userId,
        user,
        repliedTo,
        message,
        createdAt,
        updatedAt,
        replies,
      ];

  @override
  String toString() {
    return 'PostCommentModel{id: $id, postId: $postId, userId: $userId, user: $user, repliedTo: $repliedTo, message: $message, createdAt: $createdAt, updatedAt: $updatedAt, replies: $replies}';
  }
}

extension ListPostCommentExetntion on List<PostCommentModel> {
  List<PostCommentModel> get filteredCommentsForPostList {
    final kComments = [...this];
    if (kComments.isEmpty) return <PostCommentModel>[];
    final firstComment = kComments[0];
    final isFirstCommentRepliesExists = firstComment.replies.isNotEmpty;

    if (isFirstCommentRepliesExists) {
      final isRelipesMorthan2 = firstComment.replies.length >= 2;
      return [
        firstComment.copyWith(replies: [...firstComment.replies].take(isRelipesMorthan2 ? 2 : 1).toList()),
      ];
    }
    if (kComments.length >= 2) {
      final isMorethan3 = kComments.length >= 3;
      return [...kComments].take(isMorethan3 ? 3 : 2).map((e) => e.copyWith(replies: <PostCommentModel>[])).toList();
    }
    return [firstComment];
  }
}
