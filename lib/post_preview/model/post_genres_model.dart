import 'package:equatable/equatable.dart';
import 'package:tynt_web/constants/app_strings.dart';

class GenresModel extends Equatable {
  const GenresModel({
    required this.id,
    this.iconPath,
    this.name,
  });

  factory GenresModel.fromJson(Map<String, dynamic> json) {
    return GenresModel(
      id: json['id'] as int,
      iconPath: json['icon_path'] as String?,
      name: json['name'] as String?,
    );
  }
  final int id;
  final String? iconPath;
  final String? name;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'icon_path': iconPath,
      'name': name,
    };
  }

  @override
  List<Object?> get props => [id, iconPath, name];

  String? get iconUrl => iconPath == null ? null : AppStrings.getMediaFileUrl(iconPath!);

  @override
  String toString() {
    return 'GenresModel{id: $id, iconPath: $iconPath, name: $name}';
  }
}

// Example usage: Fake genres list
List<GenresModel> get fakeGenersList => List.generate(
      10,
      (index) => const GenresModel(id: 0, iconPath: '', name: 'Genres'),
    );
