import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:tynt_web/constants/app_strings.dart';
import 'package:tynt_web/extentions/string_extentions.dart';
import 'package:tynt_web/post_preview/model/post_genres_model.dart';
import 'package:tynt_web/utlity/utlity.dart';

class CommunityModel extends Equatable {
  const CommunityModel({
    required this.id,
    this.userId = 0,
    this.name,
    this.description,
    this.imagePath,
    this.membersCount = 0,
    this.genre,
    this.isMember = false,
    this.createdAt,
    this.updatedAt,
  });

  factory CommunityModel.fromJson(Map<String, dynamic> json) {
    return CommunityModel(
      id: json['id'] as int,
      userId: (json['user_id'] ?? 0) as int,
      name: json['name'] as String?,
      description: json['description'] as String?,
      imagePath: json['image_path'] as String?,
      membersCount: (json['members_count'] ?? 0) as int,
      genre: json['genre'] != null ? GenresModel.fromJson(json['genre'] as Map<String, dynamic>) : null,
      isMember: (json['is_member'] ?? false) as bool,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );
  }
  final int id;
  final int userId;
  final String? name;
  final String? description;
  final String? imagePath;
  final int membersCount;
  final GenresModel? genre;
  final bool isMember;
  final String? createdAt;
  final String? updatedAt;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,
      'description': description,
      'image_path': imagePath,
      'members_count': membersCount,
      'genre': genre?.toJson(),
      'is_member': isMember,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  List<Object?> get props =>
      [id, userId, name, description, imagePath, membersCount, genre, isMember, createdAt, updatedAt];

  // ------------------ Computed Properties ------------------

  DateTime get createAtDateTime => DateTime.tryParse(createdAt ?? '') ?? DateTime.now();

  String get formmtedCreatedAt => DateFormat('MMM dd, yyyy').format(createAtDateTime.toLocal());

  String? get imageUrl => imagePath == null ? null : '${AppStrings.storageUrl}/${imagePath!}';

  String formmtedName(BuildContext context) => '${(name ?? '').inCaps} ${'tribe'}';

  bool get isDescriptionTooLong => (description ?? '').length > 100;

  String formmtedMemebersCount(BuildContext context) =>
      '${Utility.formatNumber(membersCount)} ${membersCount == 1 ? 'member' : 'members'}';

  @override
  String toString() {
    return 'CommunityModel{id: $id, userId: $userId, name: $name, description: $description, imagePath: $imagePath, membersCount: $membersCount, genre: $genre, isMember: $isMember, createdAt: $createdAt, updatedAt: $updatedAt}';
  }
}

// Default instance (same as before)
CommunityModel defaultCommunityModel = const CommunityModel(
  id: 0,
  name: 'Developer Community',
  description: AppStrings.sampleLongText,
);
