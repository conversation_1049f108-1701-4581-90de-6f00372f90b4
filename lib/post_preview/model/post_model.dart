// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:flutter_quill/quill_delta.dart';
import 'package:intl/intl.dart';
import 'package:tynt_web/auth/models/user_model.dart';
import 'package:tynt_web/constants/app_strings.dart';
import 'package:tynt_web/post_preview/model/comman_name_model.dart';
import 'package:tynt_web/post_preview/model/community_model.dart';
import 'package:tynt_web/post_preview/model/post_comment_model.dart';
import 'package:tynt_web/post_preview/model/post_genres_model.dart';
import 'package:tynt_web/post_preview/model/post_type_model.dart';
import 'package:tynt_web/tomes/model/tomes_model.dart';
import 'package:vsc_quill_delta_to_html/vsc_quill_delta_to_html.dart';

class PostModel extends Equatable {
  const PostModel({
    required this.id,
    this.title,
    this.descriptionDelta,
    this.imagePath,
    this.isPseudo,
    this.isDraft,
    this.isCommentOn = 0,
    this.isSaved = 0,
    this.isNominated = false,
    this.user,
    this.postType,
    this.genre,
    this.tags = const [],
    this.renderAs,
    this.community,
    this.createdAt,
    this.updatedAt,
    this.comments = const [],
    this.tomes = const [],
  });

  factory PostModel.fromJson(Map<String, dynamic> json) {
    return PostModel(
      id: json['id'] as int,
      title: json['title'] as String?,
      descriptionDelta: json['description'] as String?,
      imagePath: json['image_path'] as String?,
      isPseudo: json['is_pseudo'] as int?,
      isDraft: json['is_draft'] as int?,
      isCommentOn: (json['is_comment_on'] ?? 0) as int,
      isSaved: (json['is_saved'] ?? 0) as int,
      isNominated: (json['is_nominated'] ?? false) as bool,
      user: json['user'] != null ? UserModel.fromJson(json['user'] as Map<String, dynamic>) : null,
      postType: json['type'] != null ? PostTypeModel.fromJson(json['type'] as Map<String, dynamic>) : null,
      genre: json['genre'] != null ? GenresModel.fromJson(json['genre'] as Map<String, dynamic>) : null,
      tags: (json['tags'] as List<dynamic>? ?? [])
          .map((e) => CommonNameModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      renderAs: json['render_as'] as String?,
      community: json['community'] != null ? CommunityModel.fromJson(json['community'] as Map<String, dynamic>) : null,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      comments: (json['comments'] as List<dynamic>? ?? [])
          .map((e) => PostCommentModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      tomes:
          (json['tomes'] as List<dynamic>? ?? []).map((e) => TomesModel.fromJson(e as Map<String, dynamic>)).toList(),
    );
  }
  final int id;
  final String? title;
  final String? descriptionDelta;
  final String? imagePath;
  final int? isPseudo;
  final int? isDraft;
  final int isCommentOn;
  final int isSaved;
  final bool isNominated;
  final UserModel? user;
  final PostTypeModel? postType;
  final GenresModel? genre;
  final List<CommonNameModel> tags;
  final String? renderAs;
  final CommunityModel? community;
  final String? createdAt;
  final String? updatedAt;
  final List<PostCommentModel> comments;
  final List<TomesModel> tomes;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': descriptionDelta,
      'image_path': imagePath,
      'is_pseudo': isPseudo,
      'is_draft': isDraft,
      'is_comment_on': isCommentOn,
      'is_saved': isSaved,
      'is_nominated': isNominated,
      'user': user?.toJson(),
      'type': postType?.toJson(),
      'genre': genre?.toJson(),
      'tags': tags.map((e) => e.toJson()).toList(),
      'render_as': renderAs,
      'community': community?.toJson(),
      'created_at': createdAt,
      'updated_at': updatedAt,
      'comments': comments.map((e) => e.toJson()).toList(),
      'tomes': tomes.map((e) => e.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        title,
        descriptionDelta,
        imagePath,
        isPseudo,
        isDraft,
        isCommentOn,
        isSaved,
        isNominated,
        user,
        postType,
        genre,
        tags,
        renderAs,
        community,
        createdAt,
        updatedAt,
        comments,
        tomes,
      ];

  // ------------------ Computed Getters ------------------

  DateTime get createAtDateTime => DateTime.tryParse(createdAt ?? '') ?? DateTime.now();

  String get formmtedCreatedAt => DateFormat('MMM dd, yyyy').format(createAtDateTime.toLocal());

  String get description {
    try {
      if (descriptionDelta == null || descriptionDelta!.trim().isEmpty) return '';
      final parsed = jsonDecode(descriptionDelta!);
      if (parsed is! List || parsed.isEmpty) return '';
      final delta = Delta.fromJson(parsed);
      final converter = QuillDeltaToHtmlConverter(delta.toJson());
      return converter.convert();
    } catch (e) {
      return descriptionDelta ?? '';
    }
  }

  String? get imageUrl => imagePath == null ? null : '${AppStrings.storageUrl}/${imagePath!}';

  bool get isCommunityPost => community != null;

  bool get isTyntYPost => renderAs == AppStrings.tyntY;

  bool get showComments => isCommentOn == 1;

  List<PostCommentModel> get filteredComments => [...comments].filteredCommentsForPostList;

  bool get isShortPost {
    if (imagePath != null) return false;
    return (title ?? '').length + (description ?? '').length < 100;
  }

  bool get isDescriptionTooLong => (description ?? '').length > 300;

  bool isOtherUserPost(int userId) => user?.id != userId;

  @override
  String toString() {
    return 'PostModel{id: $id, title: $title, descriptionDelta: $descriptionDelta, imagePath: $imagePath, isPseudo: $isPseudo, isDraft: $isDraft, isCommentOn: $isCommentOn, isSaved: $isSaved, isNominated: $isNominated, user: $user, postType: $postType, genre: $genre, tags: $tags, renderAs: $renderAs, community: $community, createdAt: $createdAt, updatedAt: $updatedAt, comments: $comments, tomes: $tomes}';
  }

  PostModel copyWith({
    int? id,
    String? title,
    String? descriptionDelta,
    String? imagePath,
    int? isPseudo,
    int? isDraft,
    int? isCommentOn,
    int? isSaved,
    bool? isNominated,
    UserModel? user,
    PostTypeModel? postType,
    GenresModel? genre,
    List<CommonNameModel>? tags,
    String? renderAs,
    CommunityModel? community,
    String? createdAt,
    String? updatedAt,
    List<PostCommentModel>? comments,
    List<TomesModel>? tomes,
  }) {
    return PostModel(
      id: id ?? this.id,
      title: title ?? this.title,
      descriptionDelta: descriptionDelta ?? this.descriptionDelta,
      imagePath: imagePath ?? this.imagePath,
      isPseudo: isPseudo ?? this.isPseudo,
      isDraft: isDraft ?? this.isDraft,
      isCommentOn: isCommentOn ?? this.isCommentOn,
      isSaved: isSaved ?? this.isSaved,
      isNominated: isNominated ?? this.isNominated,
      user: user ?? this.user,
      postType: postType ?? this.postType,
      genre: genre ?? this.genre,
      tags: tags ?? this.tags,
      renderAs: renderAs ?? this.renderAs,
      community: community ?? this.community,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      comments: comments ?? this.comments,
      tomes: tomes ?? this.tomes,
    );
  }
}
