import 'package:equatable/equatable.dart';

class CommonNameModel extends Equatable {
  const CommonNameModel({
    required this.id,
    this.name,
  });

  factory CommonNameModel.fromJson(Map<String, dynamic> json) {
    return CommonNameModel(
      id: json['id'] as int,
      name: json['name'] as String?,
    );
  }
  final int id;
  final String? name;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }

  @override
  List<Object?> get props => [id, name];

  @override
  String toString() {
    return 'CommonNameModel{id: $id, name: $name}';
  }
}
