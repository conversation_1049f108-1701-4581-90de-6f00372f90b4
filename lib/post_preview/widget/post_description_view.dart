// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/post_preview/model/post_type_model.dart';

class PostDescriptionView extends StatelessWidget {
  const PostDescriptionView({
    required this.text,
    required this.postType,
    super.key,
    this.fontSize = 16,
    this.isForPostDetail = false,
    this.textColor,
    this.isDescriptionTooLong = false,
  });
  final String text;
  final double fontSize;
  final bool isForPostDetail;
  final PostTypeModel postType;
  final Color? textColor;
  final bool isDescriptionTooLong;

  @override
  Widget build(BuildContext context) {
    return Skeleton.replace(
      replacement: Text(
        text,
        style: context.textTheme.bodyLarge?.copyWith(
          color: textColor ?? AppColors.subText,
          fontSize: fontSize,
          height: isForPostDetail ? 1.7 : 1.5,
          fontWeight: FontWeight.w400,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DescriptionWrapper(
            hideDscription: !isForPostDetail && isDescriptionTooLong,
            child: HtmlWidget(
              text,
              textStyle: context.textTheme.bodyMedium?.copyWith(
                fontSize: fontSize,
                // color: context.textTheme.labelLarge?.color,
                overflow: isForPostDetail ? null : (isDescriptionTooLong ? TextOverflow.ellipsis : null),
                height: isForPostDetail ? 1.7 : 1.5,
                fontWeight: FontWeight.w400,
              ),
              customWidgetBuilder: (element) {
                if (element.localName == 'p') {
                  final text = element.text;
                  return RichText(text: _buildTappableSpans(text, context));
                }
                return null;
              },
            ),
          ),
          if (!isForPostDetail && isDescriptionTooLong)
            Padding(
              padding: const EdgeInsets.only(top: 10),
              child: Text(
                context.l10n.tapToReadMore,
                style: context.textTheme.bodyLarge?.copyWith(
                  color: AppColors.highlights2,
                  fontSize: fontSize,
                  height: isForPostDetail ? 1.7 : 1.5,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
        ],
      ),
    );
  }

  TextSpan _buildTappableSpans(String text, BuildContext context) {
    final regex = RegExp(r'(@\w+|#\w+)', multiLine: true);
    final matches = regex.allMatches(text);
    final spans = <InlineSpan>[];
    var lastIndex = 0;

    for (final match in matches) {
      if (match.start > lastIndex) {
        spans.add(TextSpan(text: text.substring(lastIndex, match.start)));
      }

      final matchedText = match.group(0)!;
      spans.add(
        TextSpan(
          text: matchedText,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.blue,
                fontSize: fontSize,
                height: isForPostDetail ? 1.7 : 1.5,
                fontWeight: FontWeight.w400,
              ),
          recognizer: TapGestureRecognizer()
            ..onTap = () {
              // if (matchedText.startsWith('@')) {
              //   if (!postType.shouldMentionMovieValue) {
              //     context.navigateUserProfile(context, null, userName: matchedText.replaceFirst('@', ''));
              //   }
              // } else {
              //   log('Tapped on hashtag: $matchedText');
              // }
            },
        ),
      );

      lastIndex = match.end;
    }

    if (lastIndex < text.length) {
      spans.add(TextSpan(text: text.substring(lastIndex)));
    }

    return TextSpan(children: spans, style: const TextStyle(color: Colors.black));
  }
}

class DescriptionWrapper extends StatelessWidget {
  const DescriptionWrapper({required this.child, super.key, this.hideDscription = false});
  final bool hideDscription;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return hideDscription
        ? SizedBox(
            height: 100,
            child: Stack(
              children: [
                child,
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    height: 30,
                    decoration: BoxDecoration(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      gradient: LinearGradient(
                        begin: Alignment.bottomCenter,
                        end: Alignment.topCenter,
                        stops: const [0.2, 1],
                        colors: [
                          Theme.of(context).scaffoldBackgroundColor,
                          Theme.of(context).scaffoldBackgroundColor.withOpacity(0),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          )
        : child;
  }
}
