// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:tynt_web/constants/app_theme.dart';
import 'package:tynt_web/extentions/context_extentions.dart';

class PostTitleView extends StatelessWidget {
  const PostTitleView({
    required this.text,
    this.fontSize = 24,
    this.isTyntYPost = false,
    super.key,
  });

  final String text;
  final double fontSize;
  final bool isTyntYPost;

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: TextSpan(
        children: [
          if (isTyntYPost)
            TextSpan(
              text: 'Y ',
              style: context.textTheme.headlineSmall?.copyWith(
                fontSize: fontSize + 1,
                letterSpacing: 1,
                height: 1.3,
                fontWeight: FontWeight.w700,
              ),
            ),
          TextSpan(
            text: text,
            style: context.textTheme.headlineSmall?.copyWith(
              fontSize: fontSize,
              letterSpacing: 1,
              height: 1.3,
              fontWeight: FontWeight.w400,
              fontFamily: AppTheme.manropeFontFamily,
            ),
          ),
        ],
      ),
    );
  }
}
