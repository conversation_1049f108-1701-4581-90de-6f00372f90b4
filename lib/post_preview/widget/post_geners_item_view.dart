// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';

class PostGenersItemView extends StatelessWidget {
  const PostGenersItemView({
    required this.text,
    super.key,
    this.fontSize = 12,
    this.textColor = AppColors.subText,
    this.backgroundColor,
    this.padding = const EdgeInsets.symmetric(vertical: 4, horizontal: 12),
    this.onPressed,
  });
  final String text;
  final double fontSize;
  final Color textColor;
  final Color? backgroundColor;
  final EdgeInsetsGeometry padding;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Skeleton.leaf(
        child: Container(
          padding: padding,
          decoration: const BoxDecoration(
              // color: backgroundColor ?? AppColors.background,
              // borderRadius: BorderRadius.circular(6),
              ),
          child: Text(
            text,
            style: context.textTheme.labelMedium?.copyWith(
              fontSize: fontSize,
              height: 1.2,
            ),
          ),
        ),
      ),
    );
  }
}
