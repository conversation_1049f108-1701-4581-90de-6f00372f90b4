// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:tynt_web/extentions/context_extentions.dart';

class PostTagItemView extends StatelessWidget {
  const PostTagItemView({
    required this.text,
    super.key,
    this.fontSize = 15,
  });
  final String text;
  final double fontSize;

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: context.textTheme.labelMedium?.copyWith(
        fontSize: fontSize,
        fontWeight: FontWeight.w400,
      ),
    );
  }
}
