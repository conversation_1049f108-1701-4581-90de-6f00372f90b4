// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';

class PostCommunityView extends StatelessWidget {
  const PostCommunityView({
    required this.text,
    required this.communityId,
    super.key,
    this.fontSize = 12,
    this.textColor,
    this.isFromCommunityPostPage = false,
  });

  final String text;
  final int communityId;
  final double fontSize;
  final Color? textColor;
  final bool isFromCommunityPostPage;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (isFromCommunityPostPage) return;
        // context.navigateCommunityDetailPage(context, communityId);
      },
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: <PERSON>umn(
        children: [
          Text(
            text,
            style: context.textTheme.labelMedium?.copyWith(
              fontSize: fontSize,
              letterSpacing: 1,
              fontWeight: FontWeight.w400,
            ),
          ),
          Gap(context.isMobile ? 6 : 10),
        ],
      ),
    );
  }
}
