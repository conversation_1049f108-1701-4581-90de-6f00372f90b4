// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/constants/app_constants.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/extentions/string_extentions.dart';
import 'package:tynt_web/post_preview/model/post_model.dart';
import 'package:tynt_web/post_preview/model/post_type_model.dart';
import 'package:tynt_web/post_preview/widget/post_community_view.dart';
import 'package:tynt_web/post_preview/widget/post_description_view.dart';
import 'package:tynt_web/post_preview/widget/post_geners_item_view.dart';
import 'package:tynt_web/post_preview/widget/post_header_view.dart';
import 'package:tynt_web/post_preview/widget/post_tag_item_view.dart';
import 'package:tynt_web/post_preview/widget/post_title_view.dart';
import 'package:tynt_web/widgets/post_image_view.dart';

class PostDetailView extends StatefulWidget {
  const PostDetailView({
    required this.postModel,
    super.key,
    this.isFromOtherUserPage = false,
  });
  final PostModel postModel;
  final bool isFromOtherUserPage;

  @override
  State<PostDetailView> createState() => _PostDetailViewState();
}

class _PostDetailViewState extends State<PostDetailView> {
  final FlutterTts flutterTts = FlutterTts();
  bool isPlaying = false;
  bool isPaused = false;

  Duration estimatedDuration = const Duration(seconds: 40); // adjust based on text length
  Duration elapsed = Duration.zero;
  Timer? progressTimer;

  Future _speak(String text) async {
    await flutterTts.speak(text);
    setState(() {
      isPlaying = true;
      isPaused = false;
      elapsed = Duration.zero;
      _startProgressTimer();
    });
  }

  Future _pauseReading() async {
    await flutterTts.pause();
    _stopProgressTimer();
    setState(() {
      isPlaying = false;
      isPaused = true;
    });
  }

  Future _resumeReading() async {
    await flutterTts.speak(widget.postModel.description ?? '');
    _startProgressTimer();
    setState(() {
      isPlaying = true;
      isPaused = false;
    });
  }

  Future _stopReading() async {
    await flutterTts.stop();
    _stopProgressTimer();
    setState(() {
      isPlaying = false;
      isPaused = false;
      elapsed = Duration.zero;
    });
  }

  void _startProgressTimer() {
    progressTimer?.cancel();
    progressTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (mounted) {
        setState(() {
          elapsed += const Duration(seconds: 1);
          if (elapsed >= estimatedDuration) {
            progressTimer?.cancel();
            isPlaying = false;
            isPaused = false;
          }
        });
      }
    });
  }

  void _stopProgressTimer() {
    progressTimer?.cancel();
  }

  @override
  void initState() {
    super.initState();

    flutterTts.setCompletionHandler(() {
      setState(() {
        isPlaying = false;
        isPaused = false;
      });
    });
    final text = widget.postModel.description ?? '';
    final wordCount = text.trim().split(RegExp(r'\s+')).length;
    const wordsPerMinute = 200;
    estimatedDuration = Duration(
      seconds: ((wordCount / wordsPerMinute) * 60).round(),
    );
  }

  @override
  void dispose() {
    flutterTts.stop();
    _stopProgressTimer();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(bottom: kBottomPadding, top: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.postModel.imageUrl != null) ...[
            // Gap(context.isMobile ? 8 : 10),
            PostImageView(
              borderRadius: 0,
              imageUrl: widget.postModel.imageUrl,
              width: MediaQuery.sizeOf(context).width,
              boxfit: BoxFit.fitWidth,
            ),
            Gap(context.isMobile ? 8 : 10),
          ] else ...[
            Gap(context.isMobile ? 32 : 32),
          ],
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Gap(16),
                if (widget.postModel.title?.isPureValid ?? false) ...[
                  // Gap(context.isMobile ? 8 : 10),
                  PostTitleView(
                    text: widget.postModel.title ?? '',
                    isTyntYPost: widget.postModel.isTyntYPost,
                    fontSize: context.isMobile ? 24 : 28,
                  ),
                ],
                Row(
                  children: [
                    Expanded(
                      child: Skeleton.leaf(
                        child: PostHeaderView(
                          fontSize: context.isMobile ? 14 : 18,
                          title: widget.postModel.postType?.name ?? '',
                          date: widget.postModel.formmtedCreatedAt,
                          onPostTypeTap: () {},
                        ),
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        if (widget.postModel.isDraft == 1)
                          Skeleton.leaf(
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
                              decoration: const BoxDecoration(
                                color: AppColors.background,
                                borderRadius: BorderRadius.all(Radius.circular(5)),
                              ),
                              child: Text(
                                'Draft',
                                style: context.textTheme.bodyMedium?.copyWith(
                                  fontSize: context.isMobile ? 12 : 16,
                                  color: AppColors.red,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                          ),
                        if (widget.postModel.genre != null)
                          PostGenersItemView(
                            text: widget.postModel.genre?.name ?? '',
                            onPressed: () {},
                          ),
                      ],
                    ),
                  ],
                ),
                Gap(context.isMobile ? 8 : 16),
                // Row(
                //   children: [
                //     IconButton(
                //       icon: Icon(
                //         isPlaying
                //             ? Icons.pause
                //             : isPaused
                //                 ? Icons.play_arrow
                //                 : Icons.play_arrow,
                //       ),
                //       iconSize: 48,
                //       onPressed: () {
                //         if (isPlaying) {
                //           _pauseReading();
                //         } else if (isPaused) {
                //           _resumeReading();
                //         } else {
                //           _speak(widget.postModel.description ?? '');
                //         }
                //       },
                //     ),
                //     Column(
                //       crossAxisAlignment: CrossAxisAlignment.start,
                //       children: [
                //         Slider(
                //           max: estimatedDuration.inSeconds.toDouble(),
                //           value: elapsed.inSeconds.clamp(0, estimatedDuration.inSeconds).toDouble(),
                //           onChanged: (_) {}, // disable manual seek, just visual
                //         ),
                //         Text(
                //           '${_formatDuration(elapsed)} / ${_formatDuration(estimatedDuration)}',
                //           style: const TextStyle(fontSize: 14, color: Colors.grey),
                //         ),
                //       ],
                //     ),
                //   ],
                // ),

                Gap(context.isMobile ? 8 : 25),
                PostDescriptionView(
                  isForPostDetail: true,
                  postType: widget.postModel.postType ?? const PostTypeModel(id: 0),
                  fontSize: context.isMobile ? 16 : 20,
                  text: widget.postModel.description ?? '',
                ),
                if (widget.postModel.community != null) ...[
                  PostCommunityView(
                    text: widget.postModel.community?.formmtedName(context) ?? '',
                    communityId: widget.postModel.community?.id ?? 0,
                    textColor: AppColors.secondary,
                  ),
                  const Gap(2),
                ],

                // if (postModel.imageUrl != null) ...[
                //   Gap(context.isMobile ? 8 : 10),
                //   PostImageView(
                //     imageUrl: postModel.imageUrl,
                //     width: MediaQuery.sizeOf(context).width,
                //     boxfit: BoxFit.fitWidth,
                //   ),
                //   Gap(context.isMobile ? 8 : 10),
                // ],
                // if (postModel.title.isPureValid) ...[
                //   Gap(context.isMobile ? 8 : 10),
                //   PostTitleView(
                //     text: postModel.title ?? '',
                //     isTyntYPost: postModel.isTyntYPost,
                //     fontSize: context.isMobile ? 24 : 28,
                //   ),
                // ],
                // Gap(context.isMobile ? 20 : 25),
                // PostDescriptionView(
                //   isForPostDetail: true,
                //   postType: postModel.postType ?? const PostTypeModel(id: 0),
                //   fontSize: context.isMobile ? 16 : 20,
                //   text: postModel.description ?? '',
                // ),
                Gap(context.isMobile ? 16 : 20),
                // BlocSelector<AuthenticationBloc, AuthenticationState, UserResponse?>(
                //   selector: (state) => state.userResponse,
                //   builder: (context, user) {
                //     final isPublicUser = user?.publicUser?.isPublicUser ?? user?.publicUser?.isBusinessUser ?? false;
                //     return UserListTile(
                //       isBusinessUser: postModel.user?.isBusinessUser ?? false,
                //       name: postModel.user?.formmtedUserName ?? '',
                //       fontSize: context.isMobile ? 15 : 20,
                //       isGhostUser: postModel.user?.isPseudoUser ?? false,
                //       url: postModel.user?.formmtedAvatarProfile ?? '',
                //       ghostIconSize: context.isMobile ? 18 : 22,
                //       iconRadius: context.isMobile ? 17 : 22,
                //       onFollowButtonPressed: () {
                //         if (postModel.user?.id == 0) return;
                //         context.read<PostDetailBloc>().add(PostDetailEvent.followToggle(postModel.user?.id ?? 0));
                //       },
                //       onProfileTap: () {
                //         if (isFromOtherUserPage) return;
                //         context.navigateUserProfile(context, postModel.user?.id);
                //       },
                //       isFollowButton: postModel.user?.isFollowing ?? false,
                //       showFollowButton: isPublicUser &&
                //           (user?.publicUserId != postModel.user?.id) &&
                //           (user?.pseudoUser?.id != postModel.user?.id),
                //     );
                //   },
                // ),
                if (widget.postModel.tags.isNotEmpty) ...[
                  Gap(context.isMobile ? 16 : 20),
                  Wrap(
                    runSpacing: context.isMobile ? 8 : 16,
                    spacing: context.isMobile ? 8 : 16,
                    children: List.generate(
                      widget.postModel.tags.length,
                      (index) => PostTagItemView(
                        text: '#${widget.postModel.tags[index].name}',
                        fontSize: context.isMobile ? 15 : 20,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration d) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(d.inMinutes.remainder(60));
    final seconds = twoDigits(d.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
