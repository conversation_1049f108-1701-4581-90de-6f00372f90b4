// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:tynt_web/extentions/context_extentions.dart';

class PostHeaderView extends StatelessWidget {
  const PostHeaderView({
    required this.title,
    required this.date,
    super.key,
    this.fontSize = 12,
    this.onPostTypeTap,
  });
  final String title;
  final String date;
  final double fontSize;
  final VoidCallback? onPostTypeTap;

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: title,
            style: context.textTheme.labelMedium?.copyWith(
              fontSize: fontSize,
              fontWeight: FontWeight.w400,
              color: Theme.of(context).primaryColor,
            ),
            recognizer: TapGestureRecognizer()..onTap = onPostTypeTap,
          ),
          if (title.trim().isNotEmpty && date.trim().isNotEmpty) ...[
            const WidgetSpan(child: SizedBox(width: 2)),
            WidgetSpan(
              alignment: PlaceholderAlignment.middle,
              child: Container(
                width: 3.5,
                height: 3.5,
                margin: const EdgeInsets.symmetric(horizontal: 2),
                decoration:
                    BoxDecoration(color: Theme.of(context).textTheme.labelMedium?.color, shape: BoxShape.circle),
              ),
            ),
          ],
          const WidgetSpan(child: SizedBox(width: 2)),
          TextSpan(
            text: date,
            style: context.textTheme.labelMedium?.copyWith(
              fontSize: fontSize,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}
