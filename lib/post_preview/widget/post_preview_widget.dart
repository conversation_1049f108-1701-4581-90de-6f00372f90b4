import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/post_preview/model/post_type_model.dart';
import 'package:tynt_web/post_preview/widget/post_description_view.dart';
import 'package:tynt_web/widgets/post_image_view.dart';

class PostPreviewWidget extends StatelessWidget {
  const PostPreviewWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: AppColors.white,
      ),
      child: Column(
        children: [
          Gap(context.isMobile ? 10 : 12),
          PostImageView(
            imageUrl:
                'https://plus.unsplash.com/premium_photo-1664474619075-644dd191935f?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8aW1hZ2V8ZW58MHx8MHx8fDA%3D',
            width: MediaQuery.sizeOf(context).width / 2.25,
            boxfit: BoxFit.fitWidth,
          ),
          Gap(context.isMobile ? 10 : 12),
          PostDescriptionView(
            fontSize: context.isMobile ? 16 : 18,
            text: 'postModel.description' ?? '',
            isDescriptionTooLong: true,
            postType: PostTypeModel(id: 0),
          ),
        ],
      ),
    );
  }
}
