import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_quill/quill_delta.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:tynt_web/app/cubit/refresh_cubit.dart';
import 'package:tynt_web/auth/models/app_file_data.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/create_post/cubit/create_edit_post_cubit.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/extentions/string_extentions.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/post_preview/model/comman_name_model.dart';
import 'package:tynt_web/post_preview/model/post_genres_model.dart';
import 'package:tynt_web/post_preview/model/post_type_model.dart';
import 'package:tynt_web/post_preview/widget/post_description_view.dart';
import 'package:tynt_web/post_preview/widget/post_geners_item_view.dart';
import 'package:tynt_web/post_preview/widget/post_header_view.dart';
import 'package:tynt_web/post_preview/widget/post_tag_item_view.dart';
import 'package:tynt_web/post_preview/widget/post_title_view.dart';
import 'package:tynt_web/primary_button/primary_button.dart';
import 'package:tynt_web/routes/app_routes.dart';
import 'package:tynt_web/utlity/tuples.dart';
import 'package:tynt_web/utlity/utlity.dart';
import 'package:tynt_web/widgets/custom_progress_indicator.dart';
import 'package:tynt_web/widgets/dailog_boxes.dart';
import 'package:tynt_web/widgets/post_image_view.dart';
import 'package:tynt_web/widgets/user_identifire.dart';

class PostPreviewPage extends StatefulWidget {
  const PostPreviewPage({super.key, this.id});
  final int? id;

  @override
  State<PostPreviewPage> createState() => _PostPreviewPageState();
}

class _PostPreviewPageState extends State<PostPreviewPage> {
  late final isForEdit = widget.id != null;
  final publishPostButtonController = PrimaryLoadingButtonController();
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: ConstrainedBox(
          constraints: const BoxConstraints(
            maxWidth: 716,
          ),
          child: Column(
            children: [
              AppBar(
                leadingWidth: 0,
                titleSpacing: 0,
                title: Text(
                  context.l10n.postPreview,
                  style: context.textTheme.bodyMedium?.copyWith(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
              Flexible(
                child: SingleChildScrollView(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        BlocSelector<CreateEditPostCubit, CreateEditPostState, AppFileData?>(
                          selector: (state) => state.postImage,
                          builder: (context, postImage) {
                            if (postImage == null) return const SizedBox.shrink();
                            return PostImageView(
                              boxfit: BoxFit.fitWidth,
                              width: MediaQuery.sizeOf(context).width,
                              imageUrl: postImage.networkUrl,
                              localFileBytes: postImage.imageBytes,
                              borderRadius: 0,
                            );
                          },
                        ),
                        Container(
                          padding: const EdgeInsets.all(20),
                          width: double.infinity,
                          constraints: const BoxConstraints(
                            maxWidth: 716,
                          ),
                          decoration: const BoxDecoration(
                            color: AppColors.white,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: BlocSelector<CreateEditPostCubit, CreateEditPostState, String>(
                                      selector: (state) => state.selectedPosType?.name ?? '',
                                      builder: (context, name) {
                                        return PostHeaderView(
                                          fontSize: context.isMobile ? 14 : 18,
                                          title: name,
                                          date: DateFormat('MMM dd, yyyy').format(DateTime.now()),
                                        );
                                      },
                                    ),
                                  ),
                                  BlocSelector<CreateEditPostCubit, CreateEditPostState, GenresModel?>(
                                    selector: (state) => state.selectedGenre,
                                    builder: (context, genres) {
                                      if (genres == null) return const SizedBox.shrink();
                                      return PostGenersItemView(
                                        text: genres.name ?? '',
                                      );
                                    },
                                  ),
                                ],
                              ),
                              const Gap(16),
                              BlocSelector<CreateEditPostCubit, CreateEditPostState, Tuple2<String, bool>>(
                                selector: (state) => tuple2(state.title ?? '', state.isForTyntY),
                                builder: (context, state) {
                                  if (state.value1.isNotPureValid) {
                                    return const SizedBox.shrink();
                                  }
                                  return Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      PostTitleView(
                                        text: state.value1,
                                        isTyntYPost: state.value2,
                                        fontSize: 32,
                                      ),
                                      const Gap(16),
                                    ],
                                  );
                                },
                              ),
                              BlocSelector<CreateEditPostCubit, CreateEditPostState, Tuple2<String, PostTypeModel>>(
                                selector: (state) => tuple2(
                                    state.description ?? '', state.selectedPosType ?? const PostTypeModel(id: 0)),
                                builder: (context, value) {
                                  var htmlDescription = '';
                                  final parsed = jsonDecode(value.value1);
                                  if (parsed is List) {
                                    final delta = Delta.fromJson(parsed);

                                    htmlDescription = Utility.convertDeltaToHtml(delta);
                                  }
                                  return UserIdentifer(
                                    builder: (_, isTyntUser) {
                                      return PostDescriptionView(
                                        isForPostDetail: true,
                                        text: htmlDescription,
                                        postType: value.value2,
                                        textColor: isTyntUser ? AppColors.secondary : AppColors.subText,
                                      );
                                    },
                                  );
                                },
                              ),
                              BlocSelector<CreateEditPostCubit, CreateEditPostState,
                                  Tuple2<List<CommonNameModel>, List<String>>>(
                                selector: (state) => tuple2(state.selctedHasTagList, state.newHasTagsList),
                                builder: (context, value) {
                                  final allHashTags = <String>{
                                    ...[...value.value1].map((e) => e.name ?? ''),
                                    ...value.value2,
                                  }.toList();
                                  if (allHashTags.isEmpty) return const SizedBox.shrink();
                                  return Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      const Gap(16),
                                      Wrap(
                                        runSpacing: 8,
                                        spacing: 8,
                                        children: List.generate(
                                          allHashTags.length,
                                          (index) => PostTagItemView(
                                            text: '#${allHashTags[index]}',
                                          ),
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                        const Gap(20),
                        Align(
                          alignment: context.isMobile ? Alignment.center : Alignment.centerRight,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              PrimaryButton(
                                width: 164,
                                onPressed: () {
                                  context.read<CreateEditPostCubit>().changeClosePreview();
                                },
                                text: context.l10n.cancel,
                                backgroundColor: AppColors.white,
                                textColor: AppColors.primary,
                                borderColor: AppColors.primary,
                              ),
                              const Gap(15),
                              PrimaryButton(
                                width: 164,
                                customLoadingWidget: const CustomProgressIndicator(),
                                primaryLoadingButtonController: publishPostButtonController,
                                onPressed: () async {
                                  final state = context.read<CreateEditPostCubit>().state;
                                  if (isForEdit) {
                                    publishPostButtonController.start();
                                    final failToUpdatePost = await context.read<CreateEditPostCubit>().savePost();
                                    if (failToUpdatePost != null) {
                                      publishPostButtonController.success();
                                      context.read<RefreshCubit>().refreshPostDetail(failToUpdatePost);
                                      await DailogBoxes.openPostSettingDailog(context, postId: widget.id!);
                                      if (context.canPop()) {
                                        context.pop();
                                      } else {
                                        context.goNamed(AppRoutes.profile.name);
                                      }
                                      return;
                                    }
                                    publishPostButtonController.error();
                                    return;
                                  }
                                  publishPostButtonController.start();
                                  final failOrCreatePost = await context.read<CreateEditPostCubit>().publishPost();
                                  if (failOrCreatePost) {
                                    publishPostButtonController.success();
                                    await DailogBoxes.openPostSettingDailog(context, postId: state.createPostId!);
                                    if (context.canPop()) {
                                      context.pop();
                                    } else {
                                      context.goNamed(AppRoutes.profile.name);
                                    }
                                    return;
                                  }
                                  publishPostButtonController.error();
                                },
                                text: isForEdit ? context.l10n.editPost : context.l10n.publishPost,
                              )
                            ],
                          ),
                        ),
                        const Gap(20)
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
