part of 'i_post_repository.dart';

@Injectable(as: IPostRepository)
class PostRepository extends IPostRepository {
  PostRepository(super.client);

  @override
  ApiResult<PostListResponse> getPostsList({
    int page = 1,
    int perPage = 10,
    int? userId,
    int? isDarft,
    int? isOwn,
    int? communityId,
    String? search,
    int? authenticatedId,
    List<int> genresIds = const <int>[],
    int? postTypeId,
    String? filter,
  }) async {
    final response = await client.get(
      url: AppStrings.posts,
      params: <String, String>{
        'page': '$page',
        'per_page': '$perPage',
        if (userId != null) 'user_id': '$userId',
        if (isDarft != null) 'is_draft': '$isDarft',
        if (isOwn != null) 'is_own': '$isOwn',
        if (search != null) 'search': search,
        if (authenticatedId != null) 'authenticate_id': '$authenticatedId',
        if (filter != null) 'filter': filter,
        if (communityId != null) 'community_id': '$communityId',
        if (postTypeId != null) 'post_type_id': '$postTypeId',
        if (genresIds.isNotEmpty)
          for (int i = 0; i < genresIds.length; i++) 'genre_ids[$i]': '${genresIds[i]}',
      },
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(
        () => PostListResponse.fromJson(r),
        (o, s) => HttpFailure.parsing(o.toString(), 500, s),
      ),
    );
  }

  @override
  ApiResult<TomesListResponse> getTomesList({String? search}) async {
    final response = await client.get(
      url: AppStrings.tomesList,
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(
        () {
          return TomesListResponse.fromJson(r);
        },
        (o, s) {
          return HttpFailure.parsing(o.toString(), 500, s);
        },
      ),
    );
  }

  @override
  ApiResult<PostDetailResponse> getPostDetail(int id) async {
    final response = await client.get(url: AppStrings.postDetail(id));

    return response.fold(
      left,
      (r) => Either.tryCatch(
        () => PostDetailResponse.fromJson(r),
        (o, s) => HttpFailure.parsing(o.toString(), 500, s),
      ),
    );
  }

  @override
  ApiResult<PostDetailResponse> createPost({
    required String description,
    int? postTypeId,
    String? title,
    List<int> tagsIds = const <int>[],
    int? genresId,
    List<String> newTags = const <String>[],
    bool isPseudo = false,
    bool isDraft = false,
    Uint8List? image,
    int? communityId,
    bool isTyntYPost = false,
    bool showComments = false,
    List<int> mentionUserIds = const <int>[],
    List<int> mentionMovieIds = const <int>[],
  }) async {
    final response = await client.multipart(
      url: AppStrings.createPost,
      webFiles: [
        if (image != null) MapEntry('image_path', image),
      ],
      requests: <String, String>{
        if (postTypeId != null) 'post_type_id': postTypeId.toString(),
        if (title.isPureValid) 'title': title!.trim(),
        'description': description.trim(),
        'is_pseudo': isPseudo ? '1' : '0',
        'is_draft': isDraft ? '1' : '0',
        'is_comment_on': showComments ? '1' : '0',
        'is_from_web': '1',
        if (isTyntYPost && communityId == null) 'render_as': AppStrings.tyntY,
        if (!isTyntYPost && communityId != null) 'render_as': AppStrings.community,
        if (!isTyntYPost && communityId != null) 'community_id': '$communityId',
        if (genresId != null) 'genre_id': '$genresId',
        if (tagsIds.isNotEmpty)
          for (var i = 0; i < tagsIds.length; i++) 'tag_ids[$i]': '${tagsIds[i]}',
        if (newTags.isNotEmpty)
          for (var i = 0; i < newTags.length; i++) 'tags[$i]': newTags[i],
        if (mentionUserIds.isNotEmpty)
          for (var i = 0; i < newTags.length; i++) 'user_ids[$i]': mentionUserIds[i].toString(),
        if (mentionMovieIds.isNotEmpty)
          for (var i = 0; i < newTags.length; i++) 'movie_ids[$i]': mentionMovieIds[i].toString(),
      },
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(
        () => PostDetailResponse.fromJson(r),
        (o, s) => HttpFailure.parsing(o.toString(), 500, s),
      ),
    );
  }

  @override
  ApiResult<PostDetailResponse> editPost({
    required int postId,
    String? title,
    String? description,
    int? postTypeId,
    List<int> tagsIds = const <int>[],
    List<int> genresIds = const <int>[],
    List<String> newTags = const <String>[],
    bool isDraft = false,
    int? genresId,
    String? deleteImage,
    Uint8List? image,
    int? communityId,
    bool showComments = false,
    List<int> mentionUserIds = const <int>[],
    List<int> mentionMovieIds = const <int>[],
    List<int> tomsIds = const <int>[],
  }) async {
    final response = await client.multipart(
      url: AppStrings.postEdit(postId),
      webFiles: [
        if (image != null) MapEntry('image_path', image),
      ],
      requests: <String, String>{
        if (postTypeId != null) 'post_type_id': postTypeId.toString(),
        if (title.isPureValid) 'title': title!.trim(),
        if (description.isPureValid) 'description': description!.trim(),
        'is_draft': isDraft ? '1' : '0',
        if (genresId != null) 'genre_id': '$genresId',
        if (deleteImage != null) 'delete_image': '1',
        if (communityId != null) 'community_id': '$communityId',
        'is_from_web': '1',
        'is_comment_on': showComments ? '1' : '0',
        if (tagsIds.isNotEmpty)
          for (var i = 0; i < tagsIds.length; i++) 'tag_ids[$i]': '${tagsIds[i]}',
        if (newTags.isNotEmpty)
          for (var i = 0; i < newTags.length; i++) 'tags[$i]': newTags[i],
        if (mentionUserIds.isNotEmpty)
          for (var i = 0; i < mentionUserIds.length; i++) 'user_ids[$i]': mentionUserIds[i].toString(),
        if (mentionMovieIds.isNotEmpty)
          for (var i = 0; i < mentionMovieIds.length; i++) 'movie_ids[$i]': mentionMovieIds[i].toString(),
        if (tomsIds.isNotEmpty)
          for (var i = 0; i < tomsIds.length; i++) 'tome_id[$i]': tomsIds[i].toString(),
      },
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(
        () => PostDetailResponse.fromJson(r),
        (o, s) => HttpFailure.parsing(o.toString(), 500, s),
      ),
    );
  }

  @override
  ApiResult<PostTypeListResponse> getPostTypeList({int page = 1, int perPage = 10, String? search}) async {
    final response = await client.get(
      url: AppStrings.postType,
      params: <String, String>{
        'page': '$page',
        'per_page': '$perPage',
        if (search.isPureValid) 'search': search!,
      },
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(
        () => PostTypeListResponse.fromJson(r),
        (o, s) => HttpFailure.parsing(o.toString(), 500, s),
      ),
    );
  }

  @override
  ApiResult<CommonResponse> deletePost(int postId) async {
    final response = await client.delete(url: AppStrings.postDelete(postId));

    return response.fold(
      left,
      (r) => Either.tryCatch(
        () => CommonResponse.fromJson(r),
        (o, s) => HttpFailure.parsing(o.toString(), 500, s),
      ),
    );
  }

  @override
  ApiResult<CommonNameListResponse> getMoviesList({String? search}) async {
    final response = await client.get(
      url: AppStrings.moviesList,
      params: <String, dynamic>{
        if (search != null) 'search': search.trim(),
      },
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(
        () => CommonNameListResponse.fromJson(r),
        (o, s) => HttpFailure.parsing(o.toString(), 500, s),
      ),
    );
  }

  @override
  ApiResult<PostGenresListResponse> getGenresList({
    String? search,
    int? page,
    int? perPage,
  }) async {
    final response = await client.get(
      url: AppStrings.genresList,
      params: <String, dynamic>{
        if (page != null) 'page': '$page',
        if (perPage != null) 'per_page': '$perPage',
        if (search != null && search.trim().isNotEmpty) 'search': search.trim(),
      },
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(
          () => PostGenresListResponse.fromJson(r), (o, s) => HttpFailure.parsing(o.toString(), 500, s)),
    );
  }

  @override
  ApiResult<CommonNameListResponse> getTagsList({String? search}) async {
    final response = await client.get(
      url: AppStrings.tagsList,
      params: <String, dynamic>{
        if (search != null) 'search': search.trim(),
      },
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(
        () => CommonNameListResponse.fromJson(r),
        (o, s) => HttpFailure.parsing(o.toString(), 500, s),
      ),
    );
  }
}
