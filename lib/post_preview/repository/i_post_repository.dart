import 'dart:typed_data';

import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:tynt_web/auth/response/common_response.dart';
import 'package:tynt_web/constants/app_strings.dart';
import 'package:tynt_web/extentions/string_extentions.dart';
import 'package:tynt_web/post_preview/response/common_name_list_response.dart';
import 'package:tynt_web/post_preview/response/post_detail_reponse.dart';
import 'package:tynt_web/post_preview/response/post_genres_list_response.dart';
import 'package:tynt_web/post_preview/response/post_list_response.dart';
import 'package:tynt_web/post_preview/response/post_type_list_response.dart';
import 'package:tynt_web/tomes/response/tome_list_respons.dart';
import 'package:tynt_web/utlity/app_typedefs/http_typedef.dart';
import 'package:tynt_web/utlity/failure/http_failure.dart';
import 'package:tynt_web/utlity/network/client.dart';

part 'post_repository.dart';

abstract class IPostRepository {
  IPostRepository(this.client);
  final Client client;

  ApiResult<PostListResponse> getPostsList({
    int page = 1,
    int perPage = 10,
    int? userId,
    int? isDarft,
    int? isOwn,
    int? communityId,
    String? search,
    List<int> genresIds = const <int>[],
    int? authenticatedId,
    int? postTypeId,
    String? filter,
  });

  ApiResult<TomesListResponse> getTomesList({
    String? search,
  });

  ApiResult<PostDetailResponse> getPostDetail(int id);

  ApiResult<PostDetailResponse> createPost({
    required String description,
    int? postTypeId,
    String? title,
    List<int> tagsIds = const <int>[],
    int? genresId,
    List<String> newTags = const <String>[],
    bool isPseudo = false,
    bool isDraft = false,
    Uint8List? image,
    int? communityId,
    bool isTyntYPost = false,
    bool showComments = false,
    List<int> mentionUserIds = const <int>[],
    List<int> mentionMovieIds = const <int>[],
  });

  ApiResult<PostDetailResponse> editPost({
    required int postId,
    String? title,
    String? description,
    int? postTypeId,
    List<int> tagsIds = const <int>[],
    int? genresId,
    List<String> newTags = const <String>[],
    String? deleteImage,
    bool isDraft = false,
    Uint8List? image,
    int? communityId,
    bool showComments = false,
    List<int> mentionUserIds = const <int>[],
    List<int> mentionMovieIds = const <int>[],
    List<int> tomsIds = const <int>[],
  });

  ApiResult<PostTypeListResponse> getPostTypeList({
    int page = 1,
    int perPage = 10,
    String? search,
  });

  ApiResult<CommonResponse> deletePost(int postId);

  ApiResult<CommonNameListResponse> getMoviesList({String? search});

  ApiResult<PostGenresListResponse> getGenresList({
    String? search,
    int? page,
    int? perPage,
  });

  ApiResult<CommonNameListResponse> getTagsList({String? search});
}
