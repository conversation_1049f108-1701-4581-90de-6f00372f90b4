import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:tynt_web/extentions/fpdart_extentions.dart';
import 'package:tynt_web/post_preview/model/post_model.dart';
import 'package:tynt_web/post_preview/repository/i_post_repository.dart';
import 'package:tynt_web/post_preview/response/post_list_response.dart';
import 'package:tynt_web/tomes/model/tomes_model.dart';
import 'package:tynt_web/tomes/response/tome_list_respons.dart';
import 'package:tynt_web/utlity/app_typedefs/http_typedef.dart';
import 'package:tynt_web/utlity/bloc_tranformer.dart';
import 'package:tynt_web/utlity/enum/my_account_post_filter_enum.dart';
import 'package:tynt_web/utlity/failure/http_failure.dart';

part 'my_account_event.dart';
part 'my_account_state.dart';

@injectable
class MyAccountBloc extends Bloc<MyAccountEvent, MyAccountState> {
  MyAccountBloc({
    required this.postRepository,
  }) : super(const MyAccountInitial()) {
    on<MyAccountLoad>(_onMyAccountLoad);
    on<MyAccountLoadMore>(_onMyAccountLoadMore, transformer: loadMoreTransformer());
    on<MyAccountChangePostFilter>(_onMyAccountChangePostFilter);
    on<MyAccountEditPost>(_onEditPost);
    on<MyAccountDeletePost>(_onDeletePost);
    on<MyAccountAddPost>(_onAddPost);
    on<MyAccountAddTome>(_onAddTome);
    on<MyAccountEditTome>(_onEditTome);
    on<MyAccountDeleteTome>(_onDeleteTome);
  }

  final IPostRepository postRepository;

  Future<void> _onMyAccountLoad(
    MyAccountLoad event,
    Emitter<MyAccountState> emit,
  ) async {
    emit(const MyAccountLoading());

    final failOrGetApiResults = await Future.wait([
      postRepository.getPostsList(isOwn: 1),
      postRepository.getTomesList(),
    ]);

    final failOrPosts = failOrGetApiResults[0] as ApiNormalResult<PostListResponse>;
    final failOrTomes = failOrGetApiResults[1] as ApiNormalResult<TomesListResponse>;

    emit(
      MyAccountLoaded(
        posts: failOrPosts.getRightFolded()?.posts ?? <PostModel>[],
        hasReachedMax: (failOrPosts.getRightFolded()?.posts ?? <PostModel>[]).length < 9,
        postFailure: failOrPosts.getLeftFolded(),
        tomesFailure: failOrTomes.getLeftFolded(),
        tomes: failOrTomes.getRightFolded()?.data ?? <TomesModel>[],
      ),
    );
  }

  Future<void> _onMyAccountChangePostFilter(
    MyAccountChangePostFilter event,
    Emitter<MyAccountState> emit,
  ) async {
    emit(const MyAccountLoading());

    final failOrGetApiResults = await Future.wait([
      postRepository.getPostsList(isOwn: 1, filter: event.type.postFilter),
      postRepository.getTomesList(),
    ]);

    final failOrPosts = failOrGetApiResults[0] as ApiNormalResult<PostListResponse>;
    final failOrTomes = failOrGetApiResults[1] as ApiNormalResult<TomesListResponse>;

    emit(
      MyAccountLoaded(
        posts: failOrPosts.getRightFolded()?.posts ?? <PostModel>[],
        hasReachedMax: (failOrPosts.getRightFolded()?.posts ?? <PostModel>[]).length < 9,
        postFailure: failOrPosts.getLeftFolded(),
        tomesFailure: failOrTomes.getLeftFolded(),
        tomes: failOrTomes.getRightFolded()?.data ?? <TomesModel>[],
        postFilter: event.type,
      ),
    );
  }

  Future<void> _onMyAccountLoadMore(
    MyAccountLoadMore event,
    Emitter<MyAccountState> emit,
  ) async {
    if (state is! MyAccountLoaded) return;
    final newState = state as MyAccountLoaded;

    emit(newState.copyWith(isPostLoadingMore: true));

    final failOrSuccess = await postRepository.getPostsList(
      page: newState.currentPage + 1,
      isOwn: 1,
    );

    emit(
      failOrSuccess.fold(
        (l) => newState.copyWith(isPostLoadingMore: false),
        (r) => newState.copyWith(
          isPostLoadingMore: false,
          hasReachedMax: r.posts.length < 9,
          posts: [...newState.posts, ...r.posts],
          currentPage: newState.currentPage + 1,
        ),
      ),
    );
  }

  Future<void> _onEditPost(
    MyAccountEditPost event,
    Emitter<MyAccountState> emit,
  ) async {
    if (state is! MyAccountLoaded) return;
    final newState = state as MyAccountLoaded;

    emit(newState.copyWith(posts: [...newState.posts.map((e) => e.id == event.post.id ? event.post : e)]));
  }

  Future<void> _onDeletePost(
    MyAccountDeletePost event,
    Emitter<MyAccountState> emit,
  ) async {
    if (state is! MyAccountLoaded) return;
    final newState = state as MyAccountLoaded;

    emit(newState.copyWith(posts: [...newState.posts]..removeWhere((e) => e.id == event.post.id)));
  }

  Future<void> _onAddPost(
    MyAccountAddPost event,
    Emitter<MyAccountState> emit,
  ) async {
    if (state is! MyAccountLoaded) return;
    final newState = state as MyAccountLoaded;

    emit(newState.copyWith(posts: [event.post, ...newState.posts]));
  }

  Future<void> _onAddTome(
    MyAccountAddTome event,
    Emitter<MyAccountState> emit,
  ) async {
    if (state is! MyAccountLoaded) return;
    final newState = state as MyAccountLoaded;

    emit(newState.copyWith(tomes: [event.tome, ...newState.tomes]));
  }

  Future<void> _onEditTome(
    MyAccountEditTome event,
    Emitter<MyAccountState> emit,
  ) async {
    if (state is! MyAccountLoaded) return;
    final newState = state as MyAccountLoaded;

    emit(newState.copyWith(tomes: [...newState.tomes.map((e) => e.id == event.tome.id ? event.tome : e)]));
  }

  Future<void> _onDeleteTome(
    MyAccountDeleteTome event,
    Emitter<MyAccountState> emit,
  ) async {
    if (state is! MyAccountLoaded) return;
    final newState = state as MyAccountLoaded;

    emit(newState.copyWith(tomes: [...newState.tomes]..removeWhere((e) => e.id == event.tome.id)));
  }
}
