part of 'my_account_bloc.dart';

sealed class MyAccountState extends Equatable {
  const MyAccountState();

  @override
  List<Object?> get props => [];

  @override
  String toString() {
    return 'MyAccountState()';
  }

  R map<R>({
    required R Function(MyAccountInitial state) initial,
    required R Function(MyAccountLoading state) loading,
    required R Function(MyAccountLoaded state) loaded,
  }) {
    if (this is MyAccountInitial) {
      return initial(this as MyAccountInitial);
    }
    if (this is MyAccountLoading) {
      return loading(this as MyAccountLoading);
    }
    if (this is MyAccountLoaded) {
      return loaded(this as MyAccountLoaded);
    }
    return initial(this as MyAccountInitial);
  }

  R? mapOrNull<R>({
    R? Function(MyAccountInitial state)? initial,
    R? Function(MyAccountLoading state)? loading,
    R? Function(MyAccountLoaded state)? loaded,
  }) {
    if (this is MyAccountInitial) {
      return initial?.call(this as MyAccountInitial);
    }
    if (this is MyAccountLoading) {
      return loading?.call(this as MyAccountLoading);
    }
    if (this is MyAccountLoaded) {
      return loaded?.call(this as MyAccountLoaded);
    }
    return null;
  }

  R maybeWhen<R>({
    required R Function() orElse,
    R Function(MyAccountInitial state)? initial,
    R Function(MyAccountLoading state)? loading,
    R Function(MyAccountLoaded state)? loaded,
  }) {
    if (this is MyAccountInitial) {
      return initial?.call(this as MyAccountInitial) ?? orElse();
    }
    if (this is MyAccountLoading) {
      return loading?.call(this as MyAccountLoading) ?? orElse();
    }
    if (this is MyAccountLoaded) {
      return loaded?.call(this as MyAccountLoaded) ?? orElse();
    }
    return orElse();
  }
}

final class MyAccountInitial extends MyAccountState {
  const MyAccountInitial();

  @override
  String toString() {
    return 'MyAccountInitial()';
  }
}

final class MyAccountLoading extends MyAccountState {
  const MyAccountLoading();

  @override
  String toString() {
    return 'MyAccountLoading()';
  }
}

final class MyAccountLoaded extends MyAccountState {
  const MyAccountLoaded({
    this.posts = const <PostModel>[],
    this.tomes = const <TomesModel>[],
    this.currentPage = 1,
    this.hasReachedMax = false,
    this.isPostLoadingMore = false,
    this.postFailure,
    this.tomesFailure,
    this.postFilter = MyAccountPostFilterType.all,
  });

  final List<PostModel> posts;
  final List<TomesModel> tomes;
  final int currentPage;
  final bool hasReachedMax;
  final bool isPostLoadingMore;
  final HttpFailure? postFailure;
  final HttpFailure? tomesFailure;
  final MyAccountPostFilterType postFilter;

  @override
  List<Object?> get props =>
      [posts, tomes, postFailure, tomesFailure, currentPage, hasReachedMax, isPostLoadingMore, postFilter];

  MyAccountLoaded copyWith({
    List<PostModel>? posts,
    List<TomesModel>? tomes,
    int? currentPage,
    bool? hasReachedMax,
    bool? isPostLoadingMore,
    HttpFailure? postFailure,
    HttpFailure? tomesFailure,
    MyAccountPostFilterType? postFilter,
  }) {
    return MyAccountLoaded(
      posts: posts ?? this.posts,
      tomes: tomes ?? this.tomes,
      currentPage: currentPage ?? this.currentPage,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      isPostLoadingMore: isPostLoadingMore ?? this.isPostLoadingMore,
      postFailure: postFailure ?? this.postFailure,
      tomesFailure: tomesFailure ?? this.tomesFailure,
      postFilter: postFilter ?? this.postFilter,
    );
  }

  @override
  String toString() {
    return 'MyAccountLoaded ( posts: $posts, tomes: $tomes, currentPage: $currentPage, hasReachedMax: $hasReachedMax, isPostLoadingMore: $isPostLoadingMore, postFailure: $postFailure, tomesFailure: $tomesFailure, postFilter: $postFilter)';
  }
}
