part of 'my_account_bloc.dart';

sealed class MyAccountEvent extends Equatable {
  const MyAccountEvent();

  @override
  List<Object?> get props => [];
}

final class MyAccountLoad extends MyAccountEvent {
  const MyAccountLoad();
}

final class MyAccountLoadMore extends MyAccountEvent {
  const MyAccountLoadMore();

  @override
  List<Object?> get props => [];

  @override
  String toString() => 'MyAccountLoadMore()';
}

final class MyAccountChangePostFilter extends MyAccountEvent {
  const MyAccountChangePostFilter({required this.type});
  final MyAccountPostFilterType type;

  @override
  List<Object?> get props => [type];

  @override
  String toString() => 'MyAccountChangePostFilter(type: $type)';
}

final class MyAccountAddPost extends MyAccountEvent {
  const MyAccountAddPost({required this.post});
  final PostModel post;

  @override
  List<Object?> get props => [post];

  @override
  String toString() => 'MyAccountAddPost(post: $post)';
}

final class MyAccountEditPost extends MyAccountEvent {
  const MyAccountEditPost({required this.post});
  final PostModel post;

  @override
  List<Object?> get props => [post];

  @override
  String toString() => 'MyAccountEditPost(post: $post)';
}

final class MyAccountDeletePost extends MyAccountEvent {
  const MyAccountDeletePost({required this.post});
  final PostModel post;

  @override
  List<Object?> get props => [post];

  @override
  String toString() => 'MyAccountDeletePost(post: $post)';
}

final class MyAccountAddTome extends MyAccountEvent {
  const MyAccountAddTome({required this.tome});
  final TomesModel tome;

  @override
  List<Object?> get props => [tome];

  @override
  String toString() => 'MyAccountAddTome(tome: $tome)';
}

final class MyAccountEditTome extends MyAccountEvent {
  const MyAccountEditTome({required this.tome});
  final TomesModel tome;

  @override
  List<Object?> get props => [tome];

  @override
  String toString() => 'MyAccountEditTome(tome: $tome)';
}

final class MyAccountDeleteTome extends MyAccountEvent {
  const MyAccountDeleteTome({required this.tome});
  final TomesModel tome;

  @override
  List<Object?> get props => [tome];

  @override
  String toString() => 'MyAccountDeleteTome(tome: $tome)';
}
