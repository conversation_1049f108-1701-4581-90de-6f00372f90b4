import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/app/cubit/refresh_cubit.dart';
import 'package:tynt_web/auth/bloc/authentication_bloc.dart';
import 'package:tynt_web/auth/models/user_model.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/tomes/view/my_account_tomes_list_view.dart';
import 'package:tynt_web/user_profile/bloc/my_account_bloc.dart';
import 'package:tynt_web/user_profile/view/my_account_post_list_view.dart';
import 'package:tynt_web/user_profile/widget/my_account_widget.dart';
import 'package:tynt_web/user_profile/widget/user_profile_max_width_wapper.dart';
import 'package:tynt_web/utlity/enum/my_account_post_filter_enum.dart';
import 'package:tynt_web/utlity/pagination/pagination_mixin.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/dailog_boxes.dart';
import 'package:tynt_web/widgets/no_data.dart';

class UserProfilePage extends StatefulWidget {
  const UserProfilePage({super.key});

  @override
  State<UserProfilePage> createState() => _UserProfilePageState();
}

class _UserProfilePageState extends State<UserProfilePage> with PaginationMixin {
  @override
  void initState() {
    super.initState();
    initiatePagination();
  }

  @override
  void dispose() {
    disposePagination();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthenticationBloc, AuthenticationState>(
      listenWhen: (previous, current) => previous.currentUser != current.currentUser,
      listener: (context, state) {
        context.read<MyAccountBloc>().add(const MyAccountLoad());
      },
      child: BlocListener<RefreshCubit, RefreshState>(
        listenWhen: (previous, current) => previous.deletePost != current.deletePost,
        listener: (context, state) {
          if (state.deletePost != null) {
            context.read<MyAccountBloc>().add(MyAccountDeletePost(post: state.deletePost!));
          }
        },
        child: BlocListener<RefreshCubit, RefreshState>(
          listenWhen: (previous, current) => previous.newPost == null && current.newPost != null,
          listener: (context, state) {
            if (state.newPost != null) {
              context.read<MyAccountBloc>().add(MyAccountAddPost(post: state.newPost!));
            }
          },
          child: BlocListener<RefreshCubit, RefreshState>(
            listenWhen: (previous, current) => previous.postDetail != current.postDetail,
            listener: (context, state) {
              if (state.postDetail != null) {
                context.read<MyAccountBloc>().add(MyAccountEditPost(post: state.postDetail!));
              }
            },
            child: CustomScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              controller: scrollPaginationController,
              slivers: [
                SliverToBoxAdapter(
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(maxWidth: 748),
                    child: BlocSelector<AuthenticationBloc, AuthenticationState, UserModel?>(
                      selector: (state) => state.currentUser,
                      builder: (context, user) {
                        return Skeletonizer(
                          enabled: user == null || user.id == 0,
                          child: MyAccountUserDetailWidget(user: user!),
                        );
                      },
                    ),
                  ),
                ),
                SliverMainAxisGroup(slivers: [
                  SliverToBoxAdapter(
                    child: BlocSelector<MyAccountBloc, MyAccountState, bool>(
                      selector: (state) => state is MyAccountLoading,
                      builder: (context, isLoading) {
                        return Skeletonizer(
                          enabled: isLoading,
                          child: UserProfileMaxWidthWapper(
                            child: Container(
                              margin: const EdgeInsets.symmetric(
                                horizontal: 16,
                              ),
                              child: Row(
                                children: [
                                  Text(
                                    context.l10n.tomes.toUpperCase(),
                                    style: context.textTheme.bodyMedium?.copyWith(
                                      fontSize: 15,
                                      letterSpacing: 0,
                                      color: Theme.of(context).primaryColorLight,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  const Gap(10),
                                  const AppImageAsset(
                                    AppAssets.tomesIcon,
                                    height: 22,
                                    width: 22,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    // ,
                  ),
                  const SliverGap(16),
                  SliverToBoxAdapter(
                    child: BlocBuilder<MyAccountBloc, MyAccountState>(
                      builder: (context, state) {
                        return state.map(
                          initial: (_) => const MyAccountTomesLoadingListView(),
                          loading: (_) => const MyAccountTomesLoadingListView(),
                          loaded: (state) => MyAccountTomesListView(
                            tomes: [...state.tomes],
                          ),
                        );
                      },
                    ),
                  ),
                ]),
                const SliverGap(24),
                BlocBuilder<MyAccountBloc, MyAccountState>(
                  builder: (context, state) {
                    return SliverToBoxAdapter(
                      child: UserProfileMaxWidthWapper(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                context.l10n.posts.toUpperCase(),
                                style: context.textTheme.bodyMedium?.copyWith(
                                  fontSize: 15,
                                  letterSpacing: 0,
                                  color: Theme.of(context).textTheme.labelLarge?.color,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Skeleton.unite(
                                child: InkWell(
                                  onTap: () {
                                    DailogBoxes.openPostFilterBottomSheet(
                                      context,
                                      onAllPostTap: () {
                                        context.read<MyAccountBloc>().add(
                                              const MyAccountChangePostFilter(
                                                type: MyAccountPostFilterType.all,
                                              ),
                                            );
                                      },
                                      onMyPostTap: () {
                                        context.read<MyAccountBloc>().add(
                                              const MyAccountChangePostFilter(
                                                type: MyAccountPostFilterType.publicPosts,
                                              ),
                                            );
                                      },
                                      onDraftPostTap: () {
                                        context.read<MyAccountBloc>().add(
                                              const MyAccountChangePostFilter(
                                                type: MyAccountPostFilterType.draftPosts,
                                              ),
                                            );
                                      },
                                      onTribePostTap: () {
                                        context.read<MyAccountBloc>().add(
                                              const MyAccountChangePostFilter(
                                                type: MyAccountPostFilterType.tribe,
                                              ),
                                            );
                                      },
                                    );
                                  },
                                  child: AppImageAsset(
                                    AppAssets.filterIcon,
                                    height: 24,
                                    width: 24,
                                    color: Theme.of(context).textTheme.labelLarge?.color,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
                BlocBuilder<MyAccountBloc, MyAccountState>(
                  builder: (context, state) {
                    return state.map(
                      initial: (_) => const MyAccountPostLoadingListView(),
                      loading: (_) => const MyAccountPostLoadingListView(),
                      loaded: (state) {
                        if (state.posts.isEmpty) {
                          return const SliverFillRemaining(child: NoData());
                        }
                        return MyAccountPostListView(posts: state.posts);
                      },
                    );
                  },
                ),
                const SliverGap(10),
                BlocSelector<MyAccountBloc, MyAccountState, bool>(
                  selector: (state) => state.mapOrNull(loaded: (state) => state.isPostLoadingMore) ?? false,
                  builder: (context, isPostLoadingMore) {
                    if (isPostLoadingMore) {
                      return const MyAccountPostLoadingListView();
                    }
                    return const SliverGap(0);
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void onReachedLast() {
    final state = context.read<MyAccountBloc>().state;
    final loadedState = state.mapOrNull(loaded: (state) => state);

    if (loadedState == null) return;

    if (loadedState.hasReachedMax) return;

    if (loadedState.isPostLoadingMore) return;

    context.read<MyAccountBloc>().add(const MyAccountLoadMore());
  }
}
