// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:tynt_web/user_profile/widget/select_genres_widget.dart';
import 'package:tynt_web/utlity/enum/gender_enums.dart';

class GenderListView extends StatelessWidget {
  const GenderListView({
    required this.onGenderSelect,
    super.key,
    this.selectedGender,
  });
  final Gender? selectedGender;
  final ValueChanged<Gender?> onGenderSelect;

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 14,
      runSpacing: 14,
      children: List.generate(Gender.values.length, (index) {
        final gender = Gender.values[index];
        return SelectGenresWidget(
          isSelecetd: selectedGender == gender,
          text: gender.formattedName(context),
          onPressed: () {
            if (selectedGender == gender) {
              onGenderSelect(null);
              return;
            }
            onGenderSelect(gender);
          },
        );
      }),
    );
  }
}
