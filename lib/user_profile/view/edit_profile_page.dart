import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:tynt_web/auth/bloc/authentication_bloc.dart';
import 'package:tynt_web/auth/models/app_file_data.dart';
import 'package:tynt_web/auth/models/country_model.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/primary_button/utility/primary_button_utlity.dart';
import 'package:tynt_web/primary_button/views/primary_button_view.dart';
import 'package:tynt_web/user/repository/i_user_manager_repository.dart';
import 'package:tynt_web/user_profile/view/gender_list_view.dart';
import 'package:tynt_web/user_profile/widget/user_circle_avatar.dart';
import 'package:tynt_web/utlity/app_validation.dart';
import 'package:tynt_web/utlity/enum/gender_enums.dart';
import 'package:tynt_web/utlity/extentions/string_extentions.dart';
import 'package:tynt_web/utlity/utlity.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/app_text_form_field.dart';
import 'package:tynt_web/widgets/dailog_boxes.dart';

class EditAccountPage extends StatefulWidget {
  const EditAccountPage({super.key});

  @override
  State<EditAccountPage> createState() => _EditAccountPageState();
}

class _EditAccountPageState extends State<EditAccountPage> {
  final selectedAvatar = ValueNotifier<AppFileData>(const AppFileData());
  final _form = GlobalKey<FormState>();
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneNumberController = TextEditingController();
  final countryController = TextEditingController();
  final stateController = TextEditingController();
  final buttonController = PrimaryLoadingButtonController();
  final selectedPhoneNumber = ValueNotifier<CountryModel?>(null);
  final selectedCountry = ValueNotifier<CountryModel?>(null);

  final ValueNotifier<bool> isResendVerficationLoading = ValueNotifier(false);

  final selectedGender = ValueNotifier<Gender?>(null);

  final ValueNotifier<bool> isBusinessUser = ValueNotifier(false);

  final userNameController = TextEditingController();
  final bioController = TextEditingController();

  @override
  void dispose() {
    selectedAvatar.dispose();
    firstNameController.dispose();

    lastNameController.dispose();
    stateController.dispose();
    emailController.dispose();
    countryController.dispose();
    phoneNumberController.dispose();
    selectedPhoneNumber.dispose();
    buttonController.dispose();
    selectedGender.dispose();
    isBusinessUser.dispose();
    selectedCountry.dispose();
    userNameController.dispose();

    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    context.read<AuthenticationBloc>().add(const CheckAuthentication());

    setUserData();
  }

  void setUserData() {
    final user = context.read<AuthenticationBloc>().state.currentUser;
    isBusinessUser.value = user.isBusinessUser;
    userNameController.text = user.userName ?? '';
    if (user.isPublicUser) {
      firstNameController.text = user.firstName ?? '';
      lastNameController.text = user.lastName ?? '';
      selectedGender.value =
          Gender.values.firstWhereOrNull((element) => element.name.toLowerCase() == user.gender?.trim().toLowerCase());
    }

    emailController.text = user.email ?? '';
    phoneNumberController.text = user.phoneNumber ?? '';
    stateController.text = user.state ?? '';
    bioController.text = user.bio ?? '';

    selectedAvatar.value = AppFileData(
      networkUrl: user.getProfileImageUrl,
      selectedAvatar: user.avatar,
      defaultAvatar: user.isBusinessUser ? AppAssets.businessUserDefaultIcon : AppAssets.defaultMan,
    );
    if (user.country != null) {
      countryController.text = (user.country?.name ?? '').inCaps;
      selectedCountry.value = user.country;
    }
    selectedPhoneNumber.value = CountryModel(phoneCode: user.phoneCode);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 716),
          child: AppBar(
            leadingWidth: context.isMobile ? 80 : 24,
            titleSpacing: 0,
            leading: InkWell(
              onTap: () {
                context.pop();
              },
              child: const Center(child: AppImageAsset(AppAssets.backArrowIcon, height: 24, width: 24)),
            ),
            title: Padding(
              padding: const EdgeInsets.only(left: 24),
              child: Text(
                context.l10n.profile,
                style: context.textTheme.bodyMedium?.copyWith(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ),
        ),
        Flexible(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            physics: const AlwaysScrollableScrollPhysics(),
            child: BlocConsumer<AuthenticationBloc, AuthenticationState>(
              listenWhen: (previous, current) => previous.isAuthLoading != current.isAuthLoading,
              listener: (context, state) {
                setUserData();
              },
              builder: (context, state) {
                return Form(
                  key: _form,
                  child: Center(
                    child: ConstrainedBox(
                      constraints: const BoxConstraints(
                        maxWidth: 716,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Gap(20),
                          Row(
                            children: [
                              ValueListenableBuilder<AppFileData>(
                                valueListenable: selectedAvatar,
                                builder: (_, value, __) {
                                  if (value.imageBytes != null) {
                                    return CircleAvatar(
                                      backgroundColor: AppColors.white,
                                      radius: context.isMobile ? 36 : 50,
                                      backgroundImage: Image.memory(value.imageBytes!).image,
                                    );
                                  }
                                  if (value.networkUrl != null) {
                                    return UserCircleAvatar(
                                      imageUrl: value.networkUrl,
                                    );
                                  }
                                  if (value.selectedAvatar != null) {
                                    return UserCircleAvatar(
                                      imageUrl: value.selectedAvatar?.imageUrl,
                                    );
                                  }
                                  return CircleAvatar(
                                    radius: context.isMobile ? 36 : 50,
                                    backgroundImage: Image.asset(value.defaultAvatar).image,
                                  );
                                },
                              ),
                              Gap(context.isMobile ? 16 : 22),
                              TextButton(
                                style: TextButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
                                  shape: RoundedRectangleBorder(
                                    side: const BorderSide(color: AppColors.border),
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                ),
                                onPressed: () {
                                  final user = context.read<AuthenticationBloc>().state.currentUser;
                                  if (user.isBusinessUser) {
                                    pickImageFromGallery();
                                    return;
                                  }
                                  DailogBoxes.selectAvatarPopup(
                                    context,
                                    defaultAvatar: selectedAvatar.value.defaultAvatar,
                                    onAvatarTap: pickAvatrFromSheet,
                                    onGalleryTap: pickImageFromGallery,
                                  );
                                },
                                child: Text(
                                  context.l10n.uploadPic,
                                  style: context.textTheme.bodyMedium?.copyWith(
                                    // color: AppColors.primary,
                                    fontSize: context.isMobile ? 15 : 18,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          ValueListenableBuilder<bool>(
                            valueListenable: isBusinessUser,
                            builder: (_, isBusiness, __) {
                              if (isBusiness) {
                                return const SizedBox.shrink();
                              }
                              return Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Gap(context.isMobile ? 22 : 32),
                                  Row(
                                    children: [
                                      Flexible(
                                        child: AppTextFormField(
                                          isRequired: true,
                                          title: context.l10n.firstName,
                                          textInputAction: TextInputAction.next,
                                          controller: firstNameController,
                                          validator: (value) =>
                                              AppValidation.firstNameValidation(context, value: value),
                                          hintText: context.l10n.name,
                                        ),
                                      ),
                                      const Gap(15),
                                      Flexible(
                                        child: AppTextFormField(
                                          isRequired: true,
                                          controller: lastNameController,
                                          textInputAction: TextInputAction.next,
                                          validator: (value) => AppValidation.lastNameValidation(context, value: value),
                                          title: context.l10n.lastName,
                                          hintText: context.l10n.name,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              );
                            },
                          ),
                          Gap(context.isMobile ? 20 : 25),
                          AppTextFormField(
                            hintText: context.l10n.userName,
                            title: context.l10n.userName,
                            isRequired: true,
                            controller: userNameController,
                            validator: (value) => AppValidation.userNameValidationRequired(context, value: value),
                          ),
                          Gap(context.isMobile ? 20 : 25),
                          BlocBuilder<AuthenticationBloc, AuthenticationState>(
                            builder: (context, state) {
                              return AppTextFormField(
                                title: context.l10n.email,
                                isRequired: true,
                                keyboardType: TextInputType.emailAddress,
                                controller: emailController,
                                validator: (value) => AppValidation.emailValidation(context, email: value),
                                hintText: context.l10n.email,
                                isReadOnly: state.user.publicUser?.isEmailVerified == 1,
                                headerWidgets: [
                                  const WidgetSpan(child: SizedBox(width: 3)),
                                  if (state.user.publicUser?.isEmailVerified == 1)
                                    WidgetSpan(
                                      child: Icon(
                                        Icons.check_circle,
                                        size: 18,
                                        color: Theme.of(context).brightness == Brightness.dark
                                            ? AppColors.white
                                            : AppColors.primary,
                                      ),
                                    )
                                  else
                                    WidgetSpan(
                                      child: ValueListenableBuilder<bool>(
                                        valueListenable: isResendVerficationLoading,
                                        builder: (_, value, __) {
                                          if (value) {
                                            return const SizedBox(
                                              height: 15,
                                              width: 15,
                                              child: CupertinoActivityIndicator(color: AppColors.primary),
                                            );
                                          }
                                          return InkWell(
                                            onTap: resendEmailVerification,
                                            child: Text(
                                              context.l10n.verify,
                                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                                    fontWeight: FontWeight.w600,
                                                    color: Colors.blue,
                                                    fontSize: context.isMobile ? 13 : 16,
                                                  ),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                ],
                              );
                            },
                          ),
                          ValueListenableBuilder<bool>(
                            valueListenable: isBusinessUser,
                            builder: (_, isBusiness, __) {
                              if (isBusiness) return const SizedBox.shrink();
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Gap(context.isMobile ? 20 : 25),
                                  Text(
                                    context.l10n.gender,
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.copyWith(fontWeight: FontWeight.w500, fontSize: context.isMobile ? 15 : 18),
                                  ),
                                  Gap(context.isMobile ? 8 : 12),
                                  ValueListenableBuilder<Gender?>(
                                    valueListenable: selectedGender,
                                    builder: (_, pickedGender, __) {
                                      return GenderListView(
                                        onGenderSelect: (value) {
                                          selectedGender.value = value;
                                        },
                                        selectedGender: pickedGender,
                                      );
                                    },
                                  ),
                                ],
                              );
                            },
                          ),
                          Gap(context.isMobile ? 20 : 25),
                          AppTextFormField(
                            hintText: context.l10n.userBio,
                            title: context.l10n.userBio,
                            controller: bioController,
                            minLines: 4,
                            maxLines: 4,
                          ),
                          Gap(context.isMobile ? 20 : 25),
                          AppTextFormField(
                            title: context.l10n.phoneNumber,
                            prefixIcon: InkWell(
                              overlayColor: WidgetStateProperty.all(AppColors.transparent),
                              onTap: () async {
                                final countryValue = await DailogBoxes.selectCountryDailog(context);
                                if (countryValue != null) {
                                  selectedPhoneNumber.value = countryValue;
                                }
                              },
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Gap(14),
                                  ValueListenableBuilder<CountryModel?>(
                                    valueListenable: selectedPhoneNumber,
                                    builder: (_, countryCode, __) {
                                      return Text(
                                        countryCode?.phoneCode != null ? '+${countryCode?.phoneCode}' : '',
                                        style: context.textTheme.bodyMedium?.copyWith(
                                          color: Theme.of(context).brightness == Brightness.dark
                                              ? AppColors.textDark
                                              : AppColors.text,
                                        ),
                                      );
                                    },
                                  ),
                                  const Gap(10),
                                  const AppImageAsset(
                                    AppAssets.downArrowIcon,
                                    height: 24,
                                    width: 24,
                                  ),
                                  const Gap(10),
                                ],
                              ),
                            ),
                            keyboardType: TextInputType.number,
                            controller: phoneNumberController,
                            hintText: context.l10n.phoneNumber,
                          ),
                          const Gap(20),
                          Row(
                            children: [
                              Flexible(
                                child: GestureDetector(
                                  onTap: () async {
                                    final countryValue = await DailogBoxes.selectCountryDailog(context);
                                    if (countryValue != null) {
                                      countryController.text = (countryValue.name ?? '').inCaps;
                                      selectedCountry.value = countryValue;
                                    }
                                  },
                                  child: AppTextFormField(
                                    // isRequired: true,
                                    enabled: false,
                                    suffixIconConstraints: const BoxConstraints(minWidth: 40),
                                    suffixIcon: const Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        AppImageAsset(
                                          AppAssets.downArrowIcon,
                                          height: 24,
                                          width: 24,
                                        ),
                                      ],
                                    ),
                                    title: context.l10n.country,
                                    controller: countryController,
                                    keyboardType: TextInputType.none,
                                    // validator: (value) => AppValidation.countryValidation(context, value: value),
                                    hintText: context.l10n.select,
                                  ),
                                ),
                              ),
                              const Gap(15),
                              Flexible(
                                child: AppTextFormField(
                                  controller: stateController,
                                  // validator: (value) => AppValidation.stateValidation(context, value: value),
                                  title: context.l10n.state,
                                  hintText: context.l10n.state,
                                ),
                              ),
                            ],
                          ),
                          const Gap(40),
                          Center(
                            child: PrimaryButton(
                              onPressed: () {
                                if (_form.currentState!.validate()) {
                                  updateUser();
                                }
                              },
                              // borderRadius: 20,
                              text: context.l10n.save,
                              primaryLoadingButtonController: buttonController,
                            ),
                          ),
                          Gap(context.isMobile ? 22 : 32),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Future<void> updateUser() async {
    final user = context.read<AuthenticationBloc>().state.currentUser;
    buttonController.start();

    final failOrSuccess = await getIt<IUserManagerRepository>().updateUser(
      firstName: firstNameController.text,
      lastName: lastNameController.text,
      phoneNumber: phoneNumberController.text,
      bio: bioController.text,
      email: (user.isEmailVerified == 1) ? null : emailController.text.trim(),
      phoneCode: selectedPhoneNumber.value?.phoneCode,
      profileImage: selectedAvatar.value.imageBytes,
      state: stateController.text.trim(),
      avatarId: selectedAvatar.value.selectedAvatar?.id,
      gender: selectedGender.value?.name,
      countryId: selectedCountry.value?.id,
      userName: userNameController.text.trim(),
    );

    failOrSuccess.fold(
      (l) {
        buttonController.error();
        Utility.toast(message: l.message);
      },
      (r) {
        buttonController.success();

        Future.delayed(const Duration(milliseconds: 850), () {
          // Utility.toast(message: r.message);

          context.read<AuthenticationBloc>().add(UpdateUserEvent(user: r.data));
          context.pop();
        });
      },
    );
  }

  Future<void> resendEmailVerification() async {
    // isResendVerficationLoading.value = true;
    // final failOrSuccess = await getIt<IUserManagerRepository>().resendVerification();
    // failOrSuccess.fold((l) {
    //   isResendVerficationLoading.value = false;
    //   Utility.toast(message: l.message);
    // }, (r) {
    //   isResendVerficationLoading.value = false;
    //   Utility.toast(message: r.message);
    // });
  }

  Future<void> pickImageFromGallery() async {
    final pickedImage = await ImagePicker().pickImage(source: ImageSource.gallery);
    if (pickedImage != null) {
      selectedAvatar.value = AppFileData(imageBytes: await pickedImage.readAsBytes());
    }
  }

  Future<void> pickAvatrFromSheet() async {
    final pickedAvatar = await DailogBoxes.selectAvatarBottomSheet(
      context,
      selectedAvatar: selectedAvatar.value.selectedAvatar,
    );

    if (pickedAvatar != null) {
      selectedAvatar.value = AppFileData(selectedAvatar: pickedAvatar);
    }
  }
}
