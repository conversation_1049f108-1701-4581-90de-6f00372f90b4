import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:tynt_web/auth/models/app_file_data.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/constants/app_strings.dart';
import 'package:tynt_web/constants/app_theme.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/primary_button/utility/primary_button_utlity.dart';
import 'package:tynt_web/primary_button/views/primary_button_view.dart';
import 'package:tynt_web/tomes/model/tomes_model.dart';
import 'package:tynt_web/tomes/repository/i_tomes_repository.dart';
import 'package:tynt_web/user_profile/widget/user_circle_avatar.dart';
import 'package:tynt_web/utlity/app_validation.dart';
import 'package:tynt_web/utlity/utlity.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/app_text_form_field.dart';
import 'package:tynt_web/widgets/popup_wrapper.dart';

class CreateTomDailogView extends StatefulWidget {
  const CreateTomDailogView({
    super.key,
    this.onCreateTomTap,
    this.isEdit = false,
    this.tomesModel,
    this.context,
  });
  final void Function(TomesModel)? onCreateTomTap;
  final bool isEdit;
  final TomesModel? tomesModel;
  final BuildContext? context;

  @override
  State<CreateTomDailogView> createState() => _CreateTomDailogViewState();
}

class _CreateTomDailogViewState extends State<CreateTomDailogView> {
  final selectedAvatar = ValueNotifier<AppFileData>(const AppFileData());
  final tomNameController = TextEditingController();
  final tomDetailsController = TextEditingController();

  final formKey = GlobalKey<FormState>();
  final createTomController = PrimaryLoadingButtonController();

  @override
  void initState() {
    super.initState();
    if (widget.isEdit) {
      tomNameController.text = widget.tomesModel?.name ?? '';
      tomDetailsController.text = widget.tomesModel?.description ?? '';
      selectedAvatar.value = AppFileData(
        networkUrl: widget.tomesModel?.image != null ? '${AppStrings.storageUrl}/${widget.tomesModel?.image}' : null,
      );
    }
  }

  @override
  void dispose() {
    createTomController.dispose();
    tomNameController.dispose();
    tomDetailsController.dispose();
    selectedAvatar.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopUpWrapper(
      childrenPadding: const EdgeInsets.fromLTRB(20, 20, 20, 30),
      children: [
        Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Text(
                    widget.isEdit ? context.l10n.editTome : context.l10n.createTome,
                    style: context.textTheme.bodyLarge?.copyWith(
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                      fontFamily: AppTheme.spaceGroteskFontFamily,
                      color: AppColors.text,
                    ),
                  ),
                  const Gap(10),
                  const AppImageAsset(
                    AppAssets.tomesIcon,
                  ),
                ],
              ),
              const Gap(20),
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: Utility.maxWidth(context).maxWidth),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      // mainAxisSize: MainAxisSize.min,
                      children: [
                        ValueListenableBuilder<AppFileData>(
                          valueListenable: selectedAvatar,
                          builder: (_, value, __) {
                            if (value.imageBytes != null) {
                              return Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  image: DecorationImage(
                                    image: Image.memory(value.imageBytes!).image,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                height: 72,
                                width: 72,
                              );
                            }
                            if (value.selectedAvatar != null) {
                              return UserCircleAvatar(
                                imageUrl: value.selectedAvatar?.imageUrl,
                              );
                            }
                            if (value.networkUrl != null) {
                              return Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  image: DecorationImage(
                                    image: Image.network(value.networkUrl!).image,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                height: 72,
                                width: 72,
                              );
                            }
                            return Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                image: DecorationImage(
                                  image: Image.asset(value.defaultAvatar).image,
                                  fit: BoxFit.cover,
                                ),
                              ),
                              height: 72,
                              width: 72,
                            );
                          },
                        ),
                        const Gap(16),
                        TextButton(
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 16),
                            shape: RoundedRectangleBorder(
                              side: const BorderSide(color: AppColors.border),
                              borderRadius: BorderRadius.circular(6),
                            ),
                          ),
                          onPressed: pickImageFromGallery,
                          child: Text(
                            context.l10n.upload,
                            style: context.textTheme.bodyMedium?.copyWith(
                              color: AppColors.primary,
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const Gap(22),
                    AppTextFormField(
                      titleColor: AppColors.text,
                      title: context.l10n.tomeName,
                      textInputAction: TextInputAction.next,
                      controller: tomNameController,
                      validator: (value) => AppValidation.tomNameValidation(context, value: value),
                      hintText: context.l10n.tomeName,
                      hintStyle: context.textTheme.labelLarge?.copyWith(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    const Gap(8),
                    AppTextFormField(
                      isRequired: true,
                      textInputAction: TextInputAction.next,
                      controller: tomDetailsController,
                      validator: (value) => AppValidation.tomDetailsValidation(context, value: value),
                      hintText: context.l10n.details,
                      maxLines: 4,
                      hintStyle: context.textTheme.labelLarge?.copyWith(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
              const Gap(20),
              Row(
                children: [
                  PrimaryButton(
                    width: 164,
                    onPressed: () {
                      context.pop();
                    },
                    primaryLoadingButtonController: createTomController,
                    text: context.l10n.cancel,
                    backgroundColor: AppColors.transparent,
                    borderColor: AppColors.primary,
                    textColor: AppColors.primary,
                  ),
                  const Gap(15),
                  Expanded(
                    child: PrimaryButton(
                      onPressed: () {
                        if (formKey.currentState?.validate() ?? false) {
                          // if (selectedAvatar.value.getStorageFile == null && selectedAvatar.value.networkUrl == null) {
                          //   Utility.toast(
                          //     message: context.l10n.pleaseSelectImage,
                          //   );
                          //   return;
                          // }
                          if (widget.tomesModel?.id != null) {
                            editTome(context);
                            return;
                          }
                          createTome(context);
                        }
                      },
                      primaryLoadingButtonController: createTomController,
                      text: widget.isEdit ? context.l10n.editTome : context.l10n.createTome,
                    ),
                  ),
                ],
              ),
            ],
          ),
        )
      ],
    );
  }

  Future<void> createTome(BuildContext context) async {
    createTomController.start();
    final response = await getIt<ITomesRepository>().createTomes(
      name: tomNameController.text.trim(),
      description: tomDetailsController.text.trim(),
      image: selectedAvatar.value.imageBytes,
    );

    await response.fold(
      (l) {
        createTomController.error();
        Utility.toast(message: l.message);
      },
      (r) async {
        createTomController.success();
        if (r.status == '1' && r.tomes != null) {
          if (r.tomes != null) {
            widget.onCreateTomTap?.call(r.tomes!);
          }
          Navigator.pop(context);
        }
        createTomController.stop();
        Utility.toast(message: r.message);
      },
    );
  }

  Future<void> editTome(BuildContext context) async {
    createTomController.start();
    final response = await getIt<ITomesRepository>().editTomes(
      name: tomNameController.text.trim(),
      description: tomDetailsController.text.trim(),
      image: selectedAvatar.value.imageBytes,
      id: widget.tomesModel?.id ?? 0,
    );

    await response.fold(
      (l) {
        createTomController.error();
        Utility.toast(message: l.message);
      },
      (r) async {
        createTomController.success();
        if (r.status == '1' && r.tomes != null) {
          if (r.tomes != null) {
            widget.onCreateTomTap?.call(r.tomes!);
          }
          Navigator.pop(context);
        }
        createTomController.stop();
        Utility.toast(message: r.message);
      },
    );
  }

  Future<void> pickImageFromGallery() async {
    final pickedImage = await ImagePicker().pickImage(source: ImageSource.gallery);
    if (pickedImage != null) {
      final byte = await pickedImage.readAsBytes();
      selectedAvatar.value = AppFileData(imageBytes: byte);
    }
  }
}
