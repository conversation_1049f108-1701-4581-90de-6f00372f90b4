// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/auth/cubit/theme_cubit.dart';
import 'package:tynt_web/auth/models/country_model.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/constants/app_constants.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/user/repository/i_user_manager_repository.dart';
import 'package:tynt_web/utlity/easy_debouncer.dart';
import 'package:tynt_web/utlity/enum/user_type_enum.dart';
import 'package:tynt_web/utlity/extentions/string_extentions.dart';
import 'package:tynt_web/utlity/logger_config.dart';
import 'package:tynt_web/utlity/pagination/pagination_mixin.dart';
import 'package:tynt_web/utlity/utlity.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';
import 'package:tynt_web/widgets/app_text_form_field.dart';
import 'package:tynt_web/widgets/no_data.dart';
import 'package:tynt_web/widgets/popup_wrapper.dart';

class SelectCountryPopUp extends StatefulWidget {
  const SelectCountryPopUp({
    required this.scrollController,
    super.key,
  });
  final ScrollController scrollController;

  @override
  State<SelectCountryPopUp> createState() => _SelectCountryPopUpState();
}

class _SelectCountryPopUpState extends State<SelectCountryPopUp> with PaginationMixin {
  final ValueNotifier<bool> isLoading = ValueNotifier(false);
  final ValueNotifier<bool> isLoadingMore = ValueNotifier(false);
  final countriesList = ValueNotifier<List<CountryModel>>(<CountryModel>[]);
  final searchController = TextEditingController();
  int page = 1;
  bool hasReachedMax = false;

  @override
  void initState() {
    super.initState();
    initiatePagination();
    load();
  }

  @override
  void dispose() {
    isLoading.dispose();
    isLoadingMore.dispose();
    countriesList.dispose();
    searchController.dispose();
    disposePagination();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopUpWrapper(childrenPadding: const EdgeInsets.fromLTRB(20, 20, 20, 30), children: [
      Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Gap(24),
          BlocBuilder<ThemeCubit, ThemeState>(
            builder: (context, state) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    context.l10n.selectCountry,
                    style: context.textTheme.bodyLarge?.copyWith(
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                      color: state.themeMode == ThemeMode.dark && state.currentUserType == UserType.normal
                          ? AppColors.headingTextDark
                          : AppColors.text,
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      context.pop();
                    },
                    icon: Icon(
                      Icons.close_outlined,
                      color: state.themeMode == ThemeMode.dark && state.currentUserType == UserType.normal
                          ? AppColors.headingTextDark
                          : AppColors.text,
                      size: context.isMobile ? 24 : 30,
                    ),
                  ),
                ],
              );
            },
          ),
          Gap(context.isMobile ? 5 : 10),
          AppTextFormField(
            hintText: context.l10n.search,
            controller: searchController,
            onChanged: (value) {
              EasyDebounce.debounce(
                'COUNTRY_LIST_SEARCH',
                const Duration(milliseconds: kDebounceTimeInMiliSec),
                load,
              );
            },
            prefixIcon: Padding(
              padding: const EdgeInsets.only(left: 12, right: 8),
              child: AppImageAsset(
                AppAssets.searchUnselected,
                height: 24,
                width: 24,
                color: Theme.of(context).appBarTheme.titleTextStyle?.color,
              ),
            ),
            preFixIconConstraints: const BoxConstraints(),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(5),
              borderSide: BorderSide.none,
            ),
          ),
          Gap(context.isMobile ? 24 : 26),
          ValueListenableBuilder<bool>(
            valueListenable: isLoading,
            builder: (_, loading, __) {
              if (loading) {
                return const Flexible(child: CountryLoadingListView());
              }

              return ValueListenableBuilder<List<CountryModel>>(
                valueListenable: countriesList,
                builder: (_, countries, __) {
                  return ValueListenableBuilder<bool>(
                    valueListenable: isLoadingMore,
                    builder: (_, loadinMore, __) {
                      if (!loading && !loadinMore && countries.isEmpty) {
                        return const NoData();
                      }
                      return Flexible(
                        child: ListView.separated(
                          shrinkWrap: true,
                          physics:
                              loadinMore ? const NeverScrollableScrollPhysics() : const AlwaysScrollableScrollPhysics(),
                          controller: scrollPaginationController,
                          separatorBuilder: (_, __) => const Gap(24),
                          itemCount: countries.length,
                          itemBuilder: (context, index) {
                            return BlocBuilder<ThemeCubit, ThemeState>(
                              builder: (context, state) {
                                return InkWell(
                                  onTap: () {
                                    FocusScope.of(context).unfocus();
                                    context.pop(countries[index]);
                                  },
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Flexible(
                                        child: Text(
                                          (countries[index].name ?? '').inCaps,
                                          style: context.textTheme.bodyMedium?.copyWith(
                                            fontSize: context.isMobile ? 15 : 18,
                                            fontWeight: FontWeight.w400,
                                            overflow: TextOverflow.ellipsis,
                                            color: state.themeMode == ThemeMode.dark &&
                                                    state.currentUserType == UserType.normal
                                                ? AppColors.headingTextDark
                                                : AppColors.text,
                                          ),
                                        ),
                                      ),
                                      Text(
                                        countries[index].phoneCode != null ? '+${countries[index].phoneCode}' : '',
                                        style: context.textTheme.bodyMedium?.copyWith(
                                          fontSize: context.isMobile ? 15 : 16,
                                          fontWeight: FontWeight.w400,
                                          color: state.themeMode == ThemeMode.dark &&
                                                  state.currentUserType == UserType.normal
                                              ? AppColors.textDark
                                              : AppColors.subText,
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      );
                    },
                  );
                },
              );
            },
          ),
          ValueListenableBuilder<bool>(
            valueListenable: isLoadingMore,
            builder: (_, loading, __) {
              if (loading) {
                return const Padding(
                  padding: EdgeInsets.symmetric(vertical: 24),
                  child: CountryLoadingListView(),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    ]);
  }

  Future<void> load() async {
    isLoading.value = true;

    final failOrSuccess = await getIt<IUserManagerRepository>().getCountriesList(
      search: searchController.text.trim(),
      perPage: 20,
    );

    failOrSuccess.fold((l) {
      isLoading.value = false;
      debugError(l);
      Utility.toast(message: l.message);
    }, (r) {
      isLoading.value = false;
      countriesList.value = [...r.countries];
      hasReachedMax = r.countries.length < 10;
      page = 2;
    });
  }

  Future<void> loadMore() async {
    isLoadingMore.value = true;
    final failOrSuccess = await getIt<IUserManagerRepository>().getCountriesList(
      page: page,
      search: searchController.text.trim(),
    );

    failOrSuccess.fold((l) {
      isLoadingMore.value = false;
    }, (r) {
      isLoadingMore.value = false;
      page += 1;
      hasReachedMax = r.countries.length < 10;
      countriesList.value = [...countriesList.value, ...r.countries];
    });
  }

  @override
  void onReachedLast() {
    if (!isLoading.value && !isLoadingMore.value && !hasReachedMax) {
      EasyDebounce.debounce('COUNTRY_LIST_PAGINATION', const Duration(milliseconds: kDebounceTimeInMiliSec), loadMore);
    }
  }
}

class CountryLoadingListView extends StatelessWidget {
  const CountryLoadingListView({
    super.key,
  });
  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      child: ListView.separated(
        separatorBuilder: (_, __) => const Gap(24),
        itemCount: 5,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'name name',
                style: context.textTheme.bodyMedium?.copyWith(
                  fontSize: 15,
                  fontWeight: FontWeight.w400,
                ),
              ),
              Text(
                '+9191',
                style: context.textTheme.bodyMedium?.copyWith(
                  fontSize: 15,
                  fontWeight: FontWeight.w400,
                  color: AppColors.subText,
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
