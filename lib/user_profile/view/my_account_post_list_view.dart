import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/post_preview/model/post_model.dart';
import 'package:tynt_web/user_profile/widget/post_item_view.dart';
import 'package:tynt_web/user_profile/widget/user_profile_max_width_wapper.dart';

final List<PostModel> postListTemp = [
  PostModel(
    id: 1,
    title: 'This is a title',
    descriptionDelta: 'This is a description' * 8,
  ),
  const PostModel(
    id: 2,
    title: 'This is a title',
    descriptionDelta: 'This is a description',
  ),
  PostModel(
    id: 3,
    title: 'This is a title',
    descriptionDelta: 'This is a description' * 12,
  ),
  const PostModel(
    id: 4,
    title: 'This is a title',
    descriptionDelta: 'This is a description',
  ),
  const PostModel(
    id: 5,
    title: 'This is a title',
    descriptionDelta: 'This is a description',
  ),
  const PostModel(
    id: 6,
    title: 'This is a title',
    descriptionDelta: 'This is a description',
  ),
];

class MyAccountPostListView extends StatelessWidget {
  const MyAccountPostListView({required this.posts, this.isShared = false, this.tomesId, super.key});
  final List<PostModel> posts;
  final bool isShared;
  final String? tomesId;

  @override
  Widget build(BuildContext context) {
    return SliverList.separated(
      itemCount: posts.length,
      separatorBuilder: (_, __) => const UserProfileMaxWidthWapper(
        child: Padding(padding: EdgeInsets.symmetric(horizontal: 16), child: Divider(thickness: 1, height: 0)),
      ),
      itemBuilder: (context, index) {
        final post = posts[index];
        return PostItemView(postModel: post, isShared: isShared, tomesId: tomesId);
      },
    );
  }
}

class MyAccountPostLoadingListView extends StatelessWidget {
  const MyAccountPostLoadingListView({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer.sliver(
      containersColor: AppColors.softPeach,
      child: SliverList.separated(
        itemCount: postListTemp.length,
        separatorBuilder: (_, __) => const UserProfileMaxWidthWapper(
          child: Padding(padding: EdgeInsets.symmetric(horizontal: 16), child: Divider(thickness: 1, height: 0)),
        ),
        itemBuilder: (context, index) {
          final post = postListTemp[index];
          return PostItemView(postModel: post);
        },
      ),
    );
  }
}
