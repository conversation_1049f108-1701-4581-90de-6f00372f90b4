import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';

class CreateTomesWidget extends StatelessWidget {
  const CreateTomesWidget({super.key, this.onTap});
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          boxShadow: [
            BoxShadow(
              color: AppColors.black.withOpacity(0.15),
              blurRadius: 6.83,
              offset: const Offset(0, 1.95),
            ),
          ],
        ),
        child: DottedBorder(
          options: const RectDottedBorderOptions(
            color: AppColors.primary,
            dashPattern: [2, 2],
            borderPadding: EdgeInsets.all(2),
            strokeWidth: 2,
          ),
          child: Container(
            height: 118,
            width: 68,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const AppImageAsset(AppAssets.plusIcon, height: 14, width: 14),
                const Gap(8),
                Text(
                  'Create',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.primary,
                      ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
