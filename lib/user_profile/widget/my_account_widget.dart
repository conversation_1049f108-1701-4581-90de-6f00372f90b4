// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:tynt_web/auth/models/user_model.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/colors_extnetions.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/extentions/string_extentions.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/master/widget/authenticated_user_avatar.dart';
import 'package:tynt_web/routes/app_routes.dart';
import 'package:tynt_web/user_profile/widget/my_account_user_card_subdetail_widget.dart';
import 'package:tynt_web/user_profile/widget/user_profile_max_width_wapper.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';

class MyAccountUserDetailWidget extends StatelessWidget {
  const MyAccountUserDetailWidget({
    required this.user,
    this.isPseudoProfile = false,
    super.key,
  });
  final bool isPseudoProfile;
  final UserModel user;

  @override
  Widget build(BuildContext context) {
    return UserProfileMaxWidthWapper(
      child: Container(
        margin: const EdgeInsets.only(top: 20, left: 16, right: 16, bottom: 20),
        padding: const EdgeInsets.only(top: 13, bottom: 16, left: 16, right: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Theme.of(context).cardColor,
          boxShadow: [
            BoxShadow(
              blurRadius: 14,
              offset: const Offset(0, 4),
              color: AppColors.black.withOpacity2(0.15),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.black.withOpacity2(0.25),
                        blurRadius: 4,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: AuthenticatedUserAvatar(
                    radius: 32.5,
                    newImageUrl: user.formmtedAvatarProfile,
                    usePseudoUserProfile: isPseudoProfile,
                  ),
                ),
                const Gap(20),
                MyAccountUserCardSubdetailWidget(title: context.l10n.posts, value: user.formettedPostCount),
                const Gap(30),
                if (!isPseudoProfile) ...[
                  MyAccountUserCardSubdetailWidget(
                    title: context.l10n.following,
                    value: user.formettedFollowingCount,
                    onPressed: () {},
                  ),
                  const Gap(30),
                ],
                MyAccountUserCardSubdetailWidget(
                  title: context.l10n.followers,
                  value: user.formettedFollowersCount,
                  onPressed: () {},
                ),
                if (isPseudoProfile) ...[
                  const Gap(6),
                  const AppImageAsset(AppAssets.ghostSmallIcon),
                ],
              ],
            ),
            const Gap(10),
            Row(
              children: [
                Text(
                  (user.fullName ?? 'Pseudo profile').capitalizeFirstofEach,
                  style: context.textTheme.headlineMedium?.copyWith(
                    fontSize: 18,
                    letterSpacing: 0.5,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const Gap(16),
                IconButton(
                  constraints: const BoxConstraints(),
                  padding: EdgeInsets.zero,
                  splashRadius: 20,
                  onPressed: () {
                    context.pushNamed(AppRoutes.editProfile.name);
                  },
                  icon: AppImageAsset(
                    AppAssets.penIcon,
                    height: context.isMobile ? 18 : 22,
                    width: context.isMobile ? 18 : 22,
                    color: Theme.of(context).textTheme.headlineMedium!.color,
                  ),
                ),
              ],
            ),
            if (user.bio != null && user.bio!.isNotEmpty) ...[
              const Gap(6),
              Text(
                user.bio ?? '',
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
                style: context.textTheme.labelLarge?.copyWith(
                  fontSize: 13,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
