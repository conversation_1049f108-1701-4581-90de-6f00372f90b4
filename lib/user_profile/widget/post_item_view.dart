import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/l10n/l10n_extension.dart';
import 'package:tynt_web/post_preview/model/post_model.dart';
import 'package:tynt_web/post_preview/model/post_type_model.dart';
import 'package:tynt_web/post_preview/widget/post_description_view.dart';
import 'package:tynt_web/post_preview/widget/post_geners_item_view.dart';
import 'package:tynt_web/post_preview/widget/post_header_view.dart';
import 'package:tynt_web/post_preview/widget/post_tag_item_view.dart';
import 'package:tynt_web/post_preview/widget/post_title_view.dart';
import 'package:tynt_web/routes/app_routes.dart';
import 'package:tynt_web/user_profile/widget/user_profile_max_width_wapper.dart';
import 'package:tynt_web/utlity/extentions/string_extentions.dart';
import 'package:tynt_web/widgets/post_image_view.dart';

class PostItemView extends StatelessWidget {
  const PostItemView({
    required this.postModel,
    this.isFromOtherUserPage = false,
    this.isFromPostTypePage = false,
    this.isFromGenrePostPage = false,
    this.isFromCommunityPostPage = false,
    super.key,
    this.onNominatedTap,
    this.onSaveTap,
    this.backgroundColor,
    this.isShared = false,
    this.tomesId,
  });

  final bool isFromOtherUserPage;
  final PostModel postModel;
  final bool isFromPostTypePage;
  final bool isFromGenrePostPage;
  final bool isFromCommunityPostPage;
  final ValueChanged<int>? onNominatedTap;
  final ValueChanged<int>? onSaveTap;
  final Color? backgroundColor;
  final bool isShared;
  final String? tomesId;

  @override
  Widget build(BuildContext context) {
    return UserProfileMaxWidthWapper(
      child: InkWell(
        onTap: () {
          if (isShared) {
            log('${postModel.id}shared');
            context.pushNamed(
              AppRoutes.sharePostDetail.name,
              pathParameters: {
                'id': tomesId.toString(),
                'postid': postModel.id.toString(),
              },
            );
            return;
          }
          context.goNamed(
            AppRoutes.postDetail.name,
            pathParameters: {
              'id': postModel.id.toString(),
            },
          );
        },
        overlayColor: const WidgetStatePropertyAll(AppColors.transparent),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 22),
          decoration: BoxDecoration(
            // borderRadius: BorderRadius.circular(12),
            color: backgroundColor ?? Colors.transparent,
            // boxShadow: [AppColors.postItemShadow],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // if (postModel.community != null) ...[
              //   PostCommunityView(
              //     isFromCommunityPostPage: isFromCommunityPostPage,
              //     text: postModel.community?.formmtedName(context) ?? '',
              //     communityId: postModel.community?.id ?? 0,
              //   ),
              //   const Gap(2),
              // ],
              if (postModel.title?.isPureValid ?? false) ...[
                // Gap(context.isMobile ? 8 : 10),
                PostTitleView(
                  isTyntYPost: postModel.isTyntYPost,
                  fontSize: 26,
                  text: postModel.title ?? '',
                ),
              ],
              const Gap(10),
              Row(
                children: [
                  Expanded(
                    child: Skeleton.unite(
                      child: PostHeaderView(
                        title: postModel.postType?.name ?? '',
                        date: postModel.formmtedCreatedAt,
                        onPostTypeTap: () {},
                      ),
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      if (postModel.isDraft == 1)
                        Skeleton.leaf(
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
                            decoration: const BoxDecoration(
                              color: AppColors.background,
                              borderRadius: BorderRadius.all(Radius.circular(6)),
                            ),
                            child: Text(
                              context.l10n.draft,
                              style: context.textTheme.bodyMedium?.copyWith(
                                fontSize: 12,
                                color: AppColors.red,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),
                        ),
                      if (postModel.genre != null) ...[
                        const Gap(8),
                        PostGenersItemView(
                          text: postModel.genre?.name ?? '',
                          backgroundColor: AppColors.transparent,
                          onPressed: () {},
                        ),
                      ],
                    ],
                  ),
                ],
              ),
              if (postModel.imageUrl != null) ...[
                const Gap(10),
                PostImageView(
                  imageUrl: postModel.imageUrl,
                  width: MediaQuery.sizeOf(context).width,
                  boxfit: BoxFit.fitWidth,
                ),
                const Gap(10),
              ],
              const Gap(10),
              PostDescriptionView(
                text: postModel.description ?? '',
                isDescriptionTooLong: postModel.isDescriptionTooLong,
                postType: postModel.postType ?? const PostTypeModel(id: 0),
              ),
              if (postModel.tags.isNotEmpty) ...[
                const Gap(10),
                Wrap(
                  runSpacing: 8,
                  spacing: 8,
                  children: List.generate(
                    postModel.tags.length,
                    (index) => PostTagItemView(
                      text: '#${postModel.tags[index].name}',
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
              // const Gap(16),
              // Row(
              //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //   children: [
              //     Flexible(
              //       child: UserListTile(
              //         nameMaxLine: 1,
              //         isBusinessUser: postModel.user?.isBusinessUser ?? false,
              //         name: postModel.user?.formmtedUserName ?? '',
              //         onProfileTap: () {},
              //         iconRadius: 11,
              //         url: postModel.user?.formmtedAvatarProfile ?? '',
              //         isGhostUser: postModel.user?.isPseudoUser ?? false,
              //         fontSize: 14,
              //         showFollowButton: false,
              //       ),
              //     ),
              //     const Gap(20),
              //     Row(
              //       mainAxisSize: MainAxisSize.min,
              //       children: [
              //         if (postModel.showComments) ...[
              //           // PostCommentIconButton(postId: postModel.id),
              //           const Gap(18),
              //         ],
              //         TextButton.icon(
              //           onPressed: () {},
              //           icon: AppImageAsset(
              //             AppAssets.forwardIcon,
              //             color: AppColors.text2,
              //             height: 20,
              //           ),
              //           label: Text(
              //             'share',
              //             style: context.textTheme.bodyMedium?.copyWith(
              //               fontSize: 14 ,
              //               color: AppColors.text2,
              //             ),
              //           ),
              //         ),
              //       ],
              //     ),
              //   ],
              // ),
            ],
          ),
        ),
      ),
    );
  }
}
