// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';
import 'package:tynt_web/user_profile/widget/user_circle_avatar.dart';
import 'package:tynt_web/widgets/app_image_asset.dart';

class UserListTile extends StatelessWidget {
  const UserListTile({
    required this.name,
    required this.url,
    super.key,
    this.iconRadius = 23,
    this.isFollowButton = true,
    this.onFollowButtonPressed,
    this.onProfileTap,
    this.buttonText,
    this.buttonTextColor,
    this.buttonColor,
    this.showFollowButton = true,
    this.isGhostUser = false,
    this.isBusinessUser = false,
    this.fontSize = 15,
    this.fontWeight,
    this.ghostIconSize = 18,
    this.nameMaxLine = 2,
    this.userName,
    this.userNameColor,
  });
  final String name;
  final String url;
  final double iconRadius;
  final bool isFollowButton;
  final VoidCallback? onFollowButtonPressed;
  final VoidCallback? onProfileTap;
  final String? buttonText;
  final Color? buttonTextColor;
  final Color? buttonColor;
  final bool showFollowButton;
  final bool isGhostUser;
  final bool isBusinessUser;
  final double fontSize;
  final FontWeight? fontWeight;
  final double ghostIconSize;
  final int nameMaxLine;
  final String? userName;
  final Color? userNameColor;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onProfileTap,
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: Row(
        children: [
          Flexible(
            flex: 2,
            child: Row(
              children: [
                UserCircleAvatar(radius: iconRadius, imageUrl: url, isBusinessUser: isBusinessUser),
                const Gap(8),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Flexible(
                            child: Text(
                              name,
                              maxLines: nameMaxLine,
                              overflow: TextOverflow.ellipsis,
                              style: context.textTheme.bodyMedium?.copyWith(
                                fontSize: fontSize,
                                fontWeight: fontWeight,
                              ),
                            ),
                          ),
                          if (isGhostUser) ...[
                            const Gap(6),
                            AppImageAsset(
                              AppAssets.ghostSmallIcon,
                              height: ghostIconSize,
                              width: ghostIconSize,
                            ),
                          ],
                        ],
                      ),
                      Text(
                        '@$userName',
                        style: context.textTheme.labelLarge?.copyWith(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
                const Gap(3),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
