import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';

class SelectGenresWidget extends StatelessWidget {
  const SelectGenresWidget({super.key, this.text, this.onPressed, this.isSelecetd = false});

  final bool isSelecetd;
  final String? text;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return Skeleton.shade(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(6),
        child: ChoiceChip(
          selected: isSelecetd,
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          selectedColor: Theme.of(context).brightness == Brightness.dark ? AppColors.white : AppColors.black,
          labelStyle: context.textTheme.bodyMedium?.copyWith(
            fontSize: context.isMobile ? 15 : 20,
            color: isSelecetd ? AppColors.white : AppColors.text,
          ),
          labelPadding: EdgeInsets.zero,
          shadowColor: AppColors.transparent,
          selectedShadowColor: AppColors.transparent,
          backgroundColor:
              Theme.of(context).brightness == Brightness.dark ? AppColors.textfieldFillColorDark : AppColors.background,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
          onSelected: (value) => onPressed?.call(),
          label: Text(
            text ?? '',
            style: context.textTheme.bodyMedium?.copyWith(
              fontSize: 15,
              color: (Theme.of(context).brightness == Brightness.dark && isSelecetd)
                  ? AppColors.text
                  : (Theme.of(context).brightness == Brightness.light && isSelecetd)
                      ? AppColors.white
                      : (!isSelecetd && Theme.of(context).brightness == Brightness.light)
                          ? AppColors.text
                          : AppColors.white,
            ),
          ),
        ),
      ),
    );
  }
}
