// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:tynt_web/constants/app_assets.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/widgets/app_image_network.dart';

class UserCircleAvatar extends StatelessWidget {
  const UserCircleAvatar({
    super.key,
    this.radius = 30,
    this.imageUrl,
    this.isBusinessUser = false,
  });
  final double radius;
  final bool isBusinessUser;
  final String? imageUrl;

  @override
  Widget build(BuildContext context) {
    return AppImageNetwork(
      url: imageUrl ?? '',
      height: radius * 2,
      width: radius * 2,
      errorAssetIcon: isBusinessUser ? AppAssets.businessUserDefaultIcon : null,
      imageBuilder: (context, imageProvider) => CircleAvatar(
        backgroundImage: imageProvider,
        radius: radius * 2,
        backgroundColor: AppColors.white,
      ),
    );
  }
}
