// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tynt_web/constants/app_colors.dart';
import 'package:tynt_web/extentions/context_extentions.dart';

class MyAccountUserCardSubdetailWidget extends StatelessWidget {
  const MyAccountUserCardSubdetailWidget({
    required this.value,
    required this.title,
    super.key,
    this.onPressed,
  });
  final String value;
  final String title;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            value,
            style: context.textTheme.headlineMedium?.copyWith(
              fontSize: 16,
              fontWeight: FontWeight.w700,
            ),
          ),
          const Gap(3),
          Text(
            title,
            style: context.textTheme.headlineMedium?.copyWith(
              fontSize: 12,
              letterSpacing: 0,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}
