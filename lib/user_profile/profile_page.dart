import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tynt_web/auth/bloc/authentication_bloc.dart';
import 'package:tynt_web/injector/injector.dart';
import 'package:tynt_web/splash/splash_page.dart';
import 'package:tynt_web/user_profile/bloc/my_account_bloc.dart';
import 'package:tynt_web/user_profile/user_profile_page.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<AuthenticationBloc, AuthenticationState, bool>(
      selector: (state) => state.isAuthLoading,
      builder: (context, isAuthLoading) {
        if (isAuthLoading) {
          return const SplashPage();
        }
        return BlocProvider(
          create: (context) => getIt<MyAccountBloc>()..add(const MyAccountLoad()),
          child: const UserProfilePage(),
        );
      },
    );
  }
}
