// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:cloud_firestore/cloud_firestore.dart' as _i974;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:shared_preferences/shared_preferences.dart' as _i460;
import 'package:tynt_web/app/cubit/refresh_cubit.dart' as _i83;
import 'package:tynt_web/auth/bloc/authentication_bloc.dart' as _i227;
import 'package:tynt_web/auth/cubit/theme_cubit.dart' as _i245;
import 'package:tynt_web/auth/repository/i_auth_repository.dart' as _i995;
import 'package:tynt_web/create_post/cubit/create_edit_post_cubit.dart'
    as _i614;
import 'package:tynt_web/injector/firebase_injectable_module.dart' as _i665;
import 'package:tynt_web/injector/shared_preference_injectable_module.dart'
    as _i431;
import 'package:tynt_web/local_storage/repository/i_local_storage_repository.dart'
    as _i195;
import 'package:tynt_web/no_internet/cubit/internet_connectivity_cubit.dart'
    as _i531;
import 'package:tynt_web/post_preview/repository/i_post_repository.dart'
    as _i864;
import 'package:tynt_web/tomes/bloc/tomes_details_bloc.dart' as _i623;
import 'package:tynt_web/tomes/repository/i_tomes_repository.dart' as _i176;
import 'package:tynt_web/user/repository/i_user_manager_repository.dart'
    as _i120;
import 'package:tynt_web/user_profile/bloc/my_account_bloc.dart' as _i23;
import 'package:tynt_web/utlity/network/client.dart' as _i12;
import 'package:tynt_web/utlity/network/http_client.dart' as _i808;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i174.GetIt> init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final injectableModule = _$InjectableModule();
    final firebaseInjectableModule = _$FirebaseInjectableModule();
    gh.factory<_i83.RefreshCubit>(() => _i83.RefreshCubit());
    await gh.factoryAsync<_i460.SharedPreferences>(
      () => injectableModule.prefs,
      preResolve: true,
    );
    gh.lazySingleton<_i531.InternetConnectivityCubit>(
        () => _i531.InternetConnectivityCubit());
    gh.lazySingleton<_i974.FirebaseFirestore>(
        () => firebaseInjectableModule.firestore);
    gh.factory<_i195.ILocalStorageRepository>(
        () => _i195.LocalStorageRepository(gh<_i460.SharedPreferences>()));
    gh.factory<_i12.Client>(
        () => _i808.HttpClient(gh<_i195.ILocalStorageRepository>()));
    gh.lazySingleton<_i245.ThemeCubit>(() => _i245.ThemeCubit(
        localStorageRepository: gh<_i195.ILocalStorageRepository>()));
    gh.factory<_i120.IUserManagerRepository>(
        () => _i120.UserManagerRepository(gh<_i12.Client>()));
    gh.factory<_i176.ITomesRepository>(
        () => _i176.TomesRepository(gh<_i12.Client>()));
    gh.factory<_i864.IPostRepository>(
        () => _i864.PostRepository(gh<_i12.Client>()));
    gh.factory<_i995.IAuthRepository>(
        () => _i995.AuthRepository(gh<_i12.Client>()));
    gh.factory<_i623.TomesDetailsBloc>(() =>
        _i623.TomesDetailsBloc(tomesRepository: gh<_i176.ITomesRepository>()));
    gh.factory<_i23.MyAccountBloc>(
        () => _i23.MyAccountBloc(postRepository: gh<_i864.IPostRepository>()));
    gh.factoryParam<_i614.CreateEditPostCubit, _i614.CreateEditPostParams,
        dynamic>((
      params,
      _,
    ) =>
        _i614.CreateEditPostCubit(
          postRepository: gh<_i864.IPostRepository>(),
          params: params,
        ));
    gh.lazySingleton<_i227.AuthenticationBloc>(() => _i227.AuthenticationBloc(
          authRepository: gh<_i995.IAuthRepository>(),
          userManagerRepository: gh<_i120.IUserManagerRepository>(),
          localStorageRepository: gh<_i195.ILocalStorageRepository>(),
        ));
    return this;
  }
}

class _$InjectableModule extends _i431.InjectableModule {}

class _$FirebaseInjectableModule extends _i665.FirebaseInjectableModule {}
