part of 'i_local_storage_repository.dart';

@Injectable(as: ILocalStorageRepository)
class LocalStorageRepository extends ILocalStorageRepository {
  LocalStorageRepository(super.preferences);

  @override
  String get getLanguage => preferences.getString(AppStrings.appLanguage) ?? AppStrings.defaultAppLanguage;

  @override
  Future<void> setLanguage(String value) async {
    try {
      await preferences.setString(AppStrings.appLanguage, value);
    } on Exception catch (e) {
      debugError('Language not saved Error : $e');
    }
  }

  @override
  String? get getUserToken => preferences.getString(AppStrings.userToken);

  @override
  Future<void> setUserToken(String? value) async {
    if (value == null) {
      return;
    }
    try {
      await preferences.setString(AppStrings.userToken, value);
    } on Exception catch (e) {
      debugError('Token not saved Error : $e');
    }
  }

  @override
  Future<void> removeAuth() async {
    try {
      await Future.wait([
        preferences.remove(AppStrings.userToken),
        preferences.remove(AppStrings.tyntUserToken),
      ]);
    } on Exception catch (e) {
      debugError('Token not removed Error : $e');
    }
  }

  @override
  String? get getTyntUserToken => preferences.getString(AppStrings.tyntUserToken);

  @override
  Future<void> setTyntUserToken(String? value) async {
    if (value == null) {
      throw Exception('Tynt User Token is null');
    }
    try {
      await preferences.setString(AppStrings.tyntUserToken, value);
    } on Exception catch (e) {
      debugError('Tynt User Token not saved Error : $e');
    }
  }

  @override
  Future<void> removeTyntUserToken() async {
    try {
      if (getTyntUserToken != null) {
        await preferences.remove(AppStrings.tyntUserToken);
      }
    } on Exception catch (e) {
      debugError('Token not removed Error : $e');
    }
  }

  @override
  bool get getOnBoarding => preferences.getBool(AppStrings.appOnBoarding) ?? false;

  @override
  Future<void> setOnBoarding() async {
    try {
      await preferences.setBool(AppStrings.appOnBoarding, true);
    } on Exception catch (e) {
      debugError('OnBoarding not saved Error : $e');
    }
  }

  @override
  Future<void> setTheme(String value) async {
    try {
      await preferences.setString(AppStrings.appTheme, value);
    } on Exception catch (e) {
      debugError('Theme not saved Error : $e');
    }
  }

  @override
  String get getTheme => preferences.getString(AppStrings.appTheme) ?? ThemeMode.light.name;
}
