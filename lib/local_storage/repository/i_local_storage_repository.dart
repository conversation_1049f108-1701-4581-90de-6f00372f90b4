import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tynt_web/constants/app_strings.dart';
import 'package:tynt_web/utlity/logger_config.dart';

part 'local_storage_repository.dart';

abstract class ILocalStorageRepository {
  ILocalStorageRepository(this.preferences);

  final SharedPreferences preferences;

  Future<void> setUserToken(String? value);

  String? get getUserToken;

  Future<void> setTyntUserToken(String? value);

  String? get getTyntUserToken;

  Future<void> removeTyntUserToken();

  Future<void> setLanguage(String value);

  String get getLanguage;

  Future<void> removeAuth();

  Future<void> setOnBoarding();

  bool get getOnBoarding;

  Future<void> setTheme(String value);

  String get getTheme;
}
